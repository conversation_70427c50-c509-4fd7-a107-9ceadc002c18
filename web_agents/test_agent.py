from .env import MINIWOB_URL, OPENAI_API_KEY, AGENTLAB_EXP_ROOT
import os
os.environ['MINIWOB_URL'] = MINIWOB_URL
os.environ['OPENAI_API_KEY'] = OPENAI_API_KEY
os.environ['AGENTLAB_EXP_ROOT'] = AGENTLAB_EXP_ROOT

from agentlab.agents.most_basic_agent.most_basic_agent import experiment_config, MostBasicAgentArgs
from agentlab.llm.llm_configs import CHAT_MODEL_ARGS_DICT

benchmark = "miniwob_tiny_test"
chat_model_args = CHAT_MODEL_ARGS_DICT["openai/gpt-4o-mini-2024-07-18"]
agent_args = MostBasicAgentArgs(
            temperature=0.1,
            use_chain_of_thought=True,
            chat_model_args=chat_model_args,
        )
study = study_generators.run_agents_on_benchmark(agent_args, benchmark)
study.run(n_jobs=n_jobs, parallel_backend="joblib", strict_reproducibility=reproducibility_mode)