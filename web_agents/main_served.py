"""
Note: This script is a convenience script to launch experiments instead of using
the command line.

Copy this script and modify at will, but don't push your changes to the
repository.
"""
import os
#os.environ["MINIWOB_URL"] = "file:///home/<USER>/m/megh.thakkar/language_grounding/web_agents/miniwob-plusplus/miniwob/html/miniwob/"
print('scratch', os.environ['SCRATCH'])
os.environ["TRANSFORMERS_CACHE"] = os.environ['SCRATCH'] + "/huggingface/models/"
os.environ["AGENTLAB_EXP_ROOT"] = os.environ['SCRATCH'] + "/agentlab_results/"
os.environ["OPENROUTER_API_KEY"] = 'test' 
import logging
from dataclasses import dataclass
from argparse import ArgumentParser
from typing import Optional
from openai import OpenAI
from collections import defaultdict

from agentlab.agents.generic_agent.agent_configs import GenericAgentArgs, FLAGS_8B, CHAT_MODEL_ARGS_DICT
from agentlab.experiments.study import Study
from agentlab.llm.chat_api import ChatModel, OpenRouterModelArgs
from agentlab.llm.base_api import BaseModelArgs
from browsergym.experiments.benchmark import Benchmark
from browsergym.experiments.benchmark.configs import DEFAULT_HIGHLEVEL_ACTION_SET_ARGS, DEFAULT_BENCHMARKS
import numpy as np

from browsergym.experiments.benchmark.metadata.utils import task_list_from_metadata, task_metadata
from browsergym.experiments.benchmark.utils import make_env_args_list_from_repeat_tasks

logging.getLogger().setLevel(logging.INFO)
from mymodel import MyChatModel, MyModelArgs
# MODEL_NAME = 'google/gemma-2-9b-it'
# MODEL_NAME = 'meta-llama/Llama-3.1-8B-Instruct'
if __name__ == "__main__":  # necessary for dask backend
    parser = ArgumentParser()
    parser.add_argument('--model', type=str,)
    parser.add_argument('--port', type=int, default=8002)
    parser.add_argument('--host', type=str, default='localhost')
    args = parser.parse_args()
    
    MODEL_NAME = args.model


    assert 'MINIWOB_URL' in os.environ and os.environ['MINIWOB_URL'].startswith('file://')
    # choose your agent or provide a new agent
    AGENT = GenericAgentArgs(
        chat_model_args=MyModelArgs(
            model_name=MODEL_NAME,
            max_total_tokens=128_000,
            max_input_tokens=128_000,
            max_new_tokens=1024,
            url=args.host, port=args.port
        ),
        # chat_model_args=CHAT_MODEL_ARGS_DICT["openrouter/meta-llama/llama-3.1-8b-instruct"],
        flags=FLAGS_8B,
    )
    agent_args = [AGENT]

    benchmark = Benchmark(
        name="miniwob-full-n-repeat-2",
        high_level_action_set_args=DEFAULT_HIGHLEVEL_ACTION_SET_ARGS["miniwob_all"],
        is_multi_tab=False,
        supports_parallel_seeds=True,
        backends=["miniwob"],
        env_args_list=make_env_args_list_from_repeat_tasks(
            task_list=task_list_from_metadata(metadata=task_metadata("miniwob")),
            max_steps=5,
            n_repeats=3,
            seeds_rng=np.random.RandomState(42),
        ),
        task_metadata=task_metadata("miniwob"),
    )
    reproducibility_mode = False

    relaunch = False

    n_jobs = 5  # to use all available cores

    if reproducibility_mode:
        [a.set_reproducibility_mode() for a in agent_args]

    if relaunch:
        #  relaunch an existing study
        study = Study.load_most_recent(contains=None)
        study.find_incomplete(include_errors=True)

    else:
        study = Study(agent_args, benchmark, logging_level_stdout=logging.INFO)

    study.run(
        n_jobs=n_jobs,
        parallel_backend="joblib",
        strict_reproducibility=reproducibility_mode,
        n_relaunch=1,
    )

    if reproducibility_mode:
        study.append_to_journal(strict_reproducibility=True)
