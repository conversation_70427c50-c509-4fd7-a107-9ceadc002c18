agentlab==0.3.2
aiofiles==23.2.1
aiohappyeyeballs==2.4.4
aiohttp==3.11.11
aiohttp-cors==0.7.0
aiolimiter==1.2.1
aiosignal==1.3.2
annotated-types==0.7.0
anyio==4.8.0
appdirs==1.4.4
asttokens==3.0.0
async-timeout==5.0.1
attrs==24.3.0
beartype==0.12.0
beautifulsoup4==4.12.3
bitsandbytes==0.43.1
black==24.10.0
blacken-docs==1.19.1
blinker==1.9.0
browsergym==0.13.3
browsergym-assistantbench==0.13.3
browsergym-core==0.13.3
browsergym-experiments==0.13.3
browsergym-miniwob==0.13.3
browsergym-visualwebarena==0.13.3
browsergym-webarena==0.13.3
browsergym-workarena==0.4.1
cachetools==5.5.0
certifi==2024.12.14
cfgv==3.4.0
charset-normalizer==3.4.1
click==8.1.8
cloudpickle==3.1.1
colorama==0.4.6
coloredlogs==15.0.1
colorful==0.5.6
compressed-tensors==0.6.0
contexttimer==0.3.3
contourpy==1.3.1
cycler==0.12.1
dask==2025.1.0
dataclasses-json==0.6.5
datasets==2.19.0
decorator==5.1.1
dill==0.3.8
diskcache==5.6.3
distlib==0.3.8
distrax==0.1.5
distributed==2025.1.0
distro==1.9.0
docker-pycreds==0.4.0
einops==0.8.0
english-words==2.0.1
evaluate==0.4.3
exceptiongroup==1.2.2
execnet==2.1.1
executing==2.1.0
Faker==33.3.1
Farama-Notifications==0.0.4
fastapi==0.115.6
ffmpy==0.5.0
filelock==3.16.1
Flask==3.1.0
fonttools==4.55.3
frozenlist==1.5.0
fsspec==2024.3.1
gguf==0.10.0
gitdb==4.0.11
GitPython==3.1.43
google-api-core==2.18.0
google-auth==2.37.0
googleapis-common-protos==1.63.0
gradio==5.12.0
gradio_client==1.5.4
greenlet==3.1.1
grpcio==1.69.0
gymnasium==1.0.0
h11==0.14.0
hjson==3.1.0
httpcore==1.0.7
httptools==0.6.4
httpx==0.28.1
huggingface-hub==0.27.1
humanfriendly==10.0
identify==2.6.6
idna==3.10
imageio==2.37.0
importlib_metadata==8.6.1
iniconfig==2.0.0
interegular==0.3.3
ipython==8.31.0
isort==5.13.2
itsdangerous==2.2.0
jedi==0.19.2
Jinja2==3.1.5
jiter==0.8.2
jmp==0.0.4
joblib==1.4.2
jsonlines==4.0.0
jsonpatch==1.33
jsonpointer==2.4
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
kiwisolver==1.4.8
langchain==0.1.14
langchain-community==0.0.34
langchain-core==0.1.46
langchain-text-splitters==0.0.1
langsmith==0.1.52
lark==1.2.2
lazy_loader==0.4
libvisualwebarena==0.0.15
libwebarena==0.0.4
lightning-utilities==0.11.2
linkify-it-py==2.0.3
llvmlite==0.44.0
lm-format-enforcer==0.10.6
locket==1.0.0
loralib==0.1.2
lxml==5.3.0
markdown-it-py==3.0.0
MarkupSafe==2.1.5
marshmallow==3.21.1
matplotlib==3.10.0
matplotlib-inline==0.1.7
mdit-py-plugins==0.4.2
mdurl==0.1.2
memray==1.15.0
mistral_common==1.5.1
mpi4py==3.1.4
mpmath==1.3.0
msgpack==1.1.0
msgspec==0.19.0
multidict==6.1.0
multiprocess==0.70.16
mypy-extensions==1.0.0
nest-asyncio==1.6.0
networkx==3.4.2
nltk==3.9.1
nodeenv==1.9.1
numba==0.61.0
numpy==1.26.4
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.1.105
nvidia-cuda-nvrtc-cu12==12.1.105
nvidia-cuda-runtime-cu12==12.1.105
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==*********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==**********
nvidia-cusparse-cu12==**********
nvidia-ml-py==12.560.30
nvidia-nccl-cu12==2.20.5
nvidia-nvjitlink-cu12==12.6.85
nvidia-nvtx-cu12==12.1.105
openai==1.59.9
opencensus==0.11.4
opencensus-context==0.1.3
opencv-python-headless==*********
optimum==1.19.1
orjson==3.10.15
outlines==0.0.46
packaging==23.2
pandas==2.2.3
parso==0.8.4
partd==1.4.2
partial-json-parser==*******.post5
pathspec==0.12.1
peft==0.10.0
pexpect==4.9.0
pillow==10.4.0
platformdirs==4.3.6
playwright==1.49.1
pluggy==1.5.0
portalocker==3.1.1
pre_commit==4.1.0
prometheus-fastapi-instrumentator==7.0.2
prometheus_client==0.21.1
prompt_toolkit==3.0.50
propcache==0.2.1
proto-plus==1.23.0
protobuf==4.25.5
psutil==6.1.1
ptyprocess==0.7.0
pure_eval==0.2.3
py-cpuinfo==9.0.0
py-spy==0.3.14
pyairports==2.1.1
pyarrow==19.0.0
pyarrow-hotfix==0.6
pyasn1==0.6.1
pyasn1_modules==0.4.1
pycountry==24.6.1
pydantic==2.10.5
pydantic_core==2.27.2
pydub==0.25.1
pyee==12.0.0
Pygments==2.19.1
pyparsing==3.2.1
pytest==7.3.2
pytest-base-url==2.1.0
pytest-playwright==0.6.2
pytest-xdist==3.6.1
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-multipart==0.0.20
python-slugify==8.0.4
pytz==2024.2
PyYAML==6.0.2
pyzmq==26.2.0
ray==2.40.0
referencing==0.36.1
regex==2024.11.6
requests==2.32.3
rich==13.9.4
rpds-py==0.22.3
rsa==4.9
ruff==0.9.2
sacrebleu==2.5.1
safehttpx==0.1.6
safetensors==0.5.2
scikit-image==0.25.0
scipy==1.15.1
semantic-version==2.10.0
sentencepiece==0.2.0
sentry-sdk==2.0.1
setproctitle==1.3.3
shellingham==1.5.4
six==1.17.0
smart-open==7.1.0
smmap==5.0.1
sniffio==1.3.1
sortedcontainers==2.4.0
soupsieve==2.6
SQLAlchemy==2.0.29
stack-data==0.6.3
starlette==0.41.3
sympy==1.13.3
tabulate==0.9.0
tblib==3.0.0
tenacity==8.2.3
tensorflow-probability==0.24.0
text-generation==0.7.0
text-unidecode==1.3
textual==1.0.0
tifffile==2025.1.10
tiktoken==0.7.0
tokenize_rt==6.1.0
tokenizers==0.21.0
tomli==2.2.1
tomlkit==0.13.2
toolz==1.0.0
torch==2.4.0
torchmetrics==1.3.2
torchvision==0.19.0
tornado==6.4.2
tqdm==4.67.1
traitlets==5.14.3
transformers==4.48.1
transformers-stream-generator==0.0.5
triton==3.0.0
typer==0.15.1
types-requests==2.32.0.20241016
types-tqdm==4.67.0.20241221
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2024.2
uc-micro-py==1.0.3
urllib3==2.3.0
uvicorn==0.34.0
uvloop==0.21.0
virtualenv==20.26.1
vllm==0.6.3.post1
watchfiles==1.0.4
wcwidth==0.2.13
weblinx==0.3.2
weblinx-browsergym==0.0.1.dev14
websockets==14.2
Werkzeug==3.1.3
wrapt==1.17.2
xformers==0.0.27.post2
xxhash==3.4.1
yarl==1.18.3
zict==3.0.0
zipp==3.21.0
