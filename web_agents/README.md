## Run agents on miniwob

### Download MiniWoB

1. Clone the repo:
```bash
<NAME_EMAIL>:Farama-Foundation/miniwob-plusplus.git
git -C "./miniwob-plusplus" reset --hard 7fd85d71a4b60325c6585396ec4f48377d049838
```
2. Use the path of the repo in the `<PATH_TO_MINIWOB_CLONED_REPO>` in setting up the environment variables (next step).

Creating conda environment:

```
conda create -n web python=3.12
conda activate web
pip install agentlab==0.4.0 playwright==1.39.0 vllm==0.6.3.post1
```
### Setting up <PERSON><PERSON><PERSON> and <PERSON>rowserGym

Create a new conda env with python=3.11. Activate your environment.

1. Run:
```bash
pip install agentlab
```
2. Run:
```bash
playwright install
```
3. run `export MINIWOB_URL="file://$(pwd)/custom_miniwob_html/"`
4. Run `pip install vllm==0.6.3.post1`
5. Run `python env_loop.py`

`unzip -o libs.zip | grep so | awk '{print $2}' | xargs chmod 755`

### Running local benchmaarks

2. Run `salloc --partition=short-unkillable --time=0-03:00 -c 24 --mem=128G --gres=gpu:4 --constraint=ampere`
3. in another tmux tab run `srun --jobid=<JOBID> --pty bash`
4. serve vllm: `vllm serve meta-llama/Llama-3.1-70B-Instruct --tensor-parallel-size 4 --download-dir $SCRATCH/huggingface/models/ --dtype auto --port 8002`
You need 160gb to run 70b model. so use either 2 gpus with 80gb or 4 gpus with 40gb (`--tensor-parallel-size` is responsible for that)
5. Run `python main_served.py --port 8002 --model meta-llama/Llama-3.1-70B-Instruct` in the first tmux tab


## serving via sbatch

an example for the model `Qwen/Qwen2.5-3B-Instruct`. Scripts automatically download stuf into your scratch.


launch server:
```sh
cd serve && sbatch ./serve.sh Qwen/Qwen2.5-3B-Instruct && cd ..
```
launch client:
```sh
salloc --partition=main-cpu --time=1-00:00 -c 8 --mem=64G
# next commands are run in the compute node 
export MINIWOB_URL=... # put your miniwob path
# let's say the server is running on the node cn-g019.server.mila.quebec , change this 
# if your server is on a different nodes
python main_served.py --model Qwen/Qwen2.5-3B-Instruct --host cn-g019.server.mila.quebec 
```
