"""
Note: This script is a convenience script to launch experiments instead of using
the command line.

Copy this script and modify at will, but don't push your changes to the
repository.
"""
import os
#os.environ["MINIWOB_URL"] = "file:///home/<USER>/m/megh.thakkar/language_grounding/web_agents/miniwob-plusplus/miniwob/html/miniwob/"
print('scratch', os.environ['SCRATCH'])
os.environ["TRANSFORMERS_CACHE"] = os.environ['SCRATCH'] + "/huggingface/models/"
os.environ["AGENTLAB_EXP_ROOT"] = os.environ['SCRATCH'] + "/agentlab_results/"
import logging
from dataclasses import dataclass
from argparse import ArgumentParser
from typing import Optional

from agentlab.agents.generic_agent.agent_configs import GenericAgentArgs, FLAGS_8B, CHAT_MODEL_ARGS_DICT
from agentlab.experiments.study import Study
from agentlab.llm.huggingface_utils import HFBaseChatModel
from agentlab.llm.base_api import BaseModelArgs
from vllm import LLM, SamplingParams
from browsergym.experiments.benchmark import Benchmark
from browsergym.experiments.benchmark.configs import DEFAULT_HIGHLEVEL_ACTION_SET_ARGS, DEFAULT_BENCHMARKS
import numpy as np

from browsergym.experiments.benchmark.metadata.utils import task_list_from_metadata, task_metadata
from browsergym.experiments.benchmark.utils import make_env_args_list_from_repeat_tasks
from browsergym.core.registration import register_task

from browsergym.miniwob.base import AbstractMiniwobTask
from browsergym.core.env import BrowserEnv


class UseSpinnerTask1(AbstractMiniwobTask):
    desc = "Use a spinner to select 1 number."
    subdomain = "use-spinner-1"

    def _get_goal(self) -> str:
        goal = super()._get_goal()
        number = int(goal.split(" ")[1])
        if number < 0:
            goal += f"\nClick the down arrow button {abs(number)} times and then click the Submit button."
        else:
            goal += f"\nClick the up arrow button {abs(number)} times and then click the Submit button."
        return goal

class UseSpinnerTask2(AbstractMiniwobTask):
    desc = "Use a spinner to select -1 number."
    subdomain = "use-spinner-2"

    def _get_goal(self) -> str:
        goal = super()._get_goal()
        number = int(goal.split(" ")[1])
        if number < 0:
            goal += f"\nClick the down arrow button {abs(number)} times and then click the Submit button."
        else:
            goal += f"\nClick the up arrow button {abs(number)} times and then click the Submit button."
        return goal

class UseSpinnerTask3(AbstractMiniwobTask):
    desc = "Use a spinner to select 6 number."
    subdomain = "use-spinner-3"

    def _get_goal(self) -> str:
        goal = super()._get_goal()
        number = int(goal.split(" ")[1])
        if number < 0:
            goal += f"\nClick the down arrow button {abs(number)} times and then click the Submit button."
        else:
            goal += f"\nClick the up arrow button {abs(number)} times and then click the Submit button."
        return goal

class UseSpinnerTask4(AbstractMiniwobTask):
    desc = "Use a spinner to select -4 number."
    subdomain = "use-spinner-4"

    def _get_goal(self) -> str:
        goal = super()._get_goal()
        number = int(goal.split(" ")[1])
        if number < 0:
            goal += f"\nClick the down arrow button {abs(number)} times and then click the Submit button."
        else:
            goal += f"\nClick the up arrow button {abs(number)} times and then click the Submit button."
        return goal

class UseSpinnerTask5(AbstractMiniwobTask):
    desc = "Use a spinner to select 4 number."
    subdomain = "use-spinner-5"

    def _get_goal(self) -> str:
        goal = super()._get_goal()
        number = int(goal.split(" ")[1])
        if number < 0:
            goal += f"\nClick the down arrow button {abs(number)} times and then click the Submit button."
        else:
            goal += f"\nClick the up arrow button {abs(number)} times and then click the Submit button."
        return goal

logging.getLogger().setLevel(logging.INFO)

for task in [UseSpinnerTask1, UseSpinnerTask2, UseSpinnerTask3, UseSpinnerTask4, UseSpinnerTask5]:
    register_task(
        task.get_task_id(),
        task,
        nondeterministic=task.nondeterministic,
    )

MODEL_NAME = 'google/gemma-2-2b-it'
# MODEL_NAME = 'meta-llama/Llama-3.1-8B-Instruct'
if __name__ == "__main__":  # necessary for dask backend
    parser = ArgumentParser()
    parser.add_argument('--model', type=str,)
    parser.add_argument('--tp', type=int, default=1)
    args = parser.parse_args()
    
    # MODEL_NAME = args.model

    assert 'MINIWOB_URL' in os.environ and os.environ['MINIWOB_URL'].startswith('file://')
    LLM_OBJ = LLM(
        model=MODEL_NAME, 
        # hf_overrides={'cache_dir': os.environ['SCRATCH'] + '/hf'},
        download_dir=os.environ['SCRATCH'] + '/huggingface/models',
        #dtype='bfloat16',
        tensor_parallel_size=args.tp
    )

    class HF_VLLM(HFBaseChatModel):
        def __init__(
            self,
            model_name: str,
            temperature: Optional[int] = 1e-1,
            top_p=0.95, top_k=50,
            max_new_tokens: Optional[int] = 512,
        ):
            super().__init__(model_name, n_retry_server=1)
            self.llm_obj = LLM_OBJ
            self.sampling_params = SamplingParams(
                temperature=temperature, top_k=top_k,
                top_p=top_p, max_tokens=max_new_tokens
            )
            def llm_(x):
                #import pdb
                #pdb.set_trace()
                resp = self.llm_obj.generate(x, self.sampling_params)[0].outputs[0].text
                return resp
            self.llm = llm_

    @dataclass
    class HF_VLLMArgs(BaseModelArgs):
        top_p: float = 0.95
        top_k: int = 50

        def make_model(self):
            return HF_VLLM(
                model_name=self.model_name,
                temperature=self.temperature,
                max_new_tokens=self.max_new_tokens,
                top_p=self.top_p, top_k=self.top_k
            )

    # choose your agent or provide a new agent
    AGENT = GenericAgentArgs(
        chat_model_args=HF_VLLMArgs(
            model_name=MODEL_NAME,
            max_input_tokens=100000, max_new_tokens=28000,
            max_total_tokens=128000, temperature=0.1, top_p=0.95,
            top_k=50,
        ),
        # chat_model_args=CHAT_MODEL_ARGS_DICT["openrouter/meta-llama/llama-3.1-8b-instruct"],
        flags=FLAGS_8B,
    )
    agent_args = [AGENT]

    benchmark = Benchmark(
        name="miniwob-full-n-repeat-2",
        high_level_action_set_args=DEFAULT_HIGHLEVEL_ACTION_SET_ARGS["miniwob_all"],
        is_multi_tab=False,
        supports_parallel_seeds=True,
        backends=["miniwob"],
        env_args_list=make_env_args_list_from_repeat_tasks(
            task_list=[
                # "miniwob.use-spinner",
                "miniwob.use-spinner-1",
                "miniwob.use-spinner-2",
                # "miniwob.use-spinner-3",
                # "miniwob.use-spinner-4",
                # "miniwob.use-spinner-5",
            ],
            max_steps=10,
            n_repeats=1,
            seeds_rng=np.random.RandomState(42),
        ),
        # task_metadata=task_metadata("miniwob"),
    )
    reproducibility_mode = False

    relaunch = False

    n_jobs = 1  # to use all available cores

    if reproducibility_mode:
        [a.set_reproducibility_mode() for a in agent_args]

    if relaunch:
        #  relaunch an existing study
        study = Study.load_most_recent(contains=None)
        study.find_incomplete(include_errors=True)

    else:
        study = Study(agent_args, benchmark, logging_level_stdout=logging.INFO)

    study.run(
        n_jobs=n_jobs,
        parallel_backend="sequential",
        strict_reproducibility=reproducibility_mode,
        n_relaunch=1,
    )

    if reproducibility_mode:
        study.append_to_journal(strict_reproducibility=True)
