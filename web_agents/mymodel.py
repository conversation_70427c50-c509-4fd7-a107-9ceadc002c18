import os
#os.environ["MINIWOB_URL"] = "file:///home/<USER>/m/megh.thakkar/language_grounding/web_agents/miniwob-plusplus/miniwob/html/miniwob/"
print('scratch', os.environ['SCRATCH'])
os.environ["TRANSFORMERS_CACHE"] = os.environ['SCRATCH'] + "/huggingface/models/"
os.environ["AGENTLAB_EXP_ROOT"] = os.environ['SCRATCH'] + "/agentlab_results/"
os.environ["OPENROUTER_API_KEY"] = 'test' 
import logging
from dataclasses import dataclass
from argparse import ArgumentParser
from typing import Optional
from openai import OpenAI
from collections import defaultdict

from agentlab.agents.generic_agent.agent_configs import GenericAgentArgs, FLAGS_8B, CHAT_MODEL_ARGS_DICT
from agentlab.experiments.study import Study
from agentlab.llm.chat_api import ChatModel, OpenRouterModelArgs
from agentlab.llm.base_api import BaseModelArgs
from browsergym.experiments.benchmark import Benchmark
from browsergym.experiments.benchmark.configs import DEFAULT_HIGHLEVEL_ACTION_SET_ARGS, DEFAULT_BENCHMARKS
import numpy as np

from browsergym.experiments.benchmark.metadata.utils import task_list_from_metadata, task_metadata
from browsergym.experiments.benchmark.utils import make_env_args_list_from_repeat_tasks

logging.getLogger().setLevel(logging.INFO)
class MyChatModel(ChatModel):
    def __init__(
        self,
        model_name,
        api_key=None,
        temperature=0.5,
        max_tokens=100,
        max_retry=4,
        url=None,
        port=None,
        min_retry_wait_time=60,
    ):
        if url is None:
            url = 'localhost'
        if port is None:
            port = 8002
        client_args = {
            "base_url": f"http://{url}:{port}/v1",
        }
        
        super().__init__(
            model_name=model_name,
            api_key=api_key,
            temperature=temperature,
            max_tokens=max_tokens,
            max_retry=max_retry,
            min_retry_wait_time=min_retry_wait_time,
            api_key_env_var="OPENROUTER_API_KEY",
            client_class=OpenAI,
            client_args=client_args,
            pricing_func=lambda: defaultdict(lambda: defaultdict(float)),
        )
@dataclass
class MyModelArgs(BaseModelArgs):
    """Serializable object for instantiating a generic chat model with an OpenAI
    model."""
    url: str = None
    port: int = None

    def make_model(self):
        return MyChatModel(
            model_name=self.model_name,
            temperature=self.temperature,
            max_tokens=self.max_new_tokens,
            url=self.url, port=self.port
        )

