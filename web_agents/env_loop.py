import os
from dataclasses import dataclass
from typing import Optional

from agentlab.agents.generic_agent.agent_configs import GenericAgentArgs, FLAGS_8B
from agentlab.llm.huggingface_utils import HFBaseChatModel
from agentlab.llm.base_api import BaseModelArgs
from vllm import LLM, SamplingParams
import numpy as np
from agentlab.agents import dynamic_prompting as dp
import logging
from browsergym.core.registration import register_task
from pathlib import Path

from browsergym.experiments.loop import StepInfo, _send_chat_info
from browsergym.experiments.benchmark.utils import make_env_args_list_from_repeat_tasks
from browsergym.miniwob.base import AbstractMiniwobTask
from browsergym.core.env import BrowserEnv

from goal_based_agent.goal_based_agent import GoalBasedAgentArgs
from goal_based_agent.goal_agent_prompt import GoalBasedPromptFlags


class UseSpinnerTask1(AbstractMiniwobTask):
    desc = "Use a spinner to select 1 number."
    subdomain = "use-spinner-1"

    def _get_goal(self) -> str:
        goal = super()._get_goal()
        number = int(goal.split(" ")[1])
        if number < 0:
            goal += f"\nClick the down arrow button {abs(number)} times and then click the Submit button."
        else:
            goal += f"\nClick the up arrow button {abs(number)} times and then click the Submit button."
        return """{"type": "click_n_times", "button": "down arrow", "times": 1}"""

class UseSpinnerTask2(AbstractMiniwobTask):
    desc = "Use a spinner to select -1 number."
    subdomain = "use-spinner-2"

    def _get_goal(self) -> str:
        goal = super()._get_goal()
        number = int(goal.split(" ")[1])
        if number < 0:
            goal += f"\nClick the down arrow button {abs(number)} times and then click the Submit button."
        else:
            goal += f"\nClick the up arrow button {abs(number)} times and then click the Submit button."
        return """{"type": "click_n_times", "button": "down arrow", "times": 2}"""

class UseSpinnerTask3(AbstractMiniwobTask):
    desc = "Use a spinner to select 6 number."
    subdomain = "use-spinner-3"

    def _get_goal(self) -> str:
        goal = super()._get_goal()
        number = int(goal.split(" ")[1])
        if number < 0:
            goal += f"\nClick the down arrow button {abs(number)} times and then click the Submit button."
        else:
            goal += f"\nClick the up arrow button {abs(number)} times and then click the Submit button."
        return """{"type": "click_n_times", "button": "down arrow", "times": 3}"""

class UseSpinnerTask4(AbstractMiniwobTask):
    desc = "Use a spinner to select -4 number."
    subdomain = "use-spinner-4"

    def _get_goal(self) -> str:
        goal = super()._get_goal()
        number = int(goal.split(" ")[1])
        if number < 0:
            goal += f"\nClick the down arrow button {abs(number)} times and then click the Submit button."
        else:
            goal += f"\nClick the up arrow button {abs(number)} times and then click the Submit button."
        return """{"type": "click_n_times", "button": "down arrow", "times": 4}"""

class UseSpinnerTask5(AbstractMiniwobTask):
    desc = "Use a spinner to select 4 number."
    subdomain = "use-spinner-5"

    def _get_goal(self) -> str:
        goal = super()._get_goal()
        number = int(goal.split(" ")[1])
        if number < 0:
            goal += f"\nClick the down arrow button {abs(number)} times and then click the Submit button."
        else:
            goal += f"\nClick the up arrow button {abs(number)} times and then click the Submit button."
        return """{"type": "click_n_times", "button": "down arrow", "times": 5}"""

logging.getLogger().setLevel(logging.INFO)

for task in [UseSpinnerTask1, UseSpinnerTask2, UseSpinnerTask3, UseSpinnerTask4, UseSpinnerTask5]:
    register_task(
        task.get_task_id(),
        task,
        nondeterministic=task.nondeterministic,
    )



class UseSpinnerTask(AbstractMiniwobTask):
    desc = "Use a spinner to select given number."
    subdomain = "use-spinner"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.last_goal = None

    def _get_goal(self) -> str:
        # keys in json:
        # button name/id
        # how many times to click

        # informal example {'type': 'click_n_times', 'button': 'down arrrow', 'times': 6}
        """
        you are a goal based agent blah blah.
        you can work with several goal templates.
        The first goal is to 
        
        """
        goal = super()._get_goal()
        number = int(goal.split(" ")[1])
        self.last_goal = number
        if number < 0:
            goal += f"\nClick the down arrow button {abs(number)} times and then click the Submit button."
        else:
            goal += f"\nClick the up arrow button {abs(number)} times and then click the Submit button."
        return goal



if __name__ == '__main__':
    assert 'MINIWOB_URL' in os.environ and os.environ['MINIWOB_URL'].startswith('file://')
    LLM_OBJ = LLM(
        model= 'meta-llama/Llama-3.2-1B-Instruct',  #'meta-llama/Meta-Llama-3-8B-Instruct', 
        # hf_overrides={'cache_dir': os.environ['SCRATCH'] + '/hf'},
        download_dir=os.environ['SCRATCH'] + '/hf'
    )
    class HF_VLLM(HFBaseChatModel):
        def __init__(
            self,
            model_name: str,
            temperature: Optional[int] = 1e-1,
            top_p=0.95, top_k=50,
            max_new_tokens: Optional[int] = 512,
        ):
            super().__init__(model_name, base_model_name=None, n_retry_server=1)
            self.llm_obj = LLM_OBJ
            self.sampling_params = SamplingParams(
                temperature=temperature, top_k=top_k,
                top_p=top_p, max_tokens=max_new_tokens
            )
            self.temperature = temperature
            # self.llm = lambda x, temperature=1.0: {
            #     'content': self.llm_obj.generate(x, self.sampling_params)[0].outputs[0].text,
            #     'role': 'assistant'
            # }
            self.llm = lambda x, temperature=1.0: self.llm_obj.generate(x, self.sampling_params)[0].outputs[0].text

    @dataclass
    class HF_VLLMArgs(BaseModelArgs):
        top_p: float = 0.95
        top_k: int = 50

        def make_model(self):
            return HF_VLLM(
                model_name=self.model_name,
                temperature=self.temperature,
                max_new_tokens=self.max_new_tokens,
                top_p=self.top_p, top_k=self.top_k
            )
    envs = make_env_args_list_from_repeat_tasks(
        task_list=[
            "miniwob.use-spinner-4",
        ],
        max_steps=5,
        n_repeats=10,
        seeds_rng=np.random.RandomState(42),
    )
    # agent_args = GoalBasedAgentArgs(
    agent_args = GenericAgentArgs(
        chat_model_args=HF_VLLMArgs(
            model_name='meta-llama/Meta-Llama-3-8B-Instruct',
            max_input_tokens=100000, max_new_tokens=28000,
            max_total_tokens=128000, temperature=0.1, top_p=0.95,
            top_k=50,
        ),
        # chat_model_args=CHAT_MODEL_ARGS_DICT["openrouter/meta-llama/llama-3.1-8b-instruct"],
        flags=FLAGS_8B,
    )
    agent = agent_args.make_agent()

    exp_dir = Path(os.environ['SCRATCH'] + '/test')
    exp_dir.mkdir(exist_ok=True)
    for env_params in envs:
        print('Environment seed', env_params.task_seed)
        # env = env_params.make_env(
        #     action_mapping=agent.action_set.to_python_code,
        #     exp_dir=exp_dir,
        # )

        env = BrowserEnv(
            task_entrypoint=UseSpinnerTask4,
            task_kwargs=dict(
                # base_url=f"file:///home/<USER>/m/maryam.hashemzadeh/projects/artem/language_grounding/web_agents/miniwob-plusplus/miniwob/html/miniwob/"
                # base_url=f"file://{os.getcwd()}/miniwob-plusplus/miniwob/html/miniwob/"
                # base_url=f"file://{os.getcwd()}/custom_miniwob_html/"
                base_url="file:///home/<USER>/m/maryam.hashemzadeh/projects/artem/language_grounding/web_agents/custom_miniwob_html/html/"
            ),
            action_mapping=agent.action_set.to_python_code,
            # exp_dir=exp_dir,
            # /home/<USER>/a/artem.zholus/workspace/language_grounding/web_agents/miniwob-plusplus/miniwob/html/miniwob/
        )
        step_info = StepInfo(step=0)
        episode_info = [step_info]
        step_info.from_reset(
            env, seed=env_params.task_seed, obs_preprocessor=agent.obs_preprocessor
        )
        # my_current_goal = env.task.last_goal
        while not step_info.is_done:  # set a limit
            action = step_info.from_action(agent)

            if action is None:
                # will end the episode after saving the step info.
                step_info.truncated = True

            step_info.save_step_info(
                exp_dir, save_screenshot=True, save_som=False
            )

            _send_chat_info(env.unwrapped.chat, action, step_info.agent_info)

            print(f"------------ action ----------- is:    {action}")
            if action is None:
                break

            step_info = StepInfo(step=step_info.step + 1)
            episode_info.append(step_info)

            step_info.from_step(env, action, obs_preprocessor=agent.obs_preprocessor)
            
        print('reward: ', sum([s.reward for s in episode_info]))
        env.close()
