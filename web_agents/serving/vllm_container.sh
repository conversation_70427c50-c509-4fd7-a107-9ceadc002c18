#!/bin/bash

module load singularity
if [ ! -e "$SCRATCH/vllm.sif" ]; then
    singularity pull $SCRATCH/vllm.sif docker://vllm/vllm-openai:latest
fi
singularity build --sandbox $SLURM_TMPDIR/vllm_img $SCRATCH/vllm.sif
#    vllm serve $MODEL_NAME \
echo "serving $MODEL_NAME"
singularity exec --cleanenv --nv -B $SCRATCH/huggingface/models/:/models $SLURM_TMPDIR/vllm_img \
    vllm serve $MODEL_NAME \
    --tensor-parallel-size 4 \
    --download-dir /models \
    --dtype auto \
    --port 8002
#singularity run --cleanenv --nv $SLURM_TMPDIR/vllm_img \
#    --model $MODEL_NAME  \
#    --tensor-parallel-size 4 \
#    --download-dir $SCRATCH/huggingface/models/ \
#    --dtype auto \
#    --port 8002
