#!/bin/bash

#SBATCH --partition=short-unkillable
#SBATCH --time=0-03:00
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gpus-per-task=4
#SBATCH --cpus-per-task=24
#SBATCH --mem=128G
#SBATCH --constraint=ampere|hopper|lovelace

if [ "$#" -ne 1 ]; then
    echo "Usage: $0 <model>"
    exit 1
fi
export MODEL_NAME="$1"

handle_preemption() {
    trap - SIGTERM # clear the trap
    echo "Preemption signal, requing"
    kill -- -$$
    scontrol requeue $SLURM_JOB_ID
}
trap handle_preemption SIGTERM
srun --ntasks=$SLURM_JOB_NUM_NODES --ntasks-per-node=1 ./vllm_container.sh &
while wait; test $? -gt 128; do :; done
