name: Code Format

on:
  push:
    branches:
      - main
  pull_request:

jobs:

  build:
    runs-on: ubuntu-latest
    defaults:
      run:
        shell: bash -l {0}
    steps:

      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'
          cache: 'pip' # caching pip dependencies

      - name: Pip install
        run: pip install -r requirements.txt

      - name: Pip list
        run: pip list

      - name: Code Formatting
        run: black . --check
