name: Unit tests

on:
  push:
    branches:
      - main
  pull_request:

jobs:

  agentlab:
    runs-on: ubuntu-22.04

    defaults:
      run:
        shell: bash -l {0}

    steps:

      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Set up Git user
        run: git config --global user.email "<EMAIL>" && git config --global user.name "GitHub Actions"

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'
          cache: 'pip' # caching pip dependencies

      - name: Install AgentLab
        run: pip install -e .

      - name: Pip list
        run: pip list

      - name: Install Playwright
        run: playwright install chromium --with-deps

      - name: Fetch MiniWob
        uses: actions/checkout@v4
        with:
          repository: "Farama-Foundation/miniwob-plusplus"
          ref: "7fd85d71a4b60325c6585396ec4f48377d049838"
          path: "miniwob-plusplus"

      - name: <PERSON><PERSON>
        uses: Eun/http-server-action@v1
        with:
          directory: "${{ github.workspace }}/miniwob-plusplus/miniwob/html"
          port: 8080

      # Step to check if MiniWob is reachable
      - name: Check MiniWob availability
        run: curl -I "http://localhost:8080/miniwob/" || echo "MiniWob not reachable"

      - name: Run AgentLab Unit Tests
        env:
          MINIWOB_URL: "http://localhost:8080/miniwob/"
        run: pytest -n 5 --durations=10 -m 'not pricy' -v tests/