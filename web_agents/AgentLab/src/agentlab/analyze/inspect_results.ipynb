{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from agentlab.experiments.exp_utils import RESULTS_DIR\n", "from agentlab.analyze import inspect_results\n", "import pandas as pd\n", "pd.set_option('display.max_rows', 200)\n", "\n", "%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### load all summaries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_summaries = inspect_results.get_all_summaries(RESULTS_DIR.resolve().parent / \"ICML-Neurips-final-run\", ignore_cache=False, ignore_stale=True)\n", "all_summaries"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Load results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # minwob GPT-4o single agent reproduced\n", "# result_dir = RESULTS_DIR / \"2024-05-28_01-16-12_generic_agent_eval_llm\" #\n", "\n", "# # workarena GPT-4o single agent mostly reproduced\n", "# result_dir = RESULTS_DIR / \"2024-05-28_01-13-04_generic_agent_eval_llm\" \n", "# result_dir = RESULTS_DIR / \"2024-05-28_01-44-29_generic_agent_eval_llm\"\n", "\n", "result_dir = inspect_results.get_most_recent_folder(RESULTS_DIR, contains=None)\n", "\n", "print(result_dir)\n", "result_df = inspect_results.load_result_df(result_dir)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Various kind of reports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# basic report using summarize\n", "# result_df = inspect_results.set_task_category_as_index(result_df)\n", "report = inspect_results.global_report(result_df)\n", "inspect_results.display_report(report)\n", "\n", "# some potentially useful flag analysis\n", "inspect_results.flag_report(report, metric=\"avg_reward\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# more stats\n", "report_stats = inspect_results.global_report(result_df, reduce_fn=inspect_results.summarize_stats)\n", "inspect_results.display_report(report_stats)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # breakdown by error type\n", "# report = inspect_results.global_report(result_df, reduce_fn=inspect_results.report_different_errors)\n", "# inspect_results.display_report(report)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Ablation study"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ablation_report = inspect_results.ablation_report(result_df)\n", "inspect_results.display_report(ablation_report)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Constants and Variables\n", "Prints all constants and first 3 unique values of variables"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inspect_results.report_constant_and_variables(result_df, show_stack_traces=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Error report"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(inspect_results.error_report(result_df, max_stack_trace=1))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(inspect_results.error_report_detailed(result_df, max_stack_trace=1))"]}], "metadata": {"kernelspec": {"display_name": "ui-copilot", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.6"}}, "nbformat": 4, "nbformat_minor": 2}