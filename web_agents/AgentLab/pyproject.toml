[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "agentlab"
dynamic = ["version", "dependencies"]
description = "Main package for developing agents and experiments"
authors = [
    {name = "<PERSON><PERSON>", email = "<EMAIL>"},
    {name = "<PERSON>", email = "<EMAIL>"},
    {name = "<PERSON>e Gasse", email = "<EMAIL>"},
    {name = "<PERSON> Lacoste", email = "<EMAIL>"},
    {name = "<PERSON>", email = "<EMAIL>"},
    {name = "Mass<PERSON>", email = "<EMAIL>"},
    {name = "Thibault Le Sellier de Chezelles", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">3.7"
license = {text = "BSD-3-Clause"}
classifiers = [
    "Development Status :: 2 - Pre-Alpha",
    "Programming Language :: Python :: 3",
    "Operating System :: OS Independent",
    "Intended Audience :: Science/Research",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]

[project.urls]
"Homepage" = "https://github.com/ServiceNow/AgentLab"

[tool.setuptools.dynamic]
version = {attr = "agentlab.__version__"}
dependencies = {file = ["requirements.txt"]}

[tool.black]
line-length = 100
include = '\.pyi?$'
exclude = '''
/(
    \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.nox
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''


[project.scripts]
agentlab-assistant = "agentlab.ui_assistant:main"
agentlab-xray = "agentlab.analyze.agent_xray:main"
