﻿///#source 1 1 /js/required/raphael.min.js
(function(n){var o="0.4.2",e="hasOwnProperty",f=/[\.\/]/,s="*",h=function(){},c=function(n,t){return n-t},r,i,u={n:{}},t=function(n,f){var o,p;n=String(n);var k=u,w=i,v=Array.prototype.slice.call(arguments,2),s=t.listeners(n),a=0,e,l=[],y={},h=[],b=r;for(r=n,i=0,o=0,p=s.length;o<p;o++)"zIndex"in s[o]&&(l.push(s[o].zIndex),s[o].zIndex<0&&(y[s[o].zIndex]=s[o]));for(l.sort(c);l[a]<0;)if(e=y[l[a++]],h.push(e.apply(f,v)),i)return i=w,h;for(o=0;o<p;o++)if(e=s[o],"zIndex"in e)if(e.zIndex==l[a]){if(h.push(e.apply(f,v)),i)break;do if(a++,e=y[l[a]],e&&h.push(e.apply(f,v)),i)break;while(e)}else y[e.zIndex]=e;else if(h.push(e.apply(f,v)),i)break;return i=w,r=b,h.length?h:null};t._events=u;t.listeners=function(n){for(var a=n.split(f),t=u,i,v,o,e,p,h,c=[t],l=[],r=0,y=a.length;r<y;r++){for(h=[],e=0,p=c.length;e<p;e++)for(t=c[e].n,v=[t[a[r]],t[s]],o=2;o--;)i=v[o],i&&(h.push(i),l=l.concat(i.f||[]));c=h}return l};t.on=function(n,t){var e,i,r,o;if(n=String(n),typeof t!="function")return function(){};for(e=n.split(f),i=u,r=0,o=e.length;r<o;r++)i=i.n,i=i.hasOwnProperty(e[r])&&i[e[r]]||(i[e[r]]={n:{}});for(i.f=i.f||[],r=0,o=i.f.length;r<o;r++)if(i.f[r]==t)return h;return i.f.push(t),function(n){+n==+n&&(t.zIndex=+n)}};t.f=function(n){var i=[].slice.call(arguments,1);return function(){t.apply(null,[n,null].concat(i).concat([].slice.call(arguments,0)))}};t.stop=function(){i=1};t.nt=function(n){return n?new RegExp("(?:\\.|\\/|^)"+n+"(?:\\.|\\/|$)").test(r):r};t.nts=function(){return r.split(f)};t.off=t.unbind=function(n,i){var a,r,h,v,c,p,o,w,l,y;if(!n){t._events=u={n:{}};return}for(a=n.split(f),l=[u],c=0,p=a.length;c<p;c++)for(o=0;o<l.length;o+=v.length-2){if(v=[o,1],r=l[o].n,a[c]!=s)r[a[c]]&&v.push(r[a[c]]);else for(h in r)r[e](h)&&v.push(r[h]);l.splice.apply(l,v)}for(c=0,p=l.length;c<p;c++)for(r=l[c];r.n;){if(i){if(r.f){for(o=0,w=r.f.length;o<w;o++)if(r.f[o]==i){r.f.splice(o,1);break}r.f.length||delete r.f}for(h in r.n)if(r.n[e](h)&&r.n[h].f){for(y=r.n[h].f,o=0,w=y.length;o<w;o++)if(y[o]==i){y.splice(o,1);break}y.length||delete r.n[h].f}}else{delete r.f;for(h in r.n)r.n[e](h)&&r.n[h].f&&delete r.n[h].f}r=r.n}};t.once=function(n,i){var r=function(){return t.unbind(n,r),i.apply(this,arguments)};return t.on(n,r)};t.version=o;t.toString=function(){return"You are running Eve "+o};typeof module!="undefined"&&module.exports?module.exports=t:typeof define!="undefined"?define("eve",[],function(){return t}):n.eve=t})(window||this),function(n,t){typeof define=="function"&&define.amd?define(["eve"],function(i){return t(n,i)}):t(n,n.eve)}(this,function(n,t){function i(n){var r,u;return i.is(n,"function")?ai?n():t.on("raphael.DOMload",n):i.is(n,tt)?i._engine.create[v](i,n.splice(0,3+i.is(n[0],p))).add(n):(r=Array.prototype.slice.call(arguments,0),i.is(r[r.length-1],"function")?(u=r.pop(),ai?u.call(i._engine.create[v](i,r)):t.on("raphael.DOMload",function(){u.call(i._engine.create[v](i,r))})):i._engine.create[v](i,arguments))}function pt(n){var i,t;if(typeof n=="function"||Object(n)!==n)return n;i=new n.constructor;for(t in n)n[a](t)&&(i[t]=pt(n[t]));return i}function sf(n,t){for(var i=0,r=n.length;i<r;i++)if(n[i]===t)return n.push(n.splice(i,1)[0])}function it(n,t,i){function r(){var o=Array.prototype.slice.call(arguments,0),u=o.join("␀"),f=r.cache=r.cache||{},e=r.count=r.count||[];return f[a](u)?(sf(e,u),i?i(f[u]):f[u]):(e.length>=1e3&&delete f[e.shift()],e.push(u),f[u]=n[v](t,o),i?i(f[u]):f[u])}return r}function oi(){return this.hex}function br(n,t){for(var i,f=[],r=0,u=n.length;u-2*!t>r;r+=2)i=[{x:+n[r-2],y:+n[r-1]},{x:+n[r],y:+n[r+1]},{x:+n[r+2],y:+n[r+3]},{x:+n[r+4],y:+n[r+5]}],t?r?u-4==r?i[3]={x:+n[0],y:+n[1]}:u-2==r&&(i[2]={x:+n[0],y:+n[1]},i[3]={x:+n[2],y:+n[3]}):i[0]={x:+n[u-2],y:+n[u-1]}:u-4==r?i[3]=i[2]:r||(i[0]={x:+n[r],y:+n[r+1]}),f.push(["C",(-i[0].x+6*i[1].x+i[2].x)/6,(-i[0].y+6*i[1].y+i[2].y)/6,(i[1].x+6*i[2].x-i[3].x)/6,(i[1].y+6*i[2].y-i[3].y)/6,i[2].x,i[2].y]);return f}function kr(n,t,i,r,u){var f=-3*t+9*i-9*r+3*u,e=n*f+6*t-12*i+6*r;return n*e-3*t+3*i}function vt(n,t,i,r,f,e,o,s,h){var c;h==null&&(h=1);h=h>1?1:h<0?0:h;var l=h/2,w=[-.1252,.1252,-.3678,.3678,-.5873,.5873,-.7699,.7699,-.9041,.9041,-.9816,.9816],b=[.2491,.2491,.2335,.2335,.2032,.2032,.1601,.1601,.1069,.1069,.0472,.0472],a=0;for(c=0;c<12;c++){var v=l*w[c]+l,y=kr(v,n,i,f,o),p=kr(v,t,r,e,s),k=y*y+p*p;a+=b[c]*u.sqrt(k)}return l*a}function cf(n,t,i,r,u,f,e,o,s){if(!(s<0)&&!(vt(n,t,i,r,u,f,e,o)<s)){for(var a=1,l=a/2,h=a-l,c=vt(n,t,i,r,u,f,e,o,h);y(c-s)>.01;)l/=2,h+=(c<s?1:-1)*l,c=vt(n,t,i,r,u,f,e,o,h);return h}}function lf(n,t,i,r,u,f,e,s){if(!(o(n,i)<l(u,e))&&!(l(n,i)>o(u,e))&&!(o(t,r)<l(f,s))&&!(l(t,r)>o(f,s))){var p=(n*r-t*i)*(u-e)-(n-i)*(u*s-f*e),w=(n*r-t*i)*(f-s)-(t-r)*(u*s-f*e),a=(n-i)*(f-s)-(t-r)*(u-e);if(a){var v=p/a,y=w/a,h=+v.toFixed(2),c=+y.toFixed(2);if(!(h<+l(n,i).toFixed(2))&&!(h>+o(n,i).toFixed(2))&&!(h<+l(u,e).toFixed(2))&&!(h>+o(u,e).toFixed(2))&&!(c<+l(t,r).toFixed(2))&&!(c>+o(t,r).toFixed(2))&&!(c<+l(f,s).toFixed(2))&&!(c>+o(f,s).toFixed(2)))return{x:v,y:y}}}}function di(n,t,r){var ut=i.bezierBBox(n),ft=i.bezierBBox(t),u,h,c,d,g;if(!i.isBBoxIntersect(ut,ft))return r?0:[];var et=vt.apply(0,n),ot=vt.apply(0,t),p=o(~~(et/5),1),w=o(~~(ot/5),1),nt=[],tt=[],rt={},it=r?0:[];for(u=0;u<p+1;u++)h=i.findDotsAtSegment.apply(i,n.concat(u/p)),nt.push({x:h.x,y:h.y,t:u/p});for(u=0;u<w+1;u++)h=i.findDotsAtSegment.apply(i,t.concat(u/w)),tt.push({x:h.x,y:h.y,t:u/w});for(u=0;u<p;u++)for(c=0;c<w;c++){var e=nt[u],a=nt[u+1],s=tt[c],v=tt[c+1],b=y(a.x-e.x)<.001?"y":"x",k=y(v.x-s.x)<.001?"y":"x",f=lf(e.x,e.y,a.x,a.y,s.x,s.y,v.x,v.y);if(f){if(rt[f.x.toFixed(4)]==f.y.toFixed(4))continue;rt[f.x.toFixed(4)]=f.y.toFixed(4);d=e.t+y((f[b]-e[b])/(a[b]-e[b]))*(a.t-e.t);g=s.t+y((f[k]-s[k])/(v[k]-s[k]))*(v.t-s.t);d>=0&&d<=1.001&&g>=0&&g<=1.001&&(r?it++:it.push({x:f.x,y:f.y,t1:l(d,1),t2:l(g,1)}))}}return it}function gi(n,t,r){var e,o,s,h,b,k,d,g,c,l,y,p,nt,a,w,tt,v,u,f,it;for(n=i._path2curve(n),t=i._path2curve(t),y=r?0:[],p=0,nt=n.length;p<nt;p++)if(a=n[p],a[0]=="M")e=b=a[1],o=k=a[2];else for(a[0]=="C"?(c=[e,o].concat(a.slice(1)),e=c[6],o=c[7]):(c=[e,o,e,o,b,k,b,k],e=b,o=k),w=0,tt=t.length;w<tt;w++)if(v=t[w],v[0]=="M")s=d=v[1],h=g=v[2];else if(v[0]=="C"?(l=[s,h].concat(v.slice(1)),s=l[6],h=l[7]):(l=[s,h,s,h,d,g,d,g],s=d,h=g),u=di(c,l,r),r)y+=u;else{for(f=0,it=u.length;f<it;f++)u[f].segment1=p,u[f].segment2=w,u[f].bez1=c,u[f].bez2=l;y=y.concat(u)}return y}function ht(n,t,i,r,u,f){n!=null?(this.a=+n,this.b=+t,this.c=+i,this.d=+r,this.e=+u,this.f=+f):(this.a=1,this.b=0,this.c=0,this.d=1,this.e=0,this.f=0)}function eu(){return this.x+lt+this.y+lt+this.width+" × "+this.height}function gf(n,t,i,r,u,f){function l(n){return((h*n+o)*n+e)*n}function v(n,t){var i=p(n,t);return((a*i+c)*i+s)*i}function p(n,t){for(var r,u,f,s,i=n,c=0;c<8;c++){if(f=l(i)-n,y(f)<t)return i;if(s=(3*h*i+2*o)*i+e,y(s)<1e-6)break;i=i-f/s}if(r=0,u=1,i=n,i<r)return r;if(i>u)return u;while(r<u){if(f=l(i),y(f-n)<t)return i;n>f?r=i:u=i;i=(u-r)/2+r}return i}var e=3*t,o=3*(r-t)-e,h=1-e-o,s=3*i,c=3*(u-i)-s,a=1-s-c;return v(n,1/(200*f))}function ft(n,t){var i=[],u={},r;if(this.ms=t,this.times=1,n){for(r in n)n[a](r)&&(u[h(r)]=n[r],i.push(h(r)));i.sort(tf)}this.anim=u;this.top=i[i.length-1];this.percents=i}function kt(n,r,u,e,o,c){var nt,v,et,l,at,dt,ii,tt,vt,gt,yt,d,rt,st,ct,ni,ft,lt;u=h(u);var it,ot,pt,ti,bt,kt,w=n.ms,y={},g={},k={};if(e){for(v=0,et=f.length;v<et;v++)if(nt=f[v],nt.el.id==r.id&&nt.anim==n){nt.percent!=u?(f.splice(v,1),pt=1):ot=nt;r.attr(nt.totalOrigin);break}}else e=+g;for(v=0,et=n.percents.length;v<et;v++)if(n.percents[v]==u||n.percents[v]>e*n.top){u=n.percents[v];bt=n.percents[v-1]||0;w=w/n.top*(u-bt);ti=n.percents[v+1];it=n.anim[u];break}else e&&r.attr(n.anim[n.percents[v]]);if(it){if(ot)ot.initstatus=e,ot.start=new Date-ot.ms*e;else{for(l in it)if(it[a](l)&&(wi[a](l)||r.paper.customAttributes[a](l))){y[l]=r.attr(l);y[l]==null&&(y[l]=bu[l]);g[l]=it[l];switch(wi[l]){case p:k[l]=(g[l]-y[l])/w;break;case"colour":y[l]=i.getRGB(y[l]);at=i.getRGB(g[l]);k[l]={r:(at.r-y[l].r)/w,g:(at.g-y[l].g)/w,b:(at.b-y[l].b)/w};break;case"path":for(dt=wt(y[l],g[l]),ii=dt[1],y[l]=dt[0],k[l]=[],v=0,et=y[l].length;v<et;v++)for(k[l][v]=[0],tt=1,vt=y[l][v].length;tt<vt;tt++)k[l][v][tt]=(ii[v][tt]-y[l][v][tt])/w;break;case"transform":if(gt=r._,yt=yf(gt[l],g[l]),yt)for(y[l]=yt.from,g[l]=yt.to,k[l]=[],k[l].real=!0,v=0,et=y[l].length;v<et;v++)for(k[l][v]=[y[l][v][0]],tt=1,vt=y[l][v].length;tt<vt;tt++)k[l][v][tt]=(g[l][v][tt]-y[l][v][tt])/w;else d=r.matrix||new ht,rt={_:{transform:gt.transform},getBBox:function(){return r.getBBox(1)}},y[l]=[d.a,d.b,d.c,d.d,d.e,d.f],iu(rt,g[l]),g[l]=rt._.transform,k[l]=[(rt.matrix.a-d.a)/w,(rt.matrix.b-d.b)/w,(rt.matrix.c-d.c)/w,(rt.matrix.d-d.d)/w,(rt.matrix.e-d.e)/w,(rt.matrix.f-d.f)/w];break;case"csv":if(st=b(it[l])[ut](vi),ct=b(y[l])[ut](vi),l=="clip-rect")for(y[l]=ct,k[l]=[],v=ct.length;v--;)k[l][v]=(st[v]-y[l][v])/w;g[l]=st;break;default:for(st=[][s](it[l]),ct=[][s](y[l]),k[l]=[],v=r.paper.customAttributes[l].length;v--;)k[l][v]=((st[v]||0)-(ct[v]||0))/w}}if(ni=it.easing,ft=i.easing_formulas[ni],ft||(ft=b(ni).match(wu),ft&&ft.length==5?(lt=ft,ft=function(n){return gf(n,+lt[1],+lt[2],+lt[3],+lt[4],w)}):ft=uf),kt=it.start||n.start||+new Date,nt={anim:n,percent:u,timestamp:kt,start:kt+(n.del||0),status:0,initstatus:e||0,stop:!1,ms:w,easing:ft,from:y,diff:k,to:g,el:r,callback:it.callback,prev:bt,next:ti,repeat:c||n.times,origin:r.attr(),totalOrigin:o},f.push(nt),e&&!ot&&!pt&&(nt.stop=!0,nt.start=new Date-w*e,f.length==1))return or();pt&&(nt.start=new Date-nt.ms*e);f.length==1&&hu(or)}t("raphael.anim.start."+r.id,r,n)}}function cu(n){for(var t=0;t<f.length;t++)f[t].el.paper==n&&f.splice(t--,1)}var ui,fi,ff,hf,et,bt,rr,ct,fu,g,yt,w,li;i.version="2.1.2";i.eve=t;var ai,vi=/[, ]+/,lu={circle:1,rect:1,path:1,ellipse:1,text:1,image:1},au=/\{(\d+)\}/g,a="hasOwnProperty",r={doc:document,win:n},yi={was:Object.prototype[a].call(r.win,"Raphael"),is:r.win.Raphael},hr=function(){this.ca=this.customAttributes={}},c,v="apply",s="concat",dt="ontouchstart"in r.win||r.win.DocumentTouch&&r.doc instanceof DocumentTouch,d="",lt=" ",b=String,ut="split",cr="click dblclick mousedown mousemove mouseout mouseover mouseup touchstart touchmove touchend touchcancel"[ut](lt),gt={mousedown:"touchstart",mousemove:"touchmove",mouseup:"touchend"},ni=b.prototype.toLowerCase,u=Math,o=u.max,l=u.min,y=u.abs,nt=u.pow,k=u.PI,p="number",ti="string",tt="array",vu=Object.prototype.toString,ne=i._ISURL=/^url\(['"]?([^\)]+?)['"]?\)$/i,yu=/^\s*((#[a-f\d]{6})|(#[a-f\d]{3})|rgba?\(\s*([\d\.]+%?\s*,\s*[\d\.]+%?\s*,\s*[\d\.]+%?(?:\s*,\s*[\d\.]+%?)?)\s*\)|hsba?\(\s*([\d\.]+(?:deg|\xb0|%)?\s*,\s*[\d\.]+%?\s*,\s*[\d\.]+(?:%?\s*,\s*[\d\.]+)?)%?\s*\)|hsla?\(\s*([\d\.]+(?:deg|\xb0|%)?\s*,\s*[\d\.]+%?\s*,\s*[\d\.]+(?:%?\s*,\s*[\d\.]+)?)%?\s*\))\s*$/i,pu={NaN:1,Infinity:1,"-Infinity":1},wu=/^(?:cubic-)?bezier\(([^,]+),([^,]+),([^,]+),([^\)]+)\)/,pi=u.round,h=parseFloat,st=parseInt,lr=b.prototype.toUpperCase,bu=i._availableAttrs={"class":"","arrow-end":"none","arrow-start":"none",blur:0,"clip-rect":"0 0 1e9 1e9",cursor:"default",cx:0,cy:0,fill:"#fff","fill-opacity":1,font:'10px "Arial"',"font-family":'"Arial"',"font-size":"10","font-style":"normal","font-weight":400,gradient:0,height:0,href:"http://raphaeljs.com/","letter-spacing":0,opacity:1,path:"M0,0",r:0,rx:0,ry:0,src:"",stroke:"#000","stroke-dasharray":"","stroke-linecap":"butt","stroke-linejoin":"butt","stroke-miterlimit":0,"stroke-opacity":1,"stroke-width":1,target:"_blank","text-anchor":"middle",title:"Raphael",transform:"",width:0,x:0,y:0},wi=i._availableAnimAttrs={blur:p,"clip-rect":"csv",cx:p,cy:p,fill:"colour","fill-opacity":p,"font-size":p,height:p,opacity:p,path:"path",r:p,rx:p,ry:p,stroke:"colour","stroke-opacity":p,"stroke-width":p,transform:"transform",width:p,x:p,y:p},bi=/[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*,[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*/,ku={hs:1,rg:1},du=/,?([achlmqrstvxz]),?/gi,gu=/([achlmrqstvz])[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029,]*((-?\d*\.?\d*(?:e[\-+]?\d+)?[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*,?[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*)+)/ig,nf=/([rstm])[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029,]*((-?\d*\.?\d*(?:e[\-+]?\d+)?[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*,?[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*)+)/ig,ar=/(-?\d*\.?\d*(?:e[\-+]?\d+)?)[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*,?[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*/ig,te=i._radial_gradient=/^r(?:\(([^,]+?)[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*,[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*([^\)]+?)\))?/,at={},ie=function(n,t){return n.key-t.key},tf=function(n,t){return h(n)-h(t)},rf=function(){},uf=function(n){return n},ii=i._rectPath=function(n,t,i,r,u){return u?[["M",n+u,t],["l",i-u*2,0],["a",u,u,0,0,1,u,u],["l",0,r-u*2],["a",u,u,0,0,1,-u,u],["l",u*2-i,0],["a",u,u,0,0,1,-u,-u],["l",0,u*2-r],["a",u,u,0,0,1,u,-u],["z"]]:[["M",n,t],["l",i,0],["l",0,r],["l",-i,0],["z"]]},vr=function(n,t,i,r){return r==null&&(r=i),[["M",n,t],["m",0,-r],["a",i,r,0,1,1,0,2*r],["a",i,r,0,1,1,0,-2*r],["z"]]},ri=i._getPath={path:function(n){return n.attr("path")},circle:function(n){var t=n.attrs;return vr(t.cx,t.cy,t.r)},ellipse:function(n){var t=n.attrs;return vr(t.cx,t.cy,t.rx,t.ry)},rect:function(n){var t=n.attrs;return ii(t.x,t.y,t.width,t.height,t.r)},image:function(n){var t=n.attrs;return ii(t.x,t.y,t.width,t.height)},text:function(n){var t=n._getBBox();return ii(t.x,t.y,t.width,t.height)},set:function(n){var t=n._getBBox();return ii(t.x,t.y,t.width,t.height)}},ki=i.mapPath=function(n,t){if(!t)return n;var f,e,u,i,o,s,r;for(n=wt(n),u=0,o=n.length;u<o;u++)for(r=n[u],i=1,s=r.length;i<s;i+=2)f=t.x(r[i],r[i+1]),e=t.y(r[i],r[i+1]),r[i]=f,r[i+1]=e;return n};if(i._g=r,i.type=r.win.SVGAngle||r.doc.implementation.hasFeature("http://www.w3.org/TR/SVG11/feature#BasicStructure","1.1")?"SVG":"VML",i.type=="VML"){if(ui=r.doc.createElement("div"),ui.innerHTML='<v:shape adj="1"/>',fi=ui.firstChild,fi.style.behavior="url(#default#VML)",!(fi&&typeof fi.adj=="object"))return i.type=d;ui=null}i.svg=!(i.vml=i.type=="VML");i._Paper=hr;i.fn=c=hr.prototype=i.prototype;i._id=0;i._oid=0;i.is=function(n,t){return(t=ni.call(t),t=="finite")?!pu[a](+n):t=="array"?n instanceof Array:t=="null"&&n===null||t==typeof n&&n!==null||t=="object"&&n===Object(n)||t=="array"&&Array.isArray&&Array.isArray(n)||vu.call(n).slice(8,-1).toLowerCase()==t};i.angle=function(n,t,r,f,e,o){if(e==null){var s=n-r,h=t-f;return!s&&!h?0:(180+u.atan2(-h,-s)*180/k+360)%360}return i.angle(n,t,e,o)-i.angle(r,f,e,o)};i.rad=function(n){return n%360*k/180};i.deg=function(n){return n*180/k%360};i.snapTo=function(n,t,r){var f,u;if(r=i.is(r,"finite")?r:10,i.is(n,tt)){for(f=n.length;f--;)if(y(n[f]-t)<=r)return n[f]}else{if(n=+n,u=t%n,u<r)return t-u;if(u>n-r)return t-u+n}return t};ff=i.createUUID=function(n,t){return function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(n,t).toUpperCase()}}(/[xy]/g,function(n){var t=u.random()*16|0,i=n=="x"?t:t&3|8;return i.toString(16)});i.setWindow=function(n){t("raphael.setWindow",i,r.win,n);r.win=n;r.doc=r.win.document;i._engine.initWin&&i._engine.initWin(r.win)};var ei=function(n){var e,u,f,o,t;if(i.vml){e=/^\s+|\s+$/g;try{f=new ActiveXObject("htmlfile");f.write("<body>");f.close();u=f.body}catch(s){u=createPopup().document.body}o=u.createTextRange();ei=it(function(n){try{u.style.color=b(n).replace(e,d);var t=o.queryCommandValue("ForeColor");return t=(t&255)<<16|t&65280|(t&16711680)>>>16,"#"+("000000"+t.toString(16)).slice(-6)}catch(i){return"none"}})}else t=r.doc.createElement("i"),t.title="Raphaël Colour Picker",t.style.display="none",r.doc.body.appendChild(t),ei=it(function(n){return t.style.color=n,r.doc.defaultView.getComputedStyle(t,d).getPropertyValue("color")});return ei(n)},ef=function(){return"hsb("+[this.h,this.s,this.b]+")"},of=function(){return"hsl("+[this.h,this.s,this.l]+")"},yr=function(){return this.hex},pr=function(n,t,r){if(t==null&&i.is(n,"object")&&"r"in n&&"g"in n&&"b"in n&&(r=n.b,t=n.g,n=n.r),t==null&&i.is(n,ti)){var u=i.getRGB(n);n=u.r;t=u.g;r=u.b}return(n>1||t>1||r>1)&&(n/=255,t/=255,r/=255),[n,t,r]},wr=function(n,t,r,u){n*=255;t*=255;r*=255;var f={r:n,g:t,b:r,hex:i.rgb(n,t,r),toString:yr};return i.is(u,"finite")&&(f.opacity=u),f};i.color=function(n){var t;return i.is(n,"object")&&"h"in n&&"s"in n&&"b"in n?(t=i.hsb2rgb(n),n.r=t.r,n.g=t.g,n.b=t.b,n.hex=t.hex):i.is(n,"object")&&"h"in n&&"s"in n&&"l"in n?(t=i.hsl2rgb(n),n.r=t.r,n.g=t.g,n.b=t.b,n.hex=t.hex):(i.is(n,"string")&&(n=i.getRGB(n)),i.is(n,"object")&&"r"in n&&"g"in n&&"b"in n?(t=i.rgb2hsl(n),n.h=t.h,n.s=t.s,n.l=t.l,t=i.rgb2hsb(n),n.v=t.b):(n={hex:"none"},n.r=n.g=n.b=n.h=n.s=n.v=n.l=-1)),n.toString=yr,n};i.hsb2rgb=function(n,t,i,r){this.is(n,"object")&&"h"in n&&"s"in n&&"b"in n&&(i=n.b,t=n.s,n=n.h,r=n.o);n*=360;var e,o,s,f,u;return n=n%360/60,u=i*t,f=u*(1-y(n%2-1)),e=o=s=i-u,n=~~n,e+=[u,f,0,0,f,u][n],o+=[f,u,u,f,0,0][n],s+=[0,0,f,u,u,f][n],wr(e,o,s,r)};i.hsl2rgb=function(n,t,i,r){this.is(n,"object")&&"h"in n&&"s"in n&&"l"in n&&(i=n.l,t=n.s,n=n.h);(n>1||t>1||i>1)&&(n/=360,t/=100,i/=100);n*=360;var e,o,s,f,u;return n=n%360/60,u=2*t*(i<.5?i:1-i),f=u*(1-y(n%2-1)),e=o=s=i-u/2,n=~~n,e+=[u,f,0,0,f,u][n],o+=[f,u,u,f,0,0][n],s+=[0,0,f,u,u,f][n],wr(e,o,s,r)};i.rgb2hsb=function(n,t,i){i=pr(n,t,i);n=i[0];t=i[1];i=i[2];var f,e,u,r;return u=o(n,t,i),r=u-l(n,t,i),f=r==0?null:u==n?(t-i)/r:u==t?(i-n)/r+2:(n-t)/r+4,f=(f+360)%6/6,e=r==0?0:r/u,{h:f,s:e,b:u,toString:ef}};i.rgb2hsl=function(n,t,i){i=pr(n,t,i);n=i[0];t=i[1];i=i[2];var e,h,u,f,s,r;return f=o(n,t,i),s=l(n,t,i),r=f-s,e=r==0?null:f==n?(t-i)/r:f==t?(i-n)/r+2:(n-t)/r+4,e=(e+360)%6/6,u=(f+s)/2,h=r==0?0:u<.5?r/(2*u):r/(2-2*u),{h:e,s:h,l:u,toString:of}};i._path2string=function(){return this.join(",").replace(du,"$1")};hf=i._preload=function(n,t){var i=r.doc.createElement("img");i.style.cssText="position:absolute;left:-9999em;top:-9999em";i.onload=function(){t.call(this);this.onload=null;r.doc.body.removeChild(this)};i.onerror=function(){r.doc.body.removeChild(this)};r.doc.body.appendChild(i);i.src=n};i.getRGB=it(function(n){if(!n||!!((n=b(n)).indexOf("-")+1))return{r:-1,g:-1,b:-1,hex:"none",error:1,toString:oi};if(n=="none")return{r:-1,g:-1,b:-1,hex:"none",toString:oi};ku[a](n.toLowerCase().substring(0,2))||n.charAt()=="#"||(n=ei(n));var u,f,e,o,s,t,r=n.match(yu);return r?(r[2]&&(e=st(r[2].substring(5),16),f=st(r[2].substring(3,5),16),u=st(r[2].substring(1,3),16)),r[3]&&(e=st((s=r[3].charAt(3))+s,16),f=st((s=r[3].charAt(2))+s,16),u=st((s=r[3].charAt(1))+s,16)),r[4]&&(t=r[4][ut](bi),u=h(t[0]),t[0].slice(-1)=="%"&&(u*=2.55),f=h(t[1]),t[1].slice(-1)=="%"&&(f*=2.55),e=h(t[2]),t[2].slice(-1)=="%"&&(e*=2.55),r[1].toLowerCase().slice(0,4)=="rgba"&&(o=h(t[3])),t[3]&&t[3].slice(-1)=="%"&&(o/=100)),r[5])?(t=r[5][ut](bi),u=h(t[0]),t[0].slice(-1)=="%"&&(u*=2.55),f=h(t[1]),t[1].slice(-1)=="%"&&(f*=2.55),e=h(t[2]),t[2].slice(-1)=="%"&&(e*=2.55),(t[0].slice(-3)=="deg"||t[0].slice(-1)=="°")&&(u/=360),r[1].toLowerCase().slice(0,4)=="hsba"&&(o=h(t[3])),t[3]&&t[3].slice(-1)=="%"&&(o/=100),i.hsb2rgb(u,f,e,o)):r[6]?(t=r[6][ut](bi),u=h(t[0]),t[0].slice(-1)=="%"&&(u*=2.55),f=h(t[1]),t[1].slice(-1)=="%"&&(f*=2.55),e=h(t[2]),t[2].slice(-1)=="%"&&(e*=2.55),(t[0].slice(-3)=="deg"||t[0].slice(-1)=="°")&&(u/=360),r[1].toLowerCase().slice(0,4)=="hsla"&&(o=h(t[3])),t[3]&&t[3].slice(-1)=="%"&&(o/=100),i.hsl2rgb(u,f,e,o)):(r={r:u,g:f,b:e,toString:oi},r.hex="#"+(16777216|e|f<<8|u<<16).toString(16).slice(1),i.is(o,"finite")&&(r.opacity=o),r):{r:-1,g:-1,b:-1,hex:"none",error:1,toString:oi}},i);i.hsb=it(function(n,t,r){return i.hsb2rgb(n,t,r).hex});i.hsl=it(function(n,t,r){return i.hsl2rgb(n,t,r).hex});i.rgb=it(function(n,t,i){return"#"+(16777216|i|t<<8|n<<16).toString(16).slice(1)});i.getColor=function(n){var t=this.getColor.start=this.getColor.start||{h:0,s:1,b:n||.75},i=this.hsb2rgb(t.h,t.s,t.b);return t.h+=.075,t.h>1&&(t.h=0,t.s-=.2,t.s<=0&&(this.getColor.start={h:0,s:1,b:t.b})),i.hex};i.getColor.reset=function(){delete this.start};i.parsePathString=function(n){var r,u,t;return n?(r=et(n),r.arr)?rt(r.arr):(u={a:7,c:6,h:1,l:2,m:2,r:4,q:4,s:4,t:2,v:1,z:0},t=[],i.is(n,tt)&&i.is(n[0],tt)&&(t=rt(n)),t.length||b(n).replace(gu,function(n,i,r){var f=[],e=i.toLowerCase();if(r.replace(ar,function(n,t){t&&f.push(+t)}),e=="m"&&f.length>2&&(t.push([i][s](f.splice(0,2))),e="l",i=i=="m"?"l":"L"),e=="r")t.push([i][s](f));else while(f.length>=u[e])if(t.push([i][s](f.splice(0,u[e]))),!u[e])break}),t.toString=i._path2string,r.arr=rt(t),t):null};i.parseTransformString=it(function(n){if(!n)return null;var t=[];return i.is(n,tt)&&i.is(n[0],tt)&&(t=rt(n)),t.length||b(n).replace(nf,function(n,i,r){var u=[],f=ni.call(i);r.replace(ar,function(n,t){t&&u.push(+t)});t.push([i][s](u))}),t.toString=i._path2string,t});et=function(n){var t=et.ps=et.ps||{};return t[n]?t[n].sleep=100:t[n]={sleep:100},setTimeout(function(){for(var i in t)t[a](i)&&i!=n&&(t[i].sleep--,t[i].sleep||delete t[i])}),t[n]};i.findDotsAtSegment=function(n,t,i,r,f,e,o,s,h){var c=1-h,w=nt(c,3),b=nt(c,2),l=h*h,d=l*h,tt=w*n+b*3*h*i+c*3*h*h*f+d*o,it=w*t+b*3*h*r+c*3*h*h*e+d*s,a=n+2*h*(i-n)+l*(f-2*i+n),v=t+2*h*(r-t)+l*(e-2*r+t),y=i+2*h*(f-i)+l*(o-2*f+i),p=r+2*h*(e-r)+l*(s-2*e+r),rt=c*n+h*i,ut=c*t+h*r,ft=c*f+h*o,et=c*e+h*s,g=90-u.atan2(a-y,v-p)*180/k;return(a>y||v<p)&&(g+=180),{x:tt,y:it,m:{x:a,y:v},n:{x:y,y:p},start:{x:rt,y:ut},end:{x:ft,y:et},alpha:g}};i.bezierBBox=function(n,t,r,u,f,e,o,s){i.is(n,"array")||(n=[n,t,r,u,f,e,o,s]);var h=tu.apply(null,n);return{x:h.min.x,y:h.min.y,x2:h.max.x,y2:h.max.y,width:h.max.x-h.min.x,height:h.max.y-h.min.y}};i.isPointInsideBBox=function(n,t,i){return t>=n.x&&t<=n.x2&&i>=n.y&&i<=n.y2};i.isBBoxIntersect=function(n,t){var r=i.isPointInsideBBox;return r(t,n.x,n.y)||r(t,n.x2,n.y)||r(t,n.x,n.y2)||r(t,n.x2,n.y2)||r(n,t.x,t.y)||r(n,t.x2,t.y)||r(n,t.x,t.y2)||r(n,t.x2,t.y2)||(n.x<t.x2&&n.x>t.x||t.x<n.x2&&t.x>n.x)&&(n.y<t.y2&&n.y>t.y||t.y<n.y2&&t.y>n.y)};i.pathIntersection=function(n,t){return gi(n,t)};i.pathIntersectionNumber=function(n,t){return gi(n,t,1)};i.isPointInsidePath=function(n,t,r){var u=i.pathBBox(n);return i.isPointInsideBBox(u,t,r)&&gi(n,[["M",t,r],["H",u.x2+10]],1)%2==1};i._removedFactory=function(n){return function(){t("raphael.log",null,"Raphaël: you are calling to method “"+n+"” of removed object",n)}};var nr=i.pathBBox=function(n){var c=et(n),h,p,u;if(c.bbox)return pt(c.bbox);if(!n)return{x:0,y:0,width:0,height:0,x2:0,y2:0};n=wt(n);var f=0,e=0,i=[],r=[],t;for(h=0,p=n.length;h<p;h++)t=n[h],t[0]=="M"?(f=t[1],e=t[2],i.push(f),r.push(e)):(u=tu(f,e,t[1],t[2],t[3],t[4],t[5],t[6]),i=i[s](u.min.x,u.max.x),r=r[s](u.min.y,u.max.y),f=t[5],e=t[6]);var a=l[v](0,i),y=l[v](0,r),w=o[v](0,i),b=o[v](0,r),k=w-a,d=b-y,g={x:a,y:y,x2:w,y2:b,width:k,height:d,cx:a+k/2,cy:y+d/2};return c.bbox=pt(g),g},rt=function(n){var t=pt(n);return t.toString=i._path2string,t},af=i._pathToRelative=function(n){var v=et(n),u,p,f,t,s,w,h,b,c;if(v.rel)return rt(v.rel);i.is(n,tt)&&i.is(n&&n[0],tt)||(n=i.parsePathString(n));var r=[],o=0,e=0,l=0,a=0,y=0;for(n[0][0]=="M"&&(o=n[0][1],e=n[0][2],l=o,a=e,y++,r.push(["M",o,e])),u=y,p=n.length;u<p;u++){if(f=r[u]=[],t=n[u],t[0]!=ni.call(t[0])){f[0]=ni.call(t[0]);switch(f[0]){case"a":f[1]=t[1];f[2]=t[2];f[3]=t[3];f[4]=t[4];f[5]=t[5];f[6]=+(t[6]-o).toFixed(3);f[7]=+(t[7]-e).toFixed(3);break;case"v":f[1]=+(t[1]-e).toFixed(3);break;case"m":l=t[1];a=t[2];default:for(s=1,w=t.length;s<w;s++)f[s]=+(t[s]-(s%2?o:e)).toFixed(3)}}else for(f=r[u]=[],t[0]=="m"&&(l=t[1]+o,a=t[2]+e),h=0,b=t.length;h<b;h++)r[u][h]=t[h];c=r[u].length;switch(r[u][0]){case"z":o=l;e=a;break;case"h":o+=+r[u][c-1];break;case"v":e+=+r[u][c-1];break;default:o+=+r[u][c-2];e+=+r[u][c-1]}}return r.toString=i._path2string,v.rel=rt(r),r},dr=i._pathToAbsolute=function(n){var p=et(n),w,t,r,v,k,h,e,y,c,d;if(p.abs)return rt(p.abs);if(i.is(n,tt)&&i.is(n&&n[0],tt)||(n=i.parsePathString(n)),!n||!n.length)return[["M",0,0]];var o=[],u=0,f=0,l=0,a=0,b=0;for(n[0][0]=="M"&&(u=+n[0][1],f=+n[0][2],l=u,a=f,b++,o[0]=["M",u,f]),w=n.length==3&&n[0][0]=="M"&&n[1][0].toUpperCase()=="R"&&n[2][0].toUpperCase()=="Z",v=b,k=n.length;v<k;v++){if(o.push(t=[]),r=n[v],r[0]!=lr.call(r[0])){t[0]=lr.call(r[0]);switch(t[0]){case"A":t[1]=r[1];t[2]=r[2];t[3]=r[3];t[4]=r[4];t[5]=r[5];t[6]=+(r[6]+u);t[7]=+(r[7]+f);break;case"V":t[1]=+r[1]+f;break;case"H":t[1]=+r[1]+u;break;case"R":for(h=[u,f][s](r.slice(1)),e=2,y=h.length;e<y;e++)h[e]=+h[e]+u,h[++e]=+h[e]+f;o.pop();o=o[s](br(h,w));break;case"M":l=+r[1]+u;a=+r[2]+f;default:for(e=1,y=r.length;e<y;e++)t[e]=+r[e]+(e%2?u:f)}}else if(r[0]=="R")h=[u,f][s](r.slice(1)),o.pop(),o=o[s](br(h,w)),t=["R"][s](r.slice(-2));else for(c=0,d=r.length;c<d;c++)t[c]=r[c];switch(t[0]){case"Z":u=l;f=a;break;case"H":u=t[1];break;case"V":f=t[1];break;case"M":l=t[t.length-2];a=t[t.length-1];default:u=t[t.length-2];f=t[t.length-1]}}return o.toString=i._path2string,p.abs=rt(o),o},si=function(n,t,i,r){return[n,t,i,r,i,r]},gr=function(n,t,i,r,u,f){var e=1/3,o=2/3;return[e*n+o*i,e*t+o*r,e*u+o*i,e*f+o*r,u,f]},nu=function(n,t,i,r,f,e,o,h,c,l){var at=k*120/180,et=k/180*(+f||0),p=[],g,ot=it(function(n,t,i){var r=n*u.cos(i)-t*u.sin(i),f=n*u.sin(i)+t*u.cos(i);return{x:r,y:f}}),st,lt,w,gt;if(l)v=l[0],a=l[1],rt=l[2],ft=l[3];else{g=ot(n,t,-et);n=g.x;t=g.y;g=ot(h,c,-et);h=g.x;c=g.y;var oi=u.cos(k/180*f),si=u.sin(k/180*f),b=(n-h)/2,d=(t-c)/2,tt=b*b/(i*i)+d*d/(r*r);tt>1&&(tt=u.sqrt(tt),i=tt*i,r=tt*r);var ht=i*i,ct=r*r,vt=(e==o?-1:1)*u.sqrt(y((ht*ct-ht*d*d-ct*b*b)/(ht*d*d+ct*b*b))),rt=vt*i*d/r+(n+h)/2,ft=vt*-r*b/i+(t+c)/2,v=u.asin(((t-ft)/r).toFixed(9)),a=u.asin(((c-ft)/r).toFixed(9));v=n<rt?k-v:v;a=h<rt?k-a:a;v<0&&(v=k*2+v);a<0&&(a=k*2+a);o&&v>a&&(v=v-k*2);!o&&a>v&&(a=a-k*2)}if(st=a-v,y(st)>at){var ni=a,ti=h,ii=c;a=v+at*(o&&a>v?1:-1);h=rt+i*u.cos(a);c=ft+r*u.sin(a);p=nu(h,c,i,r,f,0,o,ti,ii,[a,ni,rt,ft])}st=a-v;var ri=u.cos(v),ui=u.sin(v),fi=u.cos(a),ei=u.sin(a),yt=u.tan(st/4),pt=4/3*i*yt,wt=4/3*r*yt,bt=[n,t],nt=[n+pt*ui,t-wt*ri],kt=[h+pt*ei,c-wt*fi],dt=[h,c];if(nt[0]=2*bt[0]-nt[0],nt[1]=2*bt[1]-nt[1],l)return[nt,kt,dt][s](p);for(p=[nt,kt,dt][s](p).join()[ut](","),lt=[],w=0,gt=p.length;w<gt;w++)lt[w]=w%2?ot(p[w-1],p[w],et).y:ot(p[w],p[w+1],et).x;return lt},hi=function(n,t,i,r,u,f,e,o,s){var h=1-s;return{x:nt(h,3)*n+nt(h,2)*3*s*i+h*3*s*s*u+nt(s,3)*e,y:nt(h,3)*t+nt(h,2)*3*s*r+h*3*s*s*f+nt(s,3)*o}},tu=it(function(n,t,i,r,f,e,s,h){var b=f-2*i+n-(s-2*f+i),c=2*(i-n)-2*(f-i),g=n-i,p=(-c+u.sqrt(c*c-4*b*g))/2/b,w=(-c-u.sqrt(c*c-4*b*g))/2/b,k=[t,h],d=[n,s],a;return y(p)>"1e12"&&(p=.5),y(w)>"1e12"&&(w=.5),p>0&&p<1&&(a=hi(n,t,i,r,f,e,s,h,p),d.push(a.x),k.push(a.y)),w>0&&w<1&&(a=hi(n,t,i,r,f,e,s,h,w),d.push(a.x),k.push(a.y)),b=e-2*r+t-(h-2*e+r),c=2*(r-t)-2*(e-r),g=t-r,p=(-c+u.sqrt(c*c-4*b*g))/2/b,w=(-c-u.sqrt(c*c-4*b*g))/2/b,y(p)>"1e12"&&(p=.5),y(w)>"1e12"&&(w=.5),p>0&&p<1&&(a=hi(n,t,i,r,f,e,s,h,p),d.push(a.x),k.push(a.y)),w>0&&w<1&&(a=hi(n,t,i,r,f,e,s,h,w),d.push(a.x),k.push(a.y)),{min:{x:l[v](0,d),y:l[v](0,k)},max:{x:o[v](0,d),y:o[v](0,k)}}}),wt=i._path2curve=it(function(n,t){var w=!t&&et(n),r,a;if(!t&&w.curve)return rt(w.curve);var u=dr(n),i=t&&dr(t),f={x:0,y:0,bx:0,by:0,X:0,Y:0,qx:null,qy:null},e={x:0,y:0,bx:0,by:0,X:0,Y:0,qx:null,qy:null},b=function(n,t,i){var r,u;if(!n)return["C",t.x,t.y,t.x,t.y,t.x,t.y];n[0]in{T:1,Q:1}||(t.qx=t.qy=null);switch(n[0]){case"M":t.X=n[1];t.Y=n[2];break;case"A":n=["C"][s](nu[v](0,[t.x,t.y][s](n.slice(1))));break;case"S":i=="C"||i=="S"?(r=t.x*2-t.bx,u=t.y*2-t.by):(r=t.x,u=t.y);n=["C",r,u][s](n.slice(1));break;case"T":i=="Q"||i=="T"?(t.qx=t.x*2-t.qx,t.qy=t.y*2-t.qy):(t.qx=t.x,t.qy=t.y);n=["C"][s](gr(t.x,t.y,t.qx,t.qy,n[1],n[2]));break;case"Q":t.qx=n[1];t.qy=n[2];n=["C"][s](gr(t.x,t.y,n[1],n[2],n[3],n[4]));break;case"L":n=["C"][s](si(t.x,t.y,n[1],n[2]));break;case"H":n=["C"][s](si(t.x,t.y,n[1],t.y));break;case"V":n=["C"][s](si(t.x,t.y,t.x,n[1]));break;case"Z":n=["C"][s](si(t.x,t.y,t.X,t.Y))}return n},k=function(n,t){if(n[t].length>7){n[t].shift();for(var r=n[t];r.length;)n.splice(t++,0,["C"][s](r.splice(0,6)));n.splice(t,1);a=o(u.length,i&&i.length||0)}},d=function(n,t,r,f,e){n&&t&&n[e][0]=="M"&&t[e][0]!="M"&&(t.splice(e,0,["M",f.x,f.y]),r.bx=0,r.by=0,r.x=n[e][1],r.y=n[e][2],a=o(u.length,i&&i.length||0))};for(r=0,a=o(u.length,i&&i.length||0);r<a;r++){u[r]=b(u[r],f);k(u,r);i&&(i[r]=b(i[r],e));i&&k(i,r);d(u,i,f,e,r);d(i,u,e,f,r);var c=u[r],l=i&&i[r],y=c.length,p=i&&l.length;f.x=c[y-2];f.y=c[y-1];f.bx=h(c[y-4])||f.x;f.by=h(c[y-3])||f.y;e.bx=i&&(h(l[p-4])||e.x);e.by=i&&(h(l[p-3])||e.y);e.x=i&&l[p-2];e.y=i&&l[p-1]}return i||(w.curve=rt(u)),i?[u,i]:u},null,rt),fe=i._parseDots=it(function(n){for(var f,s,c,e,u,l,r=[],t=0,o=n.length;t<o;t++){if(f={},s=n[t].match(/^([^:]*):?([\d\.]*)/),f.color=i.getRGB(s[1]),f.color.error)return null;f.color=f.color.hex;s[2]&&(f.offset=s[2]+"%");r.push(f)}for(t=1,o=r.length-1;t<o;t++)if(!r[t].offset){for(c=h(r[t-1].offset||0),e=0,u=t+1;u<o;u++)if(r[u].offset){e=r[u].offset;break}for(e||(e=100,u=o),e=h(e),l=(e-c)/(u-t+1);t<u;t++)c+=l,r[t].offset=c+"%"}return r}),ci=i._tear=function(n,t){n==t.top&&(t.top=n.prev);n==t.bottom&&(t.bottom=n.next);n.next&&(n.next.prev=n.prev);n.prev&&(n.prev.next=n.next)},ee=i._tofront=function(n,t){t.top!==n&&(ci(n,t),n.next=null,n.prev=t.top,t.top.next=n,t.top=n)},oe=i._toback=function(n,t){t.bottom!==n&&(ci(n,t),n.next=t.bottom,n.prev=null,t.bottom.prev=n,t.bottom=n)},se=i._insertafter=function(n,t,i){ci(n,i);t==i.top&&(i.top=n);t.next&&(t.next.prev=n);n.next=t.next;n.prev=t;t.next=n},he=i._insertbefore=function(n,t,i){ci(n,i);t==i.bottom&&(i.bottom=n);t.prev&&(t.prev.next=n);n.prev=t.prev;t.prev=n;n.next=t},vf=i.toMatrix=function(n,t){var r=nr(n),i={_:{transform:d},getBBox:function(){return r}};return iu(i,t),i.matrix},ce=i.transformPath=function(n,t){return ki(n,vf(n,t))},iu=i._extractTransform=function(n,t){var w,tt;if(t==null)return n._.transform;t=b(t).replace(/\.{3}|\u2026/g,n._.transform||d);var a=i.parseTransformString(t),v=0,g=0,nt=0,y=1,p=1,e=n._,u=new ht;if(e.transform=a||[],a)for(w=0,tt=a.length;w<tt;w++){var r=a[w],o=r.length,l=b(r[0]).toLowerCase(),k=r[0]!=l,s=k?u.invert():0,it,rt,h,c,f;l=="t"&&o==3?k?(it=s.x(0,0),rt=s.y(0,0),h=s.x(r[1],r[2]),c=s.y(r[1],r[2]),u.translate(h-it,c-rt)):u.translate(r[1],r[2]):l=="r"?o==2?(f=f||n.getBBox(1),u.rotate(r[1],f.x+f.width/2,f.y+f.height/2),v+=r[1]):o==4&&(k?(h=s.x(r[2],r[3]),c=s.y(r[2],r[3]),u.rotate(r[1],h,c)):u.rotate(r[1],r[2],r[3]),v+=r[1]):l=="s"?o==2||o==3?(f=f||n.getBBox(1),u.scale(r[1],r[o-1],f.x+f.width/2,f.y+f.height/2),y*=r[1],p*=r[o-1]):o==5&&(k?(h=s.x(r[3],r[4]),c=s.y(r[3],r[4]),u.scale(r[1],r[2],h,c)):u.scale(r[1],r[2],r[3],r[4]),y*=r[1],p*=r[2]):l=="m"&&o==7&&u.add(r[1],r[2],r[3],r[4],r[5],r[6]);e.dirtyT=1;n.matrix=u}n.matrix=u;e.sx=y;e.sy=p;e.deg=v;e.dx=g=u.e;e.dy=nt=u.f;y==1&&p==1&&!v&&e.bbox?(e.bbox.x+=+g,e.bbox.y+=+nt):e.dirtyT=1},ru=function(n){var t=n[0];switch(t.toLowerCase()){case"t":return[t,0,0];case"m":return[t,1,0,0,1,0,0];case"r":return n.length==4?[t,0,n[2],n[3]]:[t,0];case"s":return n.length==5?[t,1,1,n[3],n[4]]:n.length==3?[t,1,1]:[t,1]}},yf=i._equaliseTransform=function(n,t){t=b(t).replace(/\.{3}|\u2026/g,n);n=i.parseTransformString(n)||[];t=i.parseTransformString(t)||[];for(var l=o(n.length,t.length),s=[],h=[],u=0,f,c,r,e;u<l;u++){if(r=n[u]||ru(t[u]),e=t[u]||ru(r),r[0]!=e[0]||r[0].toLowerCase()=="r"&&(r[2]!=e[2]||r[3]!=e[3])||r[0].toLowerCase()=="s"&&(r[3]!=e[3]||r[4]!=e[4]))return;for(s[u]=[],h[u]=[],f=0,c=o(r.length,e.length);f<c;f++)f in r&&(s[u][f]=r[f]),f in e&&(h[u][f]=e[f])}return{from:s,to:h}};i._getContainer=function(n,t,u,f){var e;if(e=f==null&&!i.is(n,"object")?r.doc.getElementById(n):n,e!=null)return e.tagName?t==null?{container:e,width:e.style.pixelWidth||e.offsetWidth,height:e.style.pixelHeight||e.offsetHeight}:{container:e,width:t,height:u}:{container:1,x:n,y:t,width:u,height:f}};i.pathToRelative=af;i._engine={};i.path2curve=wt;i.matrix=function(n,t,i,r,u,f){return new ht(n,t,i,r,u,f)},function(n){function t(n){return n[0]*n[0]+n[1]*n[1]}function r(n){var i=u.sqrt(t(n));n[0]&&(n[0]/=i);n[1]&&(n[1]/=i)}n.add=function(n,t,i,r,u,f){var e=[[],[],[]],a=[[this.a,this.c,this.e],[this.b,this.d,this.f],[0,0,1]],l=[[n,i,u],[t,r,f],[0,0,1]],o,s,h,c;for(n&&n instanceof ht&&(l=[[n.a,n.c,n.e],[n.b,n.d,n.f],[0,0,1]]),o=0;o<3;o++)for(s=0;s<3;s++){for(c=0,h=0;h<3;h++)c+=a[o][h]*l[h][s];e[o][s]=c}this.a=e[0][0];this.b=e[1][0];this.c=e[0][1];this.d=e[1][1];this.e=e[0][2];this.f=e[1][2]};n.invert=function(){var n=this,t=n.a*n.d-n.b*n.c;return new ht(n.d/t,-n.b/t,-n.c/t,n.a/t,(n.c*n.f-n.d*n.e)/t,(n.b*n.e-n.a*n.f)/t)};n.clone=function(){return new ht(this.a,this.b,this.c,this.d,this.e,this.f)};n.translate=function(n,t){this.add(1,0,0,1,n,t)};n.scale=function(n,t,i,r){t==null&&(t=n);(i||r)&&this.add(1,0,0,1,i,r);this.add(n,0,0,t,0,0);(i||r)&&this.add(1,0,0,1,-i,-r)};n.rotate=function(n,t,r){n=i.rad(n);t=t||0;r=r||0;var f=+u.cos(n).toFixed(9),e=+u.sin(n).toFixed(9);this.add(f,e,-e,f,t,r);this.add(1,0,0,1,-t,-r)};n.x=function(n,t){return n*this.a+t*this.c+this.e};n.y=function(n,t){return n*this.b+t*this.d+this.f};n.get=function(n){return+this[b.fromCharCode(97+n)].toFixed(4)};n.toString=function(){return i.svg?"matrix("+[this.get(0),this.get(1),this.get(2),this.get(3),this.get(4),this.get(5)].join()+")":[this.get(0),this.get(2),this.get(1),this.get(3),0,0].join()};n.toFilter=function(){return"progid:DXImageTransform.Microsoft.Matrix(M11="+this.get(0)+", M12="+this.get(2)+", M21="+this.get(1)+", M22="+this.get(3)+", Dx="+this.get(4)+", Dy="+this.get(5)+", sizingmethod='auto expand')"};n.offset=function(){return[this.e.toFixed(4),this.f.toFixed(4)]};n.split=function(){var n={},f,e,o;return n.dx=this.e,n.dy=this.f,f=[[this.a,this.c],[this.b,this.d]],n.scalex=u.sqrt(t(f[0])),r(f[0]),n.shear=f[0][0]*f[1][0]+f[0][1]*f[1][1],f[1]=[f[1][0]-f[0][0]*n.shear,f[1][1]-f[0][1]*n.shear],n.scaley=u.sqrt(t(f[1])),r(f[1]),n.shear/=n.scaley,e=-f[0][1],o=f[1][1],o<0?(n.rotate=i.deg(u.acos(o)),e<0&&(n.rotate=360-n.rotate)):n.rotate=i.deg(u.asin(e)),n.isSimple=!+n.shear.toFixed(9)&&(n.scalex.toFixed(9)==n.scaley.toFixed(9)||!n.rotate),n.isSuperSimple=!+n.shear.toFixed(9)&&n.scalex.toFixed(9)==n.scaley.toFixed(9)&&!n.rotate,n.noRotation=!+n.shear.toFixed(9)&&!n.rotate,n};n.toTransformString=function(n){var t=n||this[ut]();return t.isSimple?(t.scalex=+t.scalex.toFixed(4),t.scaley=+t.scaley.toFixed(4),t.rotate=+t.rotate.toFixed(4),(t.dx||t.dy?"t"+[t.dx,t.dy]:d)+(t.scalex!=1||t.scaley!=1?"s"+[t.scalex,t.scaley,0,0]:d)+(t.rotate?"r"+[t.rotate,0,0]:d)):"m"+[this.get(0),this.get(1),this.get(2),this.get(3),this.get(4),this.get(5)]}}(ht.prototype);bt=navigator.userAgent.match(/Version\/(.*?)\s/)||navigator.userAgent.match(/Chrome\/(\d+)/);c.safari=navigator.vendor=="Apple Computer, Inc."&&(bt&&bt[1]<4||navigator.platform.slice(0,2)=="iP")||navigator.vendor=="Google Inc."&&bt&&bt[1]<8?function(){var n=this.rect(-99,-99,this.width+99,this.height+99).attr({stroke:"none"});setTimeout(function(){n.remove()})}:rf;var pf=function(){this.returnValue=!1},wf=function(){return this.originalEvent.preventDefault()},bf=function(){this.cancelBubble=!0},kf=function(){return this.originalEvent.stopPropagation()},uu=function(n){var t=r.doc.documentElement.scrollTop||r.doc.body.scrollTop,i=r.doc.documentElement.scrollLeft||r.doc.body.scrollLeft;return{x:n.clientX+i,y:n.clientY+t}},df=function(){return r.doc.addEventListener?function(n,t,i,r){var u=function(n){var t=uu(n);return i.call(r,n,t.x,t.y)},f;return n.addEventListener(t,u,!1),dt&&gt[t]&&(f=function(t){for(var f=uu(t),e=t,u=0,o=t.targetTouches&&t.targetTouches.length;u<o;u++)if(t.targetTouches[u].target==n){t=t.targetTouches[u];t.originalEvent=e;t.preventDefault=wf;t.stopPropagation=kf;break}return i.call(r,t,f.x,f.y)},n.addEventListener(gt[t],f,!1)),function(){return n.removeEventListener(t,u,!1),dt&&gt[t]&&n.removeEventListener(gt[t],u,!1),!0}}:r.doc.attachEvent?function(n,t,i,u){var f=function(n){n=n||r.win.event;var t=r.doc.documentElement.scrollTop||r.doc.body.scrollTop,f=r.doc.documentElement.scrollLeft||r.doc.body.scrollLeft,e=n.clientX+f,o=n.clientY+t;return n.preventDefault=n.preventDefault||pf,n.stopPropagation=n.stopPropagation||bf,i.call(u,n,e,o)};return n.attachEvent("on"+t,f),function(){return n.detachEvent("on"+t,f),!0}}:void 0}(),ot=[],tr=function(n){for(var f=n.clientX,e=n.clientY,v=r.doc.documentElement.scrollTop||r.doc.body.scrollTop,y=r.doc.documentElement.scrollLeft||r.doc.body.scrollLeft,i,l=ot.length,s,o;l--;){if(i=ot[l],dt&&n.touches){for(s=n.touches.length;s--;)if(o=n.touches[s],o.identifier==i.el._drag.id){f=o.clientX;e=o.clientY;(n.originalEvent?n.originalEvent:n).preventDefault();break}}else n.preventDefault();var u=i.el.node,h,a=u.nextSibling,c=u.parentNode,p=u.style.display;r.win.opera&&c.removeChild(u);u.style.display="none";h=i.el.paper.getElementByPoint(f,e);u.style.display=p;r.win.opera&&(a?c.insertBefore(u,a):c.appendChild(u));h&&t("raphael.drag.over."+i.el.id,i.el,h);f+=y;e+=v;t("raphael.drag.move."+i.el.id,i.move_scope||i.el,f-i.el._drag.x,e-i.el._drag.y,f,e,n)}},ir=function(n){i.unmousemove(tr).unmouseup(ir);for(var u=ot.length,r;u--;)r=ot[u],r.el._drag={},t("raphael.drag.end."+r.el.id,r.end_scope||r.start_scope||r.move_scope||r.el,n);ot=[]},e=i.el={};for(rr=cr.length;rr--;)(function(n){i[n]=e[n]=function(t,u){return i.is(t,"function")&&(this.events=this.events||[],this.events.push({name:n,f:t,unbind:df(this.shape||this.node||r.doc,n,t,u||this)})),this};i["un"+n]=e["un"+n]=function(t){for(var r=this.events||[],u=r.length;u--;)r[u].name==n&&(i.is(t,"undefined")||r[u].f==t)&&(r[u].unbind(),r.splice(u,1),r.length||delete this.events);return this}})(cr[rr]);e.data=function(n,r){var u=at[this.id]=at[this.id]||{},f;if(arguments.length==0)return u;if(arguments.length==1){if(i.is(n,"object")){for(f in n)n[a](f)&&this.data(f,n[f]);return this}return t("raphael.data.get."+this.id,this,u[n],n),u[n]}return u[n]=r,t("raphael.data.set."+this.id,this,r,n),this};e.removeData=function(n){return n==null?at[this.id]={}:at[this.id]&&delete at[this.id][n],this};e.getData=function(){return pt(at[this.id]||{})};e.hover=function(n,t,i,r){return this.mouseover(n,i).mouseout(t,r||i)};e.unhover=function(n,t){return this.unmouseover(n).unmouseout(t)};ct=[];e.drag=function(n,u,f,e,o,s){function h(h){var l,c;(h.originalEvent||h).preventDefault();var a=h.clientX,v=h.clientY,y=r.doc.documentElement.scrollTop||r.doc.body.scrollTop,p=r.doc.documentElement.scrollLeft||r.doc.body.scrollLeft;if(this._drag.id=h.identifier,dt&&h.touches)for(l=h.touches.length;l--;)if(c=h.touches[l],this._drag.id=c.identifier,c.identifier==this._drag.id){a=c.clientX;v=c.clientY;break}this._drag.x=a+p;this._drag.y=v+y;ot.length||i.mousemove(tr).mouseup(ir);ot.push({el:this,move_scope:e,start_scope:o,end_scope:s});u&&t.on("raphael.drag.start."+this.id,u);n&&t.on("raphael.drag.move."+this.id,n);f&&t.on("raphael.drag.end."+this.id,f);t("raphael.drag.start."+this.id,o||e||this,h.clientX+p,h.clientY+y,h)}return this._drag={},ct.push({el:this,start:h}),this.mousedown(h),this};e.onDragOver=function(n){n?t.on("raphael.drag.over."+this.id,n):t.unbind("raphael.drag.over."+this.id)};e.undrag=function(){for(var n=ct.length;n--;)ct[n].el==this&&(this.unmousedown(ct[n].start),ct.splice(n,1),t.unbind("raphael.drag.*."+this.id));ct.length||i.unmousemove(tr).unmouseup(ir);ot=[]};c.circle=function(n,t,r){var u=i._engine.circle(this,n||0,t||0,r||0);return this.__set__&&this.__set__.push(u),u};c.rect=function(n,t,r,u,f){var e=i._engine.rect(this,n||0,t||0,r||0,u||0,f||0);return this.__set__&&this.__set__.push(e),e};c.ellipse=function(n,t,r,u){var f=i._engine.ellipse(this,n||0,t||0,r||0,u||0);return this.__set__&&this.__set__.push(f),f};c.path=function(n){!n||i.is(n,ti)||i.is(n[0],tt)||(n+=d);var t=i._engine.path(i.format[v](i,arguments),this);return this.__set__&&this.__set__.push(t),t};c.image=function(n,t,r,u,f){var e=i._engine.image(this,n||"about:blank",t||0,r||0,u||0,f||0);return this.__set__&&this.__set__.push(e),e};c.text=function(n,t,r){var u=i._engine.text(this,n||0,t||0,b(r));return this.__set__&&this.__set__.push(u),u};c.set=function(n){i.is(n,"array")||(n=Array.prototype.splice.call(arguments,0,arguments.length));var t=new yt(n);return this.__set__&&this.__set__.push(t),t.paper=this,t.type="set",t};c.setStart=function(n){this.__set__=n||this.set()};c.setFinish=function(){var n=this.__set__;return delete this.__set__,n};c.setSize=function(n,t){return i._engine.setSize.call(this,n,t)};c.setViewBox=function(n,t,r,u,f){return i._engine.setViewBox.call(this,n,t,r,u,f)};c.top=c.bottom=null;c.raphael=i;fu=function(n){var u=n.getBoundingClientRect(),f=n.ownerDocument,t=f.body,i=f.documentElement,e=i.clientTop||t.clientTop||0,o=i.clientLeft||t.clientLeft||0,s=u.top+(r.win.pageYOffset||i.scrollTop||t.scrollTop)-e,h=u.left+(r.win.pageXOffset||i.scrollLeft||t.scrollLeft)-o;return{y:s,x:h}};c.getElementByPoint=function(n,t){var o=this,f=o.canvas,i=r.doc.elementFromPoint(n,t),s,u,e;if(r.win.opera&&i.tagName=="svg"&&(s=fu(f),u=f.createSVGRect(),u.x=n-s.x,u.y=t-s.y,u.width=u.height=1,e=f.getIntersectionList(u,null),e.length&&(i=e[e.length-1])),!i)return null;while(i.parentNode&&i!=f.parentNode&&!i.raphael)i=i.parentNode;return i==o.canvas.parentNode&&(i=f),i&&i.raphael?o.getById(i.raphaelid):null};c.getElementsByBBox=function(n){var t=this.set();return this.forEach(function(r){i.isBBoxIntersect(r.getBBox(),n)&&t.push(r)}),t};c.getById=function(n){for(var t=this.bottom;t;){if(t.id==n)return t;t=t.next}return null};c.forEach=function(n,t){for(var i=this.bottom;i;){if(n.call(t,i)===!1)return this;i=i.next}return this};c.getElementsByPoint=function(n,t){var i=this.set();return this.forEach(function(r){r.isPointInside(n,t)&&i.push(r)}),i};e.isPointInside=function(n,t){var r=this.realPath=ri[this.type](this);return this.attr("transform")&&this.attr("transform").length&&(r=i.transformPath(r,this.attr("transform"))),i.isPointInsidePath(r,n,t)};e.getBBox=function(n){if(this.removed)return{};var t=this._;return n?((t.dirty||!t.bboxwt)&&(this.realPath=ri[this.type](this),t.bboxwt=nr(this.realPath),t.bboxwt.toString=eu,t.dirty=0),t.bboxwt):((t.dirty||t.dirtyT||!t.bbox)&&((t.dirty||!this.realPath)&&(t.bboxwt=0,this.realPath=ri[this.type](this)),t.bbox=nr(ki(this.realPath,this.matrix)),t.bbox.toString=eu,t.dirty=t.dirtyT=0),t.bbox)};e.clone=function(){if(this.removed)return null;var n=this.paper[this.type]().attr(this.attr());return this.__set__&&this.__set__.push(n),n};e.glow=function(n){var r;if(this.type=="text")return null;n=n||{};var t={width:(n.width||10)+(+this.attr("stroke-width")||1),fill:n.fill||!1,opacity:n.opacity||.5,offsetx:n.offsetx||0,offsety:n.offsety||0,color:n.color||"#000"},u=t.width/2,f=this.paper,e=f.set(),i=this.realPath||ri[this.type](this);for(i=this.matrix?ki(i,this.matrix):i,r=1;r<u+1;r++)e.push(f.path(i).attr({stroke:t.color,fill:t.fill?t.color:"none","stroke-linejoin":"round","stroke-linecap":"round","stroke-width":+(t.width/u*r).toFixed(3),opacity:+(t.opacity/u).toFixed(3)}));return e.insertBefore(this).translate(t.offsetx,t.offsety)};var ur=function(n,t,r,u,f,e,o,s,h){return h==null?vt(n,t,r,u,f,e,o,s):i.findDotsAtSegment(n,t,r,u,f,e,o,s,cf(n,t,r,u,f,e,o,s,h))},fr=function(n,t){return function(r,u,f){var y,p;r=wt(r);var s,h,e,a,c="",v={},o,l=0;for(y=0,p=r.length;y<p;y++){if(e=r[y],e[0]=="M")s=+e[1],h=+e[2];else{if(a=ur(s,h,e[1],e[2],e[3],e[4],e[5],e[6]),l+a>u){if(t&&!v.start){if(o=ur(s,h,e[1],e[2],e[3],e[4],e[5],e[6],u-l),c+=["C"+o.start.x,o.start.y,o.m.x,o.m.y,o.x,o.y],f)return c;v.start=c;c=["M"+o.x,o.y+"C"+o.n.x,o.n.y,o.end.x,o.end.y,e[5],e[6]].join();l+=a;s=+e[5];h=+e[6];continue}if(!n&&!t)return o=ur(s,h,e[1],e[2],e[3],e[4],e[5],e[6],u-l),{x:o.x,y:o.y,alpha:o.alpha}}l+=a;s=+e[5];h=+e[6]}c+=e.shift()+e}return v.end=c,o=n?l:t?v:i.findDotsAtSegment(s,h,e[0],e[1],e[2],e[3],e[4],e[5],1),o.alpha&&(o={x:o.x,y:o.y,alpha:o.alpha}),o}},ou=fr(1),su=fr(),er=fr(0,1);i.getTotalLength=ou;i.getPointAtLength=su;i.getSubpath=function(n,t,i){if(this.getTotalLength(n)-i<1e-6)return er(n,t).end;var r=er(n,i,1);return t?er(r,t).end:r};e.getTotalLength=function(){var n=this.getPath();if(n)return this.node.getTotalLength?this.node.getTotalLength():ou(n)};e.getPointAtLength=function(n){var t=this.getPath();if(t)return su(t,n)};e.getPath=function(){var n,t=i._getPath[this.type];if(this.type!="text"&&this.type!="set")return t&&(n=t(this)),n};e.getSubpath=function(n,t){var r=this.getPath();if(r)return i.getSubpath(r,n,t)};g=i.easing_formulas={linear:function(n){return n},"<":function(n){return nt(n,1.7)},">":function(n){return nt(n,.48)},"<>":function(n){var i=.48-n/1.04,r=u.sqrt(.1734+i*i),f=r-i,o=nt(y(f),1/3)*(f<0?-1:1),e=-r-i,s=nt(y(e),1/3)*(e<0?-1:1),t=o+s+.5;return(1-t)*3*t*t+t*t*t},backIn:function(n){var t=1.70158;return n*n*((t+1)*n-t)},backOut:function(n){n=n-1;var t=1.70158;return n*n*((t+1)*n+t)+1},elastic:function(n){return n==!!n?n:nt(2,-10*n)*u.sin((n-.075)*2*k/.3)+1},bounce:function(n){var r=7.5625,t=2.75,i;return n<1/t?i=r*n*n:n<2/t?(n-=1.5/t,i=r*n*n+.75):n<2.5/t?(n-=2.25/t,i=r*n*n+.9375):(n-=2.625/t,i=r*n*n+.984375),i}};g.easeIn=g["ease-in"]=g["<"];g.easeOut=g["ease-out"]=g[">"];g.easeInOut=g["ease-in-out"]=g["<>"];g["back-in"]=g.backIn;g["back-out"]=g.backOut;var f=[],hu=n.requestAnimationFrame||n.webkitRequestAnimationFrame||n.mozRequestAnimationFrame||n.oRequestAnimationFrame||n.msRequestAnimationFrame||function(n){setTimeout(n,16)},or=function(){for(var ft=+new Date,b=0,n,v,r,u,g,c,nt,w,ut;b<f.length;b++)if(n=f[b],!n.el.removed&&!n.paused){var k=ft-n.start,h=n.ms,et=n.easing,o=n.from,l=n.diff,tt=n.to,ot=n.t,y=n.el,it={},e,rt={},d;if(n.initstatus?(k=(n.initstatus*n.anim.top-n.prev)/(n.percent-n.prev)*h,n.status=n.initstatus,delete n.initstatus,n.stop&&f.splice(b--,1)):n.status=(n.prev+(n.percent-n.prev)*(k/h))/n.anim.top,!(k<0))if(k<h){v=et(k/h);for(r in o)if(o[a](r)){switch(wi[r]){case p:e=+o[r]+v*h*l[r];break;case"colour":e="rgb("+[sr(pi(o[r].r+v*h*l[r].r)),sr(pi(o[r].g+v*h*l[r].g)),sr(pi(o[r].b+v*h*l[r].b))].join(",")+")";break;case"path":for(e=[],u=0,g=o[r].length;u<g;u++){for(e[u]=[o[r][u][0]],c=1,nt=o[r][u].length;c<nt;c++)e[u][c]=+o[r][u][c]+v*h*l[r][u][c];e[u]=e[u].join(lt)}e=e.join(lt);break;case"transform":if(l[r].real)for(e=[],u=0,g=o[r].length;u<g;u++)for(e[u]=[o[r][u][0]],c=1,nt=o[r][u].length;c<nt;c++)e[u][c]=o[r][u][c]+v*h*l[r][u][c];else w=function(n){return+o[r][n]+v*h*l[r][n]},e=[["m",w(0),w(1),w(2),w(3),w(4),w(5)]];break;case"csv":if(r=="clip-rect")for(e=[],u=4;u--;)e[u]=+o[r][u]+v*h*l[r][u];break;default:for(ut=[][s](o[r]),e=[],u=y.paper.customAttributes[r].length;u--;)e[u]=+ut[u]+v*h*l[r][u]}it[r]=e}y.attr(it),function(n,i,r){setTimeout(function(){t("raphael.anim.frame."+n,i,r)})}(y.id,y,n.anim)}else{if(function(n,r,u){setTimeout(function(){t("raphael.anim.frame."+r.id,r,u);t("raphael.anim.finish."+r.id,r,u);i.is(n,"function")&&n.call(r)})}(n.callback,y,n.anim),y.attr(tt),f.splice(b--,1),n.repeat>1&&!n.next){for(d in tt)tt[a](d)&&(rt[d]=n.totalOrigin[d]);n.el.attr(rt);kt(n.anim,n.el,n.anim.percents[0],null,n.totalOrigin,n.repeat-1)}n.next&&!n.stop&&kt(n.anim,n.el,n.next,null,n.totalOrigin,n.repeat)}}i.svg&&y&&y.paper&&y.paper.safari();f.length&&hu(or)},sr=function(n){return n>255?255:n<0?0:n};e.animateWith=function(n,t,r,u,e,o){var s=this,c,h,l;if(s.removed)return o&&o.call(s),s;for(c=r instanceof ft?r:i.animation(r,u,e,o),kt(c,s,c.percents[0],null,s.attr()),h=0,l=f.length;h<l;h++)if(f[h].anim==t&&f[h].el==n){f[l-1].start=f[h].start;break}return s};e.onAnimation=function(n){return n?t.on("raphael.anim.frame."+this.id,n):t.unbind("raphael.anim.frame."+this.id),this};ft.prototype.delay=function(n){var t=new ft(this.anim,this.ms);return t.times=this.times,t.del=+n||0,t};ft.prototype.repeat=function(n){var t=new ft(this.anim,this.ms);return t.del=this.del,t.times=u.floor(o(n,0))||1,t};i.animation=function(n,t,r,u){if(n instanceof ft)return n;(i.is(r,"function")||!r)&&(u=u||r||null,r=null);n=Object(n);t=+t||0;var e={},o;for(var f in n)n[a](f)&&h(f)!=f&&h(f)+"%"!=f&&(o=!0,e[f]=n[f]);return o?(r&&(e.easing=r),u&&(e.callback=u),new ft({100:e},t)):new ft(n,t)};e.animate=function(n,t,r,u){var f=this,e;return f.removed?(u&&u.call(f),f):(e=n instanceof ft?n:i.animation(n,t,r,u),kt(e,f,e.percents[0],null,f.attr()),f)};e.setTime=function(n,t){return n&&t!=null&&this.status(n,l(t,n.ms)/n.ms),this};e.status=function(n,t){var u=[],r=0,e,i;if(t!=null)return kt(n,this,-1,l(t,1)),this;for(e=f.length;r<e;r++)if(i=f[r],i.el.id==this.id&&(!n||i.anim==n)){if(n)return i.status;u.push({anim:i.anim,status:i.status})}return n?0:u};e.pause=function(n){for(var i=0;i<f.length;i++)f[i].el.id!=this.id||n&&f[i].anim!=n||t("raphael.anim.pause."+this.id,this,f[i].anim)!==!1&&(f[i].paused=!0);return this};e.resume=function(n){for(var r,i=0;i<f.length;i++)f[i].el.id!=this.id||n&&f[i].anim!=n||(r=f[i],t("raphael.anim.resume."+this.id,this,r.anim)!==!1&&(delete r.paused,this.status(r.anim,r.status)));return this};e.stop=function(n){for(var i=0;i<f.length;i++)f[i].el.id!=this.id||n&&f[i].anim!=n||t("raphael.anim.stop."+this.id,this,f[i].anim)!==!1&&f.splice(i--,1);return this};t.on("raphael.remove",cu);t.on("raphael.clear",cu);e.toString=function(){return"Raphaël’s object"};yt=function(n){if(this.items=[],this.length=0,this.type="set",n)for(var t=0,i=n.length;t<i;t++)n[t]&&(n[t].constructor==e.constructor||n[t].constructor==yt)&&(this[this.items.length]=this.items[this.items.length]=n[t],this.length++)};w=yt.prototype;w.push=function(){for(var n,t,i=0,r=arguments.length;i<r;i++)n=arguments[i],n&&(n.constructor==e.constructor||n.constructor==yt)&&(t=this.items.length,this[t]=this.items[t]=n,this.length++);return this};w.pop=function(){return this.length&&delete this[this.length--],this.items.pop()};w.forEach=function(n,t){for(var i=0,r=this.items.length;i<r;i++)if(n.call(t,this.items[i],i)===!1)return this;return this};for(li in e)e[a](li)&&(w[li]=function(n){return function(){var t=arguments;return this.forEach(function(i){i[n][v](i,t)})}}(li));w.attr=function(n,t){var r,f,u,e;if(n&&i.is(n,tt)&&i.is(n[0],"object"))for(r=0,f=n.length;r<f;r++)this.items[r].attr(n[r]);else for(u=0,e=this.items.length;u<e;u++)this.items[u].attr(n,t);return this};w.clear=function(){while(this.length)this.pop()};w.splice=function(n,t){var r;n=n<0?o(this.length+n,0):n;t=o(0,l(this.length-n,t));for(var u=[],e=[],f=[],i=2;i<arguments.length;i++)f.push(arguments[i]);for(i=0;i<t;i++)e.push(this[n+i]);for(;i<this.length-n;i++)u.push(this[n+i]);for(r=f.length,i=0;i<r+u.length;i++)this.items[n+i]=this[n+i]=i<r?f[i]:u[i-r];for(i=this.items.length=this.length-=t-r;this[i];)delete this[i++];return new yt(e)};w.exclude=function(n){for(var t=0,i=this.length;t<i;t++)if(this[t]==n)return this.splice(t,1),!0};w.animate=function(n,t,r,u){var o;(i.is(r,"function")||!r)&&(u=r||null);var e=this.items.length,f=e,h,c=this,s;if(!e)return this;for(u&&(s=function(){--e||u.call(c)}),r=i.is(r,ti)?r:s,o=i.animation(n,t,r,s),h=this.items[--f].animate(o);f--;)this.items[f]&&!this.items[f].removed&&this.items[f].animateWith(h,o,o),this.items[f]&&!this.items[f].removed||e--;return this};w.insertAfter=function(n){for(var t=this.items.length;t--;)this.items[t].insertAfter(n);return this};w.getBBox=function(){for(var n,t=[],i=[],r=[],u=[],f=this.items.length;f--;)this.items[f].removed||(n=this.items[f].getBBox(),t.push(n.x),i.push(n.y),r.push(n.x+n.width),u.push(n.y+n.height));return t=l[v](0,t),i=l[v](0,i),r=o[v](0,r),u=o[v](0,u),{x:t,y:i,x2:r,y2:u,width:r-t,height:u-i}};w.clone=function(n){n=this.paper.set();for(var t=0,i=this.items.length;t<i;t++)n.push(this.items[t].clone());return n};w.toString=function(){return"Raphaël‘s set"};w.glow=function(n){var t=this.paper.set();return this.forEach(function(i){var r=i.glow(n);r!=null&&r.forEach(function(n){t.push(n)})}),t};w.isPointInside=function(n,t){var i=!1;return this.forEach(function(r){if(r.isPointInside(n,t))return i=!0,!1}),i};i.registerFont=function(n){var i,u,f,r,t,e;if(!n.face)return n;this.fonts=this.fonts||{};i={w:n.w,face:{},glyphs:{}};u=n.face["font-family"];for(f in n.face)n.face[a](f)&&(i.face[f]=n.face[f]);if(this.fonts[u]?this.fonts[u].push(i):this.fonts[u]=[i],!n.svg){i.face["units-per-em"]=st(n.face["units-per-em"],10);for(r in n.glyphs)if(n.glyphs[a](r)&&(t=n.glyphs[r],i.glyphs[r]={w:t.w,k:{},d:t.d&&"M"+t.d.replace(/[mlcxtrv]/g,function(n){return{l:"L",c:"C",x:"z",t:"m",r:"l",v:"c"}[n]||"M"})+"z"},t.k))for(e in t.k)t[a](e)&&(i.glyphs[r].k[e]=t.k[e])}return n};c.getFont=function(n,t,r,u){var f,h,o,e,s,c;if(u=u||"normal",r=r||"normal",t=+t||{normal:400,bold:700,lighter:300,bolder:800}[t]||400,i.fonts){if(f=i.fonts[n],!f){h=new RegExp("(^|\\s)"+n.replace(/[^\w\d\s+!~.:_-]/g,d)+"(\\s|$)","i");for(o in i.fonts)if(i.fonts[a](o)&&h.test(o)){f=i.fonts[o];break}}if(f)for(s=0,c=f.length;s<c;s++)if(e=f[s],e.face["font-weight"]==t&&(e.face["font-style"]==r||!e.face["font-style"])&&e.face["font-stretch"]==u)break;return e}};c.print=function(n,t,r,u,f,e,s,h){var a,et,k,y;e=e||"middle";s=o(l(s||0,1),-1);h=o(l(h||1,3),1);var v=b(r)[ut](d),g=0,p=0,tt=d,c;if(i.is(u,"string")&&(u=this.getFont(u)),u){c=(f||16)/u.face["units-per-em"];var w=u.face.bbox[ut](vi),it=+w[0],nt=w[3]-w[1],rt=0,ft=+w[1]+(e=="baseline"?nt+ +u.face.descent:nt/2);for(a=0,et=v.length;a<et;a++)v[a]=="\n"?(g=0,y=0,p=0,rt+=nt*h):(k=p&&u.glyphs[v[a-1]]||{},y=u.glyphs[v[a]],g+=p?(k.w||u.w)+(k.k&&k.k[v[a]]||0)+u.w*s:0,p=1),y&&y.d&&(tt+=i.transformPath(y.d,["t",g*c,rt*c,"s",c,c,it,ft,"t",(n-it)/c,(t-ft)/c]))}return this.path(tt).attr({fill:"#000",stroke:"none"})};c.add=function(n){if(i.is(n,"array"))for(var u=this.set(),r=0,f=n.length,t;r<f;r++)t=n[r]||{},lu[a](t.type)&&u.push(this[t.type]().attr(t));return u};i.format=function(n,t){var r=i.is(t,tt)?[0][s](t):arguments;return n&&i.is(n,ti)&&r.length-1&&(n=n.replace(au,function(n,t){return r[++t]==null?d:r[t]})),n||d};i.fullfill=function(){var n=/\{([^\}]+)\}/g,t=/(?:(?:^|\.)(.+?)(?=\[|\.|$|\()|\[('|")(.+?)\2\])(\(\))?/g,i=function(n,i,r){var u=r;return i.replace(t,function(n,t,i,r,f){t=t||r;u&&(t in u&&(u=u[t]),typeof u=="function"&&f&&(u=u()))}),u=(u==null||u==r?n:u)+""};return function(t,r){return String(t).replace(n,function(n,t){return i(n,t,r)})}}();i.ninja=function(){return yi.was?r.win.Raphael=yi.is:delete Raphael,i};i.st=w,function(n,t,r){function u(){/in/.test(n.readyState)?setTimeout(u,9):i.eve("raphael.DOMload")}n.readyState==null&&n.addEventListener&&(n.addEventListener(t,r=function(){n.removeEventListener(t,r,!1);n.readyState="complete"},!1),n.readyState="loading");u()}(document,"DOMContentLoaded");t.on("raphael.DOMload",function(){ai=!0});return function(){var nt,v;if(i.svg){var t="hasOwnProperty",u=String,f=parseFloat,tt=parseInt,c=Math,k=c.max,y=c.abs,d=c.pow,l=/[, ]+/,p=i.eve,o="",w=" ",a="http://www.w3.org/1999/xlink",ft={block:"M5,0 0,2.5 5,5z",classic:"M5,0 0,2.5 5,5 3.5,3 3.5,2z",diamond:"M2.5,0 5,2.5 2.5,5 0,2.5z",open:"M6,1 1,3.5 6,6",oval:"M2.5,0A2.5,2.5,0,0,1,2.5,5 2.5,2.5,0,0,1,2.5,0z"},e={};i.toString=function(){return"Your browser supports SVG.\nYou are running Raphaël "+this.version};var n=function(r,f){var e,o;if(f){typeof r=="string"&&(r=n(r));for(e in f)f[t](e)&&(e.substring(0,6)=="xlink:"?r.setAttributeNS(a,e.substring(6),u(f[e])):r!==undefined&&(o=u(f[e]),(e!=="d"||e==="d"&&o!=="M,0,0")&&r.setAttribute(e,o)))}else r=i._g.doc.createElementNS("http://www.w3.org/2000/svg",r),r.style&&(r.style.webkitTapHighlightColor="rgba(0,0,0,0)");return r},it=function(t,r){var w="linear",l=t.id+r,b=.5,s=.5,tt=t.node,it=t.paper,g=tt.style,a=i._g.doc.getElementById(l),v,e,nt,p,h,rt;if(!a){if(r=u(r).replace(i._radial_gradient,function(n,t,i){if(w="radial",t&&i){b=f(t);s=f(i);var r=(s>.5)*2-1;d(b-.5,2)+d(s-.5,2)>.25&&(s=c.sqrt(.25-d(b-.5,2))*r+.5)&&s!=.5&&(s=s.toFixed(5)-1e-5*r)}return o}),r=r.split(/\s*\-\s*/),w=="linear"){if(v=r.shift(),v=-f(v),isNaN(v))return null;e=[0,0,c.cos(i.rad(v)),c.sin(i.rad(v))];nt=1/(k(y(e[2]),y(e[3]))||1);e[2]*=nt;e[3]*=nt;e[2]<0&&(e[0]=-e[2],e[2]=0);e[3]<0&&(e[1]=-e[3],e[3]=0)}if(p=i._parseDots(r),!p)return null;if(l=l.replace(/[\(\)\s,\xb0#]/g,"_"),t.gradient&&l!=t.gradient.id&&(it.defs.removeChild(t.gradient),delete t.gradient),!t.gradient)for(a=n(w+"Gradient",{id:l}),t.gradient=a,n(a,w=="radial"?{fx:b,fy:s}:{x1:e[0],y1:e[1],x2:e[2],y2:e[3],gradientTransform:t.matrix.invert()}),it.defs.appendChild(a),h=0,rt=p.length;h<rt;h++)a.appendChild(n("stop",{offset:p[h].offset?p[h].offset:h?"100%":"0%","stop-color":p[h].color||"#fff"}))}return n(tt,{fill:"url(#"+l+")",opacity:1,"fill-opacity":1}),g.fill=o,g.opacity=1,g.fillOpacity=1,1},b=function(t){var i=t.getBBox(1);n(t.pattern,{patternTransform:t.matrix.invert()+" translate("+i.x+","+i.y+")"})},s=function(r,f,s){var b,k,g,tt,it,rt;if(r.type=="path"){for(var ut=u(f).toLowerCase().split("-"),ht=r.paper,h=s?"end":"start",ct=r.node,l=r.attrs,d=l["stroke-width"],et=ut.length,a="classic",p,w,ot,st,c,v=3,y=3,nt=5;et--;)switch(ut[et]){case"block":case"classic":case"oval":case"diamond":case"open":case"none":a=ut[et];break;case"wide":y=5;break;case"narrow":y=2;break;case"long":v=5;break;case"short":v=2}a=="open"?(v+=2,y+=2,nt+=2,ot=1,st=s?4:1,c={fill:"none",stroke:l.stroke}):(st=ot=v/2,c={fill:l.stroke,stroke:"none"});r._.arrows?s?(r._.arrows.endPath&&e[r._.arrows.endPath]--,r._.arrows.endMarker&&e[r._.arrows.endMarker]--):(r._.arrows.startPath&&e[r._.arrows.startPath]--,r._.arrows.startMarker&&e[r._.arrows.startMarker]--):r._.arrows={};a!="none"?(b="raphael-marker-"+a,k="raphael-marker-"+h+a+v+y,i._g.doc.getElementById(b)?e[b]++:(ht.defs.appendChild(n(n("path"),{"stroke-linecap":"round",d:ft[a],id:b})),e[b]=1),g=i._g.doc.getElementById(k),g?(e[k]++,tt=g.getElementsByTagName("use")[0]):(g=n(n("marker"),{id:k,markerHeight:y,markerWidth:v,orient:"auto",refX:st,refY:y/2}),tt=n(n("use"),{"xlink:href":"#"+b,transform:(s?"rotate(180 "+v/2+" "+y/2+") ":o)+"scale("+v/nt+","+y/nt+")","stroke-width":(2/(v/nt+y/nt)).toFixed(4)}),g.appendChild(tt),ht.defs.appendChild(g),e[k]=1),n(tt,c),it=ot*(a!="diamond"&&a!="oval"),s?(p=r._.arrows.startdx*d||0,w=i.getTotalLength(l.path)-it*d):(p=it*d,w=i.getTotalLength(l.path)-(r._.arrows.enddx*d||0)),c={},c["marker-"+h]="url(#"+k+")",(w||p)&&(c.d=i.getSubpath(l.path,p,w)),n(ct,c),r._.arrows[h+"Path"]=b,r._.arrows[h+"Marker"]=k,r._.arrows[h+"dx"]=it,r._.arrows[h+"Type"]=a,r._.arrows[h+"String"]=f):(s?(p=r._.arrows.startdx*d||0,w=i.getTotalLength(l.path)-p):(p=0,w=i.getTotalLength(l.path)-(r._.arrows.enddx*d||0)),r._.arrows[h+"Path"]&&n(ct,{d:i.getSubpath(l.path,p,w)}),delete r._.arrows[h+"Path"],delete r._.arrows[h+"Marker"],delete r._.arrows[h+"dx"],delete r._.arrows[h+"Type"],delete r._.arrows[h+"String"]);for(c in e)e[t](c)&&!e[c]&&(rt=i._g.doc.getElementById(c),rt&&rt.parentNode.removeChild(rt))}},et={"":[0],none:[0],"-":[3,1],".":[1,1],"-.":[3,1,1,1],"-..":[3,1,1,1,1,1],". ":[1,3],"- ":[4,3],"--":[8,3],"- .":[4,3,1,3],"--.":[8,3,1,3],"--..":[8,3,1,3,1,3]},rt=function(t,i,r){if(i=et[u(i).toLowerCase()],i){for(var e=t.attrs["stroke-width"]||"1",s={round:e,square:e,butt:0}[t.attrs["stroke-linecap"]||r["stroke-linecap"]]||0,o=[],f=i.length;f--;)o[f]=i[f]*e+(f%2?1:-1)*s;n(t.node,{"stroke-dasharray":o.join(",")})}},g=function(r,f){var h=r.node,c=r.attrs,pt=h.style.visibility,v,e,d,vt,g,et,nt,p,st,at,ht,ct,lt,w,ut,ft,yt;h.style.visibility="hidden";for(v in f)if(f[t](v)){if(!i._availableAttrs[t](v))continue;e=f[v];c[v]=e;switch(v){case"blur":r.blur(e);break;case"title":d=h.getElementsByTagName("title");d.length&&(d=d[0])?d.firstChild.nodeValue=e:(d=n("title"),vt=i._g.doc.createTextNode(e),d.appendChild(vt),h.appendChild(d));break;case"href":case"target":g=h.parentNode;g.tagName.toLowerCase()!="a"&&(et=n("a"),g.insertBefore(et,h),et.appendChild(h),g=et);v=="target"?g.setAttributeNS(a,"show",e=="blank"?"new":e):g.setAttributeNS(a,v,e);break;case"cursor":h.style.cursor=e;break;case"transform":r.transform(e);break;case"arrow-start":s(r,e);break;case"arrow-end":s(r,e,1);break;case"clip-rect":nt=u(e).split(l);nt.length==4&&(r.clip&&r.clip.parentNode.parentNode.removeChild(r.clip.parentNode),p=n("clipPath"),st=n("rect"),p.id=i.createUUID(),n(st,{x:nt[0],y:nt[1],width:nt[2],height:nt[3]}),p.appendChild(st),r.paper.defs.appendChild(p),n(h,{"clip-path":"url(#"+p.id+")"}),r.clip=st);e||(at=h.getAttribute("clip-path"),at&&(ht=i._g.doc.getElementById(at.replace(/(^url\(#|\)$)/g,o)),ht&&ht.parentNode.removeChild(ht),n(h,{"clip-path":o}),delete r.clip));break;case"path":r.type=="path"&&(n(h,{d:e?c.path=i._pathToAbsolute(e):"M0,0"}),r._.dirty=1,r._.arrows&&("startString"in r._.arrows&&s(r,r._.arrows.startString),"endString"in r._.arrows&&s(r,r._.arrows.endString,1)));break;case"width":if(h.setAttribute(v,e),r._.dirty=1,c.fx)v="x",e=c.x;else break;case"x":c.fx&&(e=-c.x-(c.width||0));case"rx":if(v=="rx"&&r.type=="rect")break;case"cx":h.setAttribute(v,e);r.pattern&&b(r);r._.dirty=1;break;case"height":if(h.setAttribute(v,e),r._.dirty=1,c.fy)v="y",e=c.y;else break;case"y":c.fy&&(e=-c.y-(c.height||0));case"ry":if(v=="ry"&&r.type=="rect")break;case"cy":h.setAttribute(v,e);r.pattern&&b(r);r._.dirty=1;break;case"r":r.type=="rect"?n(h,{rx:e,ry:e}):h.setAttribute(v,e);r._.dirty=1;break;case"src":r.type=="image"&&h.setAttributeNS(a,"href",e);break;case"stroke-width":(r._.sx!=1||r._.sy!=1)&&(e/=k(y(r._.sx),y(r._.sy))||1);r.paper._vbSize&&(e*=r.paper._vbSize);h.setAttribute(v,e);c["stroke-dasharray"]&&rt(r,c["stroke-dasharray"],f);r._.arrows&&("startString"in r._.arrows&&s(r,r._.arrows.startString),"endString"in r._.arrows&&s(r,r._.arrows.endString,1));break;case"stroke-dasharray":rt(r,e,f);break;case"fill":if(ct=u(e).match(i._ISURL),ct){p=n("pattern");lt=n("image");p.id=i.createUUID();n(p,{x:0,y:0,patternUnits:"userSpaceOnUse",height:1,width:1});n(lt,{x:0,y:0,"xlink:href":ct[1]});p.appendChild(lt),function(t){i._preload(ct[1],function(){var i=this.offsetWidth,u=this.offsetHeight;n(t,{width:i,height:u});n(lt,{width:i,height:u});r.paper.safari()})}(p);r.paper.defs.appendChild(p);n(h,{fill:"url(#"+p.id+")"});r.pattern=p;r.pattern&&b(r);break}if(w=i.getRGB(e),w.error){if((r.type=="circle"||r.type=="ellipse"||u(e).charAt()!="r")&&it(r,e)){("opacity"in c||"fill-opacity"in c)&&(ut=i._g.doc.getElementById(h.getAttribute("fill").replace(/^url\(#|\)$/g,o)),ut&&(ft=ut.getElementsByTagName("stop"),n(ft[ft.length-1],{"stop-opacity":("opacity"in c?c.opacity:1)*("fill-opacity"in c?c["fill-opacity"]:1)})));c.gradient=e;c.fill="none";break}}else delete f.gradient,delete c.gradient,!i.is(c.opacity,"undefined")&&i.is(f.opacity,"undefined")&&n(h,{opacity:c.opacity}),!i.is(c["fill-opacity"],"undefined")&&i.is(f["fill-opacity"],"undefined")&&n(h,{"fill-opacity":c["fill-opacity"]});w[t]("opacity")&&n(h,{"fill-opacity":w.opacity>1?w.opacity/100:w.opacity});case"stroke":w=i.getRGB(e);h.setAttribute(v,w.hex);v=="stroke"&&w[t]("opacity")&&n(h,{"stroke-opacity":w.opacity>1?w.opacity/100:w.opacity});v=="stroke"&&r._.arrows&&("startString"in r._.arrows&&s(r,r._.arrows.startString),"endString"in r._.arrows&&s(r,r._.arrows.endString,1));break;case"gradient":(r.type=="circle"||r.type=="ellipse"||u(e).charAt()!="r")&&it(r,e);break;case"opacity":c.gradient&&!c[t]("stroke-opacity")&&n(h,{"stroke-opacity":e>1?e/100:e});case"fill-opacity":if(c.gradient){ut=i._g.doc.getElementById(h.getAttribute("fill").replace(/^url\(#|\)$/g,o));ut&&(ft=ut.getElementsByTagName("stop"),n(ft[ft.length-1],{"stop-opacity":e}));break}default:v=="font-size"&&(e=tt(e,10)+"px");yt=v.replace(/(\-.)/g,function(n){return n.substring(1).toUpperCase()});h.style[yt]=e;r._.dirty=1;h.setAttribute(v,e)}}ot(r,f);h.style.visibility=pt},ut=1.2,ot=function(r,f){var y,h,l,e,a,p,v;if(r.type=="text"&&(f[t]("text")||f[t]("font")||f[t]("font-size")||f[t]("x")||f[t]("y"))){var c=r.attrs,s=r.node,w=s.firstChild?tt(i._g.doc.defaultView.getComputedStyle(s.firstChild,o).getPropertyValue("font-size"),10):10;if(f[t]("text")){for(c.text=f.text;s.firstChild;)s.removeChild(s.firstChild);for(y=u(f.text).split("\n"),h=[],e=0,a=y.length;e<a;e++)l=n("tspan"),e&&n(l,{dy:w*ut,x:c.x}),l.appendChild(i._g.doc.createTextNode(y[e])),s.appendChild(l),h[e]=l}else for(h=s.getElementsByTagName("tspan"),e=0,a=h.length;e<a;e++)e?n(h[e],{dy:w*ut,x:c.x}):n(h[0],{dy:0});n(s,{x:c.x,y:c.y});r._.dirty=1;p=r._getBBox();v=c.y-(p.y+p.height/2);v&&i.is(v,"finite")&&n(h[0],{dy:v})}},h=function(n,t){this[0]=this.node=n;n.raphael=!0;this.id=i._oid++;n.raphaelid=this.id;this.matrix=i.matrix();this.realPath=null;this.paper=t;this.attrs=this.attrs||{};this._={transform:[],sx:1,sy:1,deg:0,dx:0,dy:0,dirty:1};t.bottom||(t.bottom=this);this.prev=t.top;t.top&&(t.top.next=this);t.top=this;this.next=null},r=i.el;h.prototype=r;r.constructor=h;i._engine.path=function(t,i){var u=n("path"),r;return i.canvas&&i.canvas.appendChild(u),r=new h(u,i),r.type="path",g(r,{fill:"none",stroke:"#000",path:t}),r};r.rotate=function(n,t,i){if(this.removed)return this;if(n=u(n).split(l),n.length-1&&(t=f(n[1]),i=f(n[2])),n=f(n[0]),i==null&&(t=i),t==null||i==null){var r=this.getBBox(1);t=r.x+r.width/2;i=r.y+r.height/2}return this.transform(this._.transform.concat([["r",n,t,i]])),this};r.scale=function(n,t,i,r){if(this.removed)return this;if(n=u(n).split(l),n.length-1&&(t=f(n[1]),i=f(n[2]),r=f(n[3])),n=f(n[0]),t==null&&(t=n),r==null&&(i=r),i==null||r==null)var e=this.getBBox(1);return i=i==null?e.x+e.width/2:i,r=r==null?e.y+e.height/2:r,this.transform(this._.transform.concat([["s",n,t,i,r]])),this};r.translate=function(n,t){return this.removed?this:(n=u(n).split(l),n.length-1&&(t=f(n[1])),n=f(n[0])||0,t=+t||0,this.transform(this._.transform.concat([["t",n,t]])),this)};r.transform=function(r){var u=this._,f;return r==null?u.transform:(i._extractTransform(this,r),this.clip&&n(this.clip,{transform:this.matrix.invert()}),this.pattern&&b(this),this.node&&n(this.node,{transform:this.matrix}),(u.sx!=1||u.sy!=1)&&(f=this.attrs[t]("stroke-width")?this.attrs["stroke-width"]:1,this.attr({"stroke-width":f})),this)};r.hide=function(){return this.removed||this.paper.safari(this.node.style.display="none"),this};r.show=function(){return this.removed||this.paper.safari(this.node.style.display=""),this};r.remove=function(){var n,t;if(!this.removed&&this.node.parentNode){n=this.paper;n.__set__&&n.__set__.exclude(this);p.unbind("raphael.*.*."+this.id);this.gradient&&n.defs.removeChild(this.gradient);i._tear(this,n);this.node.parentNode.tagName.toLowerCase()=="a"?this.node.parentNode.parentNode.removeChild(this.node.parentNode):this.node.parentNode.removeChild(this.node);for(t in this)this[t]=typeof this[t]=="function"?i._removedFactory(t):null;this.removed=!0}};r._getBBox=function(){var t,n;this.node.style.display=="none"&&(this.show(),t=!0);n={};try{n=this.node.getBBox()}catch(i){}finally{n=n||{}}return t&&this.hide(),n};r.attr=function(n,r){var e,c,a,s,o,h,f,u,v,y;if(this.removed)return this;if(n==null){e={};for(c in this.attrs)this.attrs[t](c)&&(e[c]=this.attrs[c]);return e.gradient&&e.fill=="none"&&(e.fill=e.gradient)&&delete e.gradient,e.transform=this._.transform,e}if(r==null&&i.is(n,"string")){if(n=="fill"&&this.attrs.fill=="none"&&this.attrs.gradient)return this.attrs.gradient;if(n=="transform")return this._.transform;for(a=n.split(l),s={},o=0,h=a.length;o<h;o++)n=a[o],s[n]=n in this.attrs?this.attrs[n]:i.is(this.paper.customAttributes[n],"function")?this.paper.customAttributes[n].def:i._availableAttrs[n];return h-1?s:s[a[0]]}if(r==null&&i.is(n,"array")){for(s={},o=0,h=n.length;o<h;o++)s[n[o]]=this.attr(n[o]);return s}r!=null?(f={},f[n]=r):n!=null&&i.is(n,"object")&&(f=n);for(u in f)p("raphael.attr."+u+"."+this.id,this,f[u]);for(u in this.paper.customAttributes)if(this.paper.customAttributes[t](u)&&f[t](u)&&i.is(this.paper.customAttributes[u],"function")){v=this.paper.customAttributes[u].apply(this,[].concat(f[u]));this.attrs[u]=f[u];for(y in v)v[t](y)&&(f[y]=v[y])}return g(this,f),this};r.toFront=function(){if(this.removed)return this;this.node.parentNode.tagName.toLowerCase()=="a"?this.node.parentNode.parentNode.appendChild(this.node.parentNode):this.node.parentNode.appendChild(this.node);var n=this.paper;return n.top!=this&&i._tofront(this,n),this};r.toBack=function(){var n,t;return this.removed?this:(n=this.node.parentNode,n===null)?this:(n.tagName.toLowerCase()=="a"?n.parentNode.insertBefore(this.node.parentNode,this.node.parentNode.parentNode.firstChild):n.firstChild!=this.node&&n.insertBefore(this.node,this.node.parentNode.firstChild),i._toback(this,this.paper),t=this.paper,this)};r.insertAfter=function(n){if(this.removed)return this;var t=n.node||n[n.length-1].node;return t.nextSibling?t.parentNode.insertBefore(this.node,t.nextSibling):t.parentNode.appendChild(this.node),i._insertafter(this,n,this.paper),this};r.insertBefore=function(n){if(this.removed)return this;var t=n.node||n[0].node;return t.parentNode.insertBefore(this.node,t),i._insertbefore(this,n,this.paper),this};r.blur=function(t){var r=this,u,f;return+t!=0?(u=n("filter"),f=n("feGaussianBlur"),r.attrs.blur=t,u.id=i.createUUID(),n(f,{stdDeviation:+t||1.5}),u.appendChild(f),r.paper.defs.appendChild(u),r._blur=u,n(r.node,{filter:"url(#"+u.id+")"})):(r._blur&&(r._blur.parentNode.removeChild(r._blur),delete r._blur,delete r.attrs.blur),r.node.removeAttribute("filter")),r};i._engine.circle=function(t,i,r,u){var e=n("circle"),f;return t.canvas&&t.canvas.appendChild(e),f=new h(e,t),f.attrs={cx:i,cy:r,r:u,fill:"none",stroke:"#000"},f.type="circle",n(e,f.attrs),f};i._engine.rect=function(t,i,r,u,f,e){var s=n("rect"),o;return t.canvas&&t.canvas.appendChild(s),o=new h(s,t),o.attrs={x:i,y:r,width:u,height:f,r:e||0,rx:e||0,ry:e||0,fill:"none",stroke:"#000"},o.type="rect",n(s,o.attrs),o};i._engine.ellipse=function(t,i,r,u,f){var o=n("ellipse"),e;return t.canvas&&t.canvas.appendChild(o),e=new h(o,t),e.attrs={cx:i,cy:r,rx:u,ry:f,fill:"none",stroke:"#000"},e.type="ellipse",n(o,e.attrs),e};i._engine.image=function(t,i,r,u,f,e){var o=n("image"),s;return n(o,{x:r,y:u,width:f,height:e,preserveAspectRatio:"none"}),o.setAttributeNS(a,"href",i),t.canvas&&t.canvas.appendChild(o),s=new h(o,t),s.attrs={x:r,y:u,width:f,height:e,src:i},s.type="image",s};i._engine.text=function(t,r,u,f){var o=n("text"),e;return t.canvas&&t.canvas.appendChild(o),e=new h(o,t),e.attrs={x:r,y:u,"text-anchor":"middle",text:f,font:i._availableAttrs.font,stroke:"none",fill:"#000"},e.type="text",g(e,e.attrs),e};i._engine.setSize=function(n,t){return this.width=n||this.width,this.height=t||this.height,this.canvas.setAttribute("width",this.width),this.canvas.setAttribute("height",this.height),this._viewBox&&this.setViewBox.apply(this,this._viewBox),this};i._engine.create=function(){var u=i._getContainer.apply(0,arguments),t=u&&u.container,o=u.x,s=u.y,f=u.width,e=u.height,r,h,c;if(!t)throw new Error("SVG container not found.");return r=n("svg"),h="overflow:hidden;",o=o||0,s=s||0,f=f||512,e=e||342,n(r,{height:e,version:1.1,width:f,xmlns:"http://www.w3.org/2000/svg"}),t==1?(r.style.cssText=h+"position:absolute;left:"+o+"px;top:"+s+"px",i._g.doc.body.appendChild(r),c=1):(r.style.cssText=h+"position:relative",t.firstChild?t.insertBefore(r,t.firstChild):t.appendChild(r)),t=new i._Paper,t.width=f,t.height=e,t.canvas=r,t.clear(),t._left=t._top=0,c&&(t.renderfix=function(){}),t.renderfix(),t};i._engine.setViewBox=function(t,i,r,u,f){p("raphael.setViewBox",this,this._viewBox,[t,i,r,u,f]);var o=k(r/this.width,u/this.height),e=this.top,c=f?"xMidYMid meet":"xMinYMin",s,h;for(t==null?(this._vbSize&&(o=1),delete this._vbSize,s="0 0 "+this.width+w+this.height):(this._vbSize=o,s=t+w+i+w+r+w+u),n(this.canvas,{viewBox:s,preserveAspectRatio:c});o&&e;)h="stroke-width"in e.attrs?e.attrs["stroke-width"]:1,e.attr({"stroke-width":h}),e._.dirty=1,e._.dirtyT=1,e=e.prev;return this._viewBox=[t,i,r,u,!!f],this};i.prototype.renderfix=function(){var n=this.canvas,u=n.style,t,i,r;try{t=n.getScreenCTM()||n.createSVGMatrix()}catch(f){t=n.createSVGMatrix()}i=-t.e%1;r=-t.f%1;(i||r)&&(i&&(this._left=(this._left+i)%1,u.left=this._left+"px"),r&&(this._top=(this._top+r)%1,u.top=this._top+"px"))};i.prototype.clear=function(){i.eve("raphael.clear",this);for(var t=this.canvas;t.firstChild;)t.removeChild(t.firstChild);this.bottom=this.top=null;(this.desc=n("desc")).appendChild(i._g.doc.createTextNode("Created with Raphaël "+i.version));t.appendChild(this.desc);t.appendChild(this.defs=n("defs"))};i.prototype.remove=function(){p("raphael.remove",this);this.canvas.parentNode&&this.canvas.parentNode.removeChild(this.canvas);for(var n in this)this[n]=typeof this[n]=="function"?i._removedFactory(n):null};nt=i.st;for(v in r)r[t](v)&&!nt[t](v)&&(nt[v]=function(n){return function(){var t=arguments;return this.forEach(function(i){i[n].apply(i,t)})}}(v))}}(),function(){var s,d,y;if(i.vml){var h="hasOwnProperty",t=String,f=parseFloat,c=Math,e=c.round,k=c.max,g=c.min,p=c.abs,l="fill",a=/[, ]+/,ut=i.eve,ft=" progid:DXImageTransform.Microsoft",o=" ",u="",nt={M:"m",L:"l",C:"c",Z:"x",m:"t",l:"r",c:"v",z:"x"},et=/([clmz]),?([^clmz]*)/gi,ot=/ progid:\S+Blur\([^\)]+\)/g,st=/-?[^,\s-]+/g,tt="position:absolute;left:0;top:0;width:1px;height:1px",n=21600,ht={path:1,rect:1,image:1},ct={circle:1,ellipse:1},lt=function(r){var l=/[ahqstv]/ig,a=i._pathToAbsolute,v,c,y,f,s,w,h,p;if(t(r).match(l)&&(a=i._path2curve),l=/[clmz]/g,a==i._pathToAbsolute&&!t(r).match(l))return t(r).replace(et,function(t,i,r){var u=[],o=i.toLowerCase()=="m",f=nt[i];return r.replace(st,function(t){o&&u.length==2&&(f+=u+nt[i=="m"?"l":"L"],u=[]);u.push(e(t*n))}),f+u});for(c=a(r),v=[],s=0,w=c.length;s<w;s++){for(y=c[s],f=c[s][0].toLowerCase(),f=="z"&&(f="x"),h=1,p=y.length;h<p;h++)f+=e(y[h]*n)+(h!=p-1?",":u);v.push(f)}return v.join(o)},it=function(n,t,r){var u=i.matrix();return u.rotate(-n,.5,.5),{dx:u.x(t,r),dy:u.y(t,r)}},w=function(t,i,r,u,f,e){var v=t._,k=t.matrix,h=v.fillpos,c=t.node,y=c.style,w=1,b="",d=n/i,g=n/r,a,s;(y.visibility="hidden",i&&r)&&(c.coordsize=p(d)+o+p(g),y.rotation=e*(i*r<0?-1:1),e&&(a=it(e,u,f),u=a.dx,f=a.dy),i<0&&(b+="x"),r<0&&(b+=" y")&&(w=-1),y.flip=b,c.coordorigin=u*-d+o+f*-g,(h||v.fillsize)&&(s=c.getElementsByTagName(l),s=s&&s[0],c.removeChild(s),h&&(a=it(e,k.x(h[0],h[1]),k.y(h[0],h[1])),s.position=a.dx*w+o+a.dy*w),v.fillsize&&(s.size=v.fillsize[0]*p(i)+o+v.fillsize[1]*p(r)),c.appendChild(s)),y.visibility="visible")};i.toString=function(){return"Your browser doesn’t support SVG. Falling down to VML.\nYou are running Raphaël "+this.version};var rt=function(n,i,r){for(var u=t(i).toLowerCase().split("-"),o=r?"end":"start",f=u.length,s="classic",h="medium",c="medium",e;f--;)switch(u[f]){case"block":case"classic":case"oval":case"diamond":case"open":case"none":s=u[f];break;case"wide":case"narrow":c=u[f];break;case"long":case"short":h=u[f]}e=n.node.getElementsByTagName("stroke")[0];e[o+"arrow"]=s;e[o+"arrowlength"]=h;e[o+"arrowwidth"]=c},v=function(r,c){var yt,nt,ot,ut,ft,y,si,pt,st,tt,d,dt,gt,et,ni,vt,ri,bt,hi;r.attrs=r.attrs||{};var b=r.node,v=r.attrs,it=b.style,ui=ht[r.type]&&(c.x!=v.x||c.y!=v.y||c.width!=v.width||c.height!=v.height||c.cx!=v.cx||c.cy!=v.cy||c.rx!=v.rx||c.ry!=v.ry||c.r!=v.r),ci=ct[r.type]&&(v.cx!=c.cx||v.cy!=c.cy||v.r!=c.r||v.rx!=c.rx||v.ry!=c.ry),p=r;for(yt in c)c[h](yt)&&(v[yt]=c[yt]);if(ui&&(v.path=i._getPath[r.type](r),r._.dirty=1),c.href&&(b.href=c.href),c.title&&(b.title=c.title),c.target&&(b.target=c.target),c.cursor&&(it.cursor=c.cursor),"blur"in c&&r.blur(c.blur),(c.path&&r.type=="path"||ui)&&(b.path=lt(~t(v.path).toLowerCase().indexOf("r")?i._pathToAbsolute(v.path):v.path),r.type=="image"&&(r._.fillpos=[v.x,v.y],r._.fillsize=[v.width,v.height],w(r,1,1,0,0,0))),"transform"in c&&r.transform(c.transform),ci){var kt=+v.cx,fi=+v.cy,ei=+v.rx||+v.r||0,oi=+v.ry||+v.r||0;b.path=i.format("ar{0},{1},{2},{3},{4},{1},{4},{1}x",e((kt-ei)*n),e((fi-oi)*n),e((kt+ei)*n),e((fi+oi)*n),e(kt*n));r._.dirty=1}if("clip-rect"in c&&(nt=t(c["clip-rect"]).split(a),nt.length==4&&(nt[2]=+nt[2]+ +nt[0],nt[3]=+nt[3]+ +nt[1],ot=b.clipRect||i._g.doc.createElement("div"),ut=ot.style,ut.clip=i.format("rect({1}px {2}px {3}px {0}px)",nt),b.clipRect||(ut.position="absolute",ut.top=0,ut.left=0,ut.width=r.paper.width+"px",ut.height=r.paper.height+"px",b.parentNode.insertBefore(ot,b),ot.appendChild(b),b.clipRect=ot)),c["clip-rect"]||b.clipRect&&(b.clipRect.style.clip="auto")),r.textpath&&(ft=r.textpath.style,c.font&&(ft.font=c.font),c["font-family"]&&(ft.fontFamily='"'+c["font-family"].split(",")[0].replace(/^['"]+|['"]+$/g,u)+'"'),c["font-size"]&&(ft.fontSize=c["font-size"]),c["font-weight"]&&(ft.fontWeight=c["font-weight"]),c["font-style"]&&(ft.fontStyle=c["font-style"])),"arrow-start"in c&&rt(p,c["arrow-start"]),"arrow-end"in c&&rt(p,c["arrow-end"],1),(c.opacity!=null||c["stroke-width"]!=null||c.fill!=null||c.src!=null||c.stroke!=null||c["stroke-width"]!=null||c["stroke-opacity"]!=null||c["fill-opacity"]!=null||c["stroke-dasharray"]!=null||c["stroke-miterlimit"]!=null||c["stroke-linejoin"]!=null||c["stroke-linecap"]!=null)&&(y=b.getElementsByTagName(l),si=!1,y=y&&y[0],y||(si=y=s(l)),r.type=="image"&&c.src&&(y.src=c.src),c.fill&&(y.on=!0),(y.on==null||c.fill=="none"||c.fill===null)&&(y.on=!1),y.on&&c.fill&&(pt=t(c.fill).match(i._ISURL),pt?(y.parentNode==b&&b.removeChild(y),y.rotate=!0,y.src=pt[1],y.type="tile",st=r.getBBox(1),y.position=st.x+o+st.y,r._.fillpos=[st.x,st.y],i._preload(pt[1],function(){r._.fillsize=[this.offsetWidth,this.offsetHeight]})):(y.color=i.getRGB(c.fill).hex,y.src=u,y.type="solid",i.getRGB(c.fill).error&&(p.type in{circle:1,ellipse:1}||t(c.fill).charAt()!="r")&&at(p,c.fill,y)&&(v.fill="none",v.gradient=c.fill,y.rotate=!1))),("fill-opacity"in c||"opacity"in c)&&(tt=((+v["fill-opacity"]+1||2)-1)*((+v.opacity+1||2)-1)*((+i.getRGB(c.fill).o+1||2)-1),tt=g(k(tt,0),1),y.opacity=tt,y.src&&(y.color="none")),b.appendChild(y),d=b.getElementsByTagName("stroke")&&b.getElementsByTagName("stroke")[0],dt=!1,d||(dt=d=s("stroke")),(c.stroke&&c.stroke!="none"||c["stroke-width"]||c["stroke-opacity"]!=null||c["stroke-dasharray"]||c["stroke-miterlimit"]||c["stroke-linejoin"]||c["stroke-linecap"])&&(d.on=!0),(c.stroke=="none"||c.stroke===null||d.on==null||c.stroke==0||c["stroke-width"]==0)&&(d.on=!1),gt=i.getRGB(c.stroke),d.on&&c.stroke&&(d.color=gt.hex),tt=((+v["stroke-opacity"]+1||2)-1)*((+v.opacity+1||2)-1)*((+gt.o+1||2)-1),et=(f(c["stroke-width"])||1)*.75,tt=g(k(tt,0),1),c["stroke-width"]==null&&(et=v["stroke-width"]),c["stroke-width"]&&(d.weight=et),et&&et<1&&(tt*=et)&&(d.weight=1),d.opacity=tt,c["stroke-linejoin"]&&(d.joinstyle=c["stroke-linejoin"]||"miter"),d.miterlimit=c["stroke-miterlimit"]||8,c["stroke-linecap"]&&(d.endcap=c["stroke-linecap"]=="butt"?"flat":c["stroke-linecap"]=="square"?"square":"round"),"stroke-dasharray"in c&&(ni={"-":"shortdash",".":"shortdot","-.":"shortdashdot","-..":"shortdashdotdot",". ":"dot","- ":"dash","--":"longdash","- .":"dashdot","--.":"longdashdot","--..":"longdashdotdot"},d.dashstyle=ni[h](c["stroke-dasharray"])?ni[c["stroke-dasharray"]]:u),dt&&b.appendChild(d)),p.type=="text"){p.paper.canvas.style.display=u;var ti=p.paper.span,ii=100,wt=v.font&&v.font.match(/\d+(?:\.\d*)?(?=px)/);for(it=ti.style,v.font&&(it.font=v.font),v["font-family"]&&(it.fontFamily=v["font-family"]),v["font-weight"]&&(it.fontWeight=v["font-weight"]),v["font-style"]&&(it.fontStyle=v["font-style"]),wt=f(v["font-size"]||wt&&wt[0])||10,it.fontSize=wt*ii+"px",p.textpath.string&&(ti.innerHTML=t(p.textpath.string).replace(/</g,"&#60;").replace(/&/g,"&#38;").replace(/\n/g,"<br>")),vt=ti.getBoundingClientRect(),p.W=v.w=(vt.right-vt.left)/ii,p.H=v.h=(vt.bottom-vt.top)/ii,p.X=v.x,p.Y=v.y+p.H/2,(("x"in c)||("y"in c))&&(p.path.v=i.format("m{0},{1}l{2},{1}",e(v.x*n),e(v.y*n),e(v.x*n)+1)),ri=["x","y","text","font","font-family","font-weight","font-style","font-size"],bt=0,hi=ri.length;bt<hi;bt++)if(ri[bt]in c){p._.dirty=1;break}switch(v["text-anchor"]){case"start":p.textpath.style["v-text-align"]="left";p.bbx=p.W/2;break;case"end":p.textpath.style["v-text-align"]="right";p.bbx=-p.W/2;break;default:p.textpath.style["v-text-align"]="center";p.bbx=0}p.textpath.style["v-text-kern"]=!0}},at=function(n,r,e){var l,s,a,h,w;n.attrs=n.attrs||{};var b=n.attrs,v=Math.pow,y="linear",p=".5 .5";if((n.attrs.gradient=r,r=t(r).replace(i._radial_gradient,function(n,t,i){return y="radial",t&&i&&(t=f(t),i=f(i),v(t-.5,2)+v(i-.5,2)>.25&&(i=c.sqrt(.25-v(t-.5,2))*((i>.5)*2-1)+.5),p=t+o+i),u}),r=r.split(/\s*\-\s*/),y=="linear"&&(l=r.shift(),l=-f(l),isNaN(l)))||(s=i._parseDots(r),!s))return null;if(n=n.shape||n.node,s.length){for(n.removeChild(e),e.on=!0,e.method="none",e.color=s[0].color,e.color2=s[s.length-1].color,a=[],h=0,w=s.length;h<w;h++)s[h].offset&&a.push(s[h].offset+o+s[h].color);e.colors=a.length?a.join():"0% "+e.color;y=="radial"?(e.type="gradientTitle",e.focus="100%",e.focussize="0 0",e.focusposition=p,e.angle=0):(e.type="gradient",e.angle=(270-l)%360);n.appendChild(e)}return 1},b=function(n,t){this[0]=this.node=n;n.raphael=!0;this.id=i._oid++;n.raphaelid=this.id;this.X=0;this.Y=0;this.attrs={};this.paper=t;this.matrix=i.matrix();this._={transform:[],sx:1,sy:1,dx:0,dy:0,deg:0,dirty:1,dirtyT:1};t.bottom||(t.bottom=this);this.prev=t.top;t.top&&(t.top.next=this);t.top=this;this.next=null},r=i.el;b.prototype=r;r.constructor=b;r.transform=function(r){var e,a,l;if(r==null)return this._.transform;e=this.paper._viewBoxShift;a=e?"s"+[e.scale,e.scale]+"-1-1t"+[e.dx,e.dy]:u;e&&(l=r=t(r).replace(/\.{3}|\u2026/g,this._.transform||u));i._extractTransform(this,a+r);var s=this.matrix.clone(),h=this.skew,c=this.node,f,v=~t(this.attrs.fill).indexOf("-"),d=!t(this.attrs.fill).indexOf("url(");if(s.translate(1,1),d||v||this.type=="image")if(h.matrix="1 0 0 1",h.offset="0 0",f=s.split(),v&&f.noRotation||!f.isSimple){c.style.filter=s.toFilter();var y=this.getBBox(),p=this.getBBox(1),b=y.x-p.x,k=y.y-p.y;c.coordorigin=b*-n+o+k*-n;w(this,1,1,b,k,0)}else c.style.filter=u,w(this,f.scalex,f.scaley,f.dx,f.dy,f.rotate);else c.style.filter=u,h.matrix=t(s),h.offset=s.offset();return l&&(this._.transform=l),this};r.rotate=function(n,i,r){if(this.removed)return this;if(n!=null){if(n=t(n).split(a),n.length-1&&(i=f(n[1]),r=f(n[2])),n=f(n[0]),r==null&&(i=r),i==null||r==null){var u=this.getBBox(1);i=u.x+u.width/2;r=u.y+u.height/2}return this._.dirtyT=1,this.transform(this._.transform.concat([["r",n,i,r]])),this}};r.translate=function(n,i){return this.removed?this:(n=t(n).split(a),n.length-1&&(i=f(n[1])),n=f(n[0])||0,i=+i||0,this._.bbox&&(this._.bbox.x+=n,this._.bbox.y+=i),this.transform(this._.transform.concat([["t",n,i]])),this)};r.scale=function(n,i,r,u){if(this.removed)return this;if(n=t(n).split(a),n.length-1&&(i=f(n[1]),r=f(n[2]),u=f(n[3]),isNaN(r)&&(r=null),isNaN(u)&&(u=null)),n=f(n[0]),i==null&&(i=n),u==null&&(r=u),r==null||u==null)var e=this.getBBox(1);return r=r==null?e.x+e.width/2:r,u=u==null?e.y+e.height/2:u,this.transform(this._.transform.concat([["s",n,i,r,u]])),this._.dirtyT=1,this};r.hide=function(){return this.removed||(this.node.style.display="none"),this};r.show=function(){return this.removed||(this.node.style.display=u),this};r._getBBox=function(){return this.removed?{}:{x:this.X+(this.bbx||0)-this.W/2,y:this.Y-this.H,width:this.W,height:this.H}};r.remove=function(){if(!this.removed&&this.node.parentNode){this.paper.__set__&&this.paper.__set__.exclude(this);i.eve.unbind("raphael.*.*."+this.id);i._tear(this,this.paper);this.node.parentNode.removeChild(this.node);this.shape&&this.shape.parentNode.removeChild(this.shape);for(var n in this)this[n]=typeof this[n]=="function"?i._removedFactory(n):null;this.removed=!0}};r.attr=function(n,t){var f,c,y,o,e,s,r,u,p,w;if(this.removed)return this;if(n==null){f={};for(c in this.attrs)this.attrs[h](c)&&(f[c]=this.attrs[c]);return f.gradient&&f.fill=="none"&&(f.fill=f.gradient)&&delete f.gradient,f.transform=this._.transform,f}if(t==null&&i.is(n,"string")){if(n==l&&this.attrs.fill=="none"&&this.attrs.gradient)return this.attrs.gradient;for(y=n.split(a),o={},e=0,s=y.length;e<s;e++)n=y[e],o[n]=n in this.attrs?this.attrs[n]:i.is(this.paper.customAttributes[n],"function")?this.paper.customAttributes[n].def:i._availableAttrs[n];return s-1?o:o[y[0]]}if(this.attrs&&t==null&&i.is(n,"array")){for(o={},e=0,s=n.length;e<s;e++)o[n[e]]=this.attr(n[e]);return o}t!=null&&(r={},r[n]=t);t==null&&i.is(n,"object")&&(r=n);for(u in r)ut("raphael.attr."+u+"."+this.id,this,r[u]);if(r){for(u in this.paper.customAttributes)if(this.paper.customAttributes[h](u)&&r[h](u)&&i.is(this.paper.customAttributes[u],"function")){p=this.paper.customAttributes[u].apply(this,[].concat(r[u]));this.attrs[u]=r[u];for(w in p)p[h](w)&&(r[w]=p[w])}r.text&&this.type=="text"&&(this.textpath.string=r.text);v(this,r)}return this};r.toFront=function(){return this.removed||this.node.parentNode.appendChild(this.node),this.paper&&this.paper.top!=this&&i._tofront(this,this.paper),this};r.toBack=function(){return this.removed?this:(this.node.parentNode.firstChild!=this.node&&(this.node.parentNode.insertBefore(this.node,this.node.parentNode.firstChild),i._toback(this,this.paper)),this)};r.insertAfter=function(n){return this.removed?this:(n.constructor==i.st.constructor&&(n=n[n.length-1]),n.node.nextSibling?n.node.parentNode.insertBefore(this.node,n.node.nextSibling):n.node.parentNode.appendChild(this.node),i._insertafter(this,n,this.paper),this)};r.insertBefore=function(n){return this.removed?this:(n.constructor==i.st.constructor&&(n=n[0]),n.node.parentNode.insertBefore(this.node,n.node),i._insertbefore(this,n,this.paper),this)};r.blur=function(n){var t=this.node.runtimeStyle,r=t.filter;return r=r.replace(ot,u),+n!=0?(this.attrs.blur=n,t.filter=r+o+ft+".Blur(pixelradius="+(+n||1.5)+")",t.margin=i.format("-{0}px 0 0 -{0}px",e(+n||1.5))):(t.filter=r,t.margin=0,delete this.attrs.blur),this};i._engine.path=function(t,i){var f=s("shape"),r,h,e;return f.style.cssText=tt,f.coordsize=n+o+n,f.coordorigin=i.coordorigin,r=new b(f,i),h={fill:"none",stroke:"#000"},t&&(h.path=t),r.type="path",r.path=[],r.Path=u,v(r,h),i.canvas.appendChild(f),e=s("skew"),e.on=!0,f.appendChild(e),r.skew=e,r.transform(u),r};i._engine.rect=function(n,t,r,u,f,e){var h=i._rectPath(t,r,u,f,e),o=n.path(h),s=o.attrs;return o.X=s.x=t,o.Y=s.y=r,o.W=s.width=u,o.H=s.height=f,s.r=e,s.path=h,o.type="rect",o};i._engine.ellipse=function(n,t,i,r,u){var f=n.path(),e=f.attrs;return f.X=t-r,f.Y=i-u,f.W=r*2,f.H=u*2,f.type="ellipse",v(f,{cx:t,cy:i,rx:r,ry:u}),f};i._engine.circle=function(n,t,i,r){var u=n.path(),f=u.attrs;return u.X=t-r,u.Y=i-r,u.W=u.H=r*2,u.type="circle",v(u,{cx:t,cy:i,r:r}),u};i._engine.image=function(n,t,r,u,f,e){var a=i._rectPath(r,u,f,e),o=n.path(a).attr({stroke:"none"}),s=o.attrs,c=o.node,h=c.getElementsByTagName(l)[0];return s.src=t,o.X=s.x=r,o.Y=s.y=u,o.W=s.width=f,o.H=s.height=e,s.path=a,o.type="image",h.parentNode==c&&c.removeChild(h),h.rotate=!0,h.src=t,h.type="tile",o._.fillpos=[r,u],o._.fillsize=[f,e],c.appendChild(h),w(o,1,1,0,0,0),o};i._engine.text=function(r,f,h,c){var a=s("shape"),y=s("path"),p=s("textpath"),l,k,w;return f=f||0,h=h||0,c=c||"",y.v=i.format("m{0},{1}l{2},{1}",e(f*n),e(h*n),e(f*n)+1),y.textpathok=!0,p.string=t(c),p.on=!0,a.style.cssText=tt,a.coordsize=n+o+n,a.coordorigin="0 0",l=new b(a,r),k={fill:"#000",stroke:"none",font:i._availableAttrs.font,text:c},l.shape=a,l.path=y,l.textpath=p,l.type="text",l.attrs.text=t(c),l.attrs.x=f,l.attrs.y=h,l.attrs.w=1,l.attrs.h=1,v(l,k),a.appendChild(p),a.appendChild(y),r.canvas.appendChild(a),w=s("skew"),w.on=!0,a.appendChild(w),l.skew=w,l.transform(u),l};i._engine.setSize=function(n,t){var r=this.canvas.style;return this.width=n,this.height=t,n==+n&&(n+="px"),t==+t&&(t+="px"),r.width=n,r.height=t,r.clip="rect(0 "+n+" "+t+" 0)",this._viewBox&&i._engine.setViewBox.apply(this,this._viewBox),this};i._engine.setViewBox=function(n,t,r,u,f){i.eve("raphael.setViewBox",this,this._viewBox,[n,t,r,u,f]);var e=this.width,o=this.height,c=1/k(r/e,u/o),s,h;return f&&(s=o/u,h=e/r,r*s<e&&(n-=(e-r*s)/2/s),u*h<o&&(t-=(o-u*h)/2/h)),this._viewBox=[n,t,r,u,!!f],this._viewBoxShift={dx:-n,dy:-t,scale:c},this.forEach(function(n){n.transform("...")}),this};i._engine.initWin=function(n){var t=n.document;t.createStyleSheet().addRule(".rvml","behavior:url(#default#VML)");try{t.namespaces.rvml||t.namespaces.add("rvml","urn:schemas-microsoft-com:vml");s=function(n){return t.createElement("<rvml:"+n+' class="rvml">')}}catch(i){s=function(n){return t.createElement("<"+n+' xmlns="urn:schemas-microsoft.com:vml" class="rvml">')}}};i._engine.initWin(i._g.win);i._engine.create=function(){var e=i._getContainer.apply(0,arguments),f=e.container,r=e.height,u=e.width,c=e.x,l=e.y;if(!f)throw new Error("VML container not found.");var t=new i._Paper,s=t.canvas=i._g.doc.createElement("div"),h=s.style;return c=c||0,l=l||0,u=u||512,r=r||342,t.width=u,t.height=r,u==+u&&(u+="px"),r==+r&&(r+="px"),t.coordsize=n*1e3+o+n*1e3,t.coordorigin="0 0",t.span=i._g.doc.createElement("span"),t.span.style.cssText="position:absolute;left:-9999em;top:-9999em;padding:0;margin:0;line-height:1;",s.appendChild(t.span),h.cssText=i.format("top:0;left:0;width:{0};height:{1};display:inline-block;position:relative;clip:rect(0 {0} {1} 0);overflow:hidden",u,r),f==1?(i._g.doc.body.appendChild(s),h.left=c+"px",h.top=l+"px",h.position="absolute"):f.firstChild?f.insertBefore(s,f.firstChild):f.appendChild(s),t.renderfix=function(){},t};i.prototype.clear=function(){i.eve("raphael.clear",this);this.canvas.innerHTML=u;this.span=i._g.doc.createElement("span");this.span.style.cssText="position:absolute;left:-9999em;top:-9999em;padding:0;margin:0;line-height:1;display:inline;";this.canvas.appendChild(this.span);this.bottom=this.top=null};i.prototype.remove=function(){i.eve("raphael.remove",this);this.canvas.parentNode.removeChild(this.canvas);for(var n in this)this[n]=typeof this[n]=="function"?i._removedFactory(n):null;return!0};d=i.st;for(y in r)r[h](y)&&!d[h](y)&&(d[y]=function(n){return function(){var t=arguments;return this.forEach(function(i){i[n].apply(i,t)})}}(y))}}(),yi.was?r.win.Raphael=i:Raphael=i,i});
//# sourceMappingURL=raphael.min.js.map

