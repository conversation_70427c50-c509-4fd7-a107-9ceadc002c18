var DOMESTIC_FLIGHTS = [
"Aberdeen, SD (ABR)",
"Abilene, TX (ABI)",
"Adak Island, AK (ADK)",
"Akiachak, AK (KKI)",
"Akiak, AK (AKI)",
"Akron/Canton, OH (CAK)",
"Akuton, AK (KQA)",
"Alakanuk, AK (AUK)",
"Alamogordo, NM (ALM)",
"Alamosa, CO (ALS)",
"Albany, NY (ALB)",
"Albany, OR - Bus service (CVO)",
"Albany, OR - Bus service (QWY)",
"Albuquerque, NM (ABQ)",
"Aleknagik, AK (WKK)",
"Alexandria, LA (AEX)",
"Allakaket, AK (AET)",
"Allentown, PA (ABE)",
"Alliance, NE (AIA)",
"Alpena, MI (APN)",
"Altoona, PA (AOO)",
"Amarillo, TX (AMA)",
"Ambler, AK (ABL)",
"Anaktueuk, AK (AKP)",
"Anchorage, AK (ANC)",
"Angoon, AK (AGN)",
"Aniak, AK (ANI)",
"Anvik, AK (ANV)",
"Appleton, WI (ATW)",
"Arcata, CA (ACV)",
"Arctic Village, AK (ARC)",
"Asheville, NC (AVL)",
"Ashland, KY/Huntington, WV (HTS)",
"Aspen, CO (ASE)",
"Athens, GA (AHN)",
"Atka, AK (AKB)",
"Atlanta, GA (ATL)",
"Atlantic City, NJ (AIY)",
"Atqasuk, AK (ATK)",
"Augusta, GA (AGS)",
"Augusta, ME (AUG)",
"Austin, TX (AUS)",
"Bakersfield, CA (BFL)",
"Baltimore, MD (BWI)",
"Bangor, ME (BGR)",
"Bar Harbour, ME (BHB)",
"Barrow, AK (BRW)",
"Barter Island, AK (BTI)",
"Baton Rouge, LA (BTR)",
"Bay City, MI (MBS)",
"Beaumont/Port Arthur, TX (BPT)",
"Beaver Creek, CO - Van service (ZBV)",
"Beaver, AK (WBQ)",
"Beckley, WV (BKW)",
"Bedford, MA (BED)",
"Belleville, IL (BLV)",
"Bellingham, WA (BLI)",
"Bemidji, MN (BJI)",
"Benton Harbor, MI (BEH)",
"Bethel, AK (BET)",
"Bethlehem, PA (ABE)",
"Bettles, AK (BTT)",
"Billings, MT (BIL)",
"Biloxi/Gulfport, MS (GPT)",
"Binghamton, NY (BGM)",
"Birch Creek, AK (KBC)",
"Birmingham, AL (BHM)",
"Bismarck, ND (BIS)",
"Block Island, RI (BID)",
"Bloomington, IL (BMI)",
"Bluefield, WV (BLF)",
"Boise, ID (BOI)",
"Boston, MA (BOS)",
"Boulder, CO - Bus service (XHH)",
"Boulder, CO - Hiltons Har H (WHH)",
"Boulder, CO - Municipal Airport (WBU)",
"Boundary, AK (BYA)",
"Bowling Green, KY (BWG)",
"Bozeman, MT (BZN)",
"Bradford, PA (BFD)",
"Brainerd, MN (BRD)",
"Brawnwood, TX (BWD)",
"Breckonridge, CO - Van service (QKB)",
"Bristol, VA (TRI)",
"Brookings, SD (BKX)",
"Brooks Lodge, AK (RBH)",
"Brownsville, TX (BRO)",
"Brunswick, GA (BQK)",
"Buckland, AK (BKC)",
"Buffalo, NY (BUF)",
"Bullhead City/Laughlin, AZ (IFP)",
"Burbank, CA (BUR)",
"Burlington, IA (BRL)",
"Burlington, VT (BTV)",
"Butte, MT (BTM)",
"Canton/Akron, OH (CAK)",
"Cape Girardeau, MO (CGI)",
"Cape Lisburne, AK (LUR)",
"Cape Newenham, AK (EHM)",
"Carbondale, IL (MDH)",
"Carlsbad, CA (CLD)",
"Carlsbad, NM (CNM)",
"Carmel, CA (MRY)",
"Casper, WY (CPR)",
"Cedar City, UT (CDC)",
"Cedar Rapids, IA (CID)",
"Central, AK (CEM)",
"Chadron, NE (CDR)",
"Chalkyitsik, AK (CIK)",
"Champaign/Urbana, IL (CMI)",
"Charleston, SC (CHS)",
"Charleston, WV (CRW)",
"Charlotte, NC (CLT)",
"Charlottesville, VA (CHO)",
"Chattanooga, TN (CHA)",
"Chefornak, AK (CYF)",
"Chevak, AK (VAK)",
"Cheyenne, WY (CYS)",
"Chicago, IL - Meigs (CGX)",
"Chicago, IL - All airports (CHI)",
"Chicago, IL - Midway (MDW)",
"Chicago, IL - O'Hare (ORD)",
"Chicken, AK (CKX)",
"Chico, CA (CIC)",
"Chignik, AK - Fisheries (KCG)",
"Chignik, AK - (KCQ)",
"Chignik, AK - Lagoon (KCL)",
"Chisana, AK (CZN)",
"Chisholm/Hibbing, MN (HIB)",
"Chuathbaluk, AK (CHU)",
"Cincinnati, OH (CVG)",
"Circle Hot Springs, AK (CHP)",
"Circle, AK (IRC)",
"Clarks Point, AK (CLP)",
"Clarksburg, WV (CKB)",
"Clearwater/St Petersburg, FL (PIE)",
"Cleveland, OH (CLE)",
"Clovis, NM (CVN)",
"Cody/Yellowstone, WY (COD)",
"Coffee Point, AK (CFA)",
"Coffman Cove, AK (KCC)",
"Cold Bay, AK (CDB)",
"College Station, TX (CLL)",
"Colorado Springs, CO (COS)",
"Columbia, MO (COU)",
"Columbia, SC (CAE)",
"Columbus, GA (CSG)",
"Columbus, MS (GTR)",
"Columbus, OH (CMH)",
"Concord, CA (CCR)",
"Concordia, KS (CNK)",
"Copper Mountain, CO - Van service (QCE)",
"Cordova, AK (CDV)",
"Corpus Christi, TX (CRP)",
"Cortez, CO (CEZ)",
"Craig, AK (CGA)",
"Crescent City, CA (CEC)",
"Crooked Creek, AK (CKO)",
"Cube Cove, AK (CUW)",
"Cumberland, MD (CBE)",
"Dallas/Fort Worth, TX (DFW)",
"Dayton, OH (DAY)",
"Daytona Beach, FL (DAB)",
"Decatur, IL (DEC)",
"Deering, AK (DRG)",
"Delta Junction, AK (DJN)",
"Denver, CO - International (DEN)",
"Denver, CO - Longmont Bus service (QWM)",
"Des Moines, IA (DSM)",
"Detroit, MI - All airports (DTT)",
"Detroit, MI - Metro/Wayne County (DTW)",
"Devil's Lake, ND (DVL)",
"Dickinson, ND (DIK)",
"Dillingham, AK (DLG)",
"Dodge City, KS (DDC)",
"Dothan, AL (DHN)",
"Dubois, PA (DUJ)",
"Dubuque, IA (DBQ)",
"Duluth, MN (DLH)",
"Durango, CO (DRO)",
"Durham, NC (RDU)",
"Durham/Raleigh, NC (RDU)",
"Dutch Harbor, AK (DUT)",
"Easton, PA (ABE)",
"Eau Claire, WI (EAU)",
"Edna Bay, AK (EDA)",
"Eek, AK (EEK)",
"Ekuk, AK (KKU)",
"Ekwok, AK (KEK)",
"El Centro, CA (IPL)",
"El Dorado, AR (ELD)",
"El Paso, TX (ELP)",
"Elfin Cove, AK (ELV)",
"Elim, AK (ELI)",
"Elko, NV (EKO)",
"Elmira, NY (ELM)",
"Ely, MN (LYU)",
"Emmonak, AK (EMK)",
"Endicott, NY (BGM)",
"Enid, OK (WDG)",
"Erie, PA (ERI)",
"Escanaba, MI (ESC)",
"Eugene, OR (EUG)",
"Eureka/Arcata, CA (ACV)",
"Eureka, NV (EUE)",
"Evansville, IN (EVV)",
"Fairbanks, AK (FAI)",
"Fargo, ND (FAR)",
"Fayetteville, AR - Municipal/Drake (FYV)",
"Fayetteville, AR - Northwest Arkansas Regional (XNA)",
"Fayetteville, NC (FAY)",
"Flagstaff, AZ (FLG)",
"Flint, MI (FNT)",
"Florence, SC (FLO)",
"Florence/Muscle Shoals/Sheffield, AL (MSL)",
"Fort Collins/Loveland, CO - Municipal Airport (FNL)",
"Fort Collins/Loveland, CO - Bus service (QWF)",
"Fort Dodge, IA (FOD)",
"Fort Lauderdale, FL (FLL)",
"Fort Leonard Wood, MO (TBN)",
"Fort Myers, FL (RSW)",
"Fort Smith, AR (FSM)",
"Fort Walton Beach, FL (VPS)",
"Fort Wayne, IN (FWA)",
"Fort Worth/Dallas, TX (DFW)",
"Franklin, PA (FKL)",
"Fresno, CA (FAT)",
"Gainesville, FL (GNV)",
"Gallup, NM (GUP)",
"Garden City, KS (GCK)",
"Gary, IN (GYY)",
"Gillette, WY (GCC)",
"Gladewater/Kilgore, TX (GGG)",
"Glasgow, MT (GGW)",
"Glendive, MT (GDV)",
"Golovin, AK (GLV)",
"Goodnews Bay, AK (GNU)",
"Grand Canyon, AZ - Heliport (JGC)",
"Grand Canyon, AZ - National Park (GCN)",
"Grand Forks, ND (GFK)",
"Grand Island, NE (GRI)",
"Grand Junction, CO (GJT)",
"Grand Rapids, MI (GRR)",
"Grand Rapids, MN (GPZ)",
"Grayling, AK (KGX)",
"Great Falls, MT (GTF)",
"Green Bay, WI (GRB)",
"Greensboro, NC (GSO)",
"Greenville, MS (GLH)",
"Greenville, NC (PGV)",
"Greenville/Spartanburg, SC (GSP)",
"Groton/New London, CT (GON)",
"Gulfport, MS (GPT)",
"Gunnison, CO (GUC)",
"Gustavus, AK (GST)",
"Hagerstown, MD (HGR)",
"Hailey, ID (SUN)",
"Haines, AK (HNS)",
"Hampton, VA (PHF)",
"Hana, HI - Island of Maui (HNM)",
"Hanapepe, HI (PAK)",
"Hancock, MI (CMX)",
"Hanover, NH (LEB)",
"Harlingen, TX (HRL)",
"Harrisburg, PA (MDT)",
"Harrison, AR (HRO)",
"Hartford, CT (BDL)",
"Havasupai, AZ (HAE)",
"Havre, MT (HVR)",
"Hayden, CO (HDN)",
"Hays, KS (HYS)",
"Healy Lake, AK (HKB)",
"Helena, MT (HLN)",
"Hendersonville, NC (AVL)",
"Hibbing/Chisholm, MN (HIB)",
"Hickory, NC (HKY)",
"High Point, NC (GSO)",
"Hilo, HI - Island of Hawaii (ITO)",
"Hilton Head, SC (HHH)",
"Hobbs, NM (HBB)",
"Hollis, AK (HYL)",
"Holy Cross, AK (HCR)",
"Homer, AK (HOM)",
"Honolulu, HI - Island of Oahu (HNL)",
"Hoolehua, HI - Island of Molokai (MKK)",
"Hoonah, AK (HNH)",
"Hooper Bay, AK (HPB)",
"Hot Springs, AR (HOT)",
"Houston, TX - All airports (HOU)",
"Houston, TX - Hobby (HOU)",
"Houston, TX - Intercontinental (IAH)",
"Hughes, AK (HUS)",
"Huntington, WV/Ashland, KY (HTS)",
"Huntsville, AL (HSV)",
"Huron, SD (HON)",
"Huslia, AK (HSL)",
"Hyannis, MA (HYA)",
"Hydaburg, AK (HYG)",
"Idaho Falls, ID (IDA)",
"Igiugig, AK (IGG)",
"Iliamna, AK (ILI)",
"Imperial, CA (IPL)",
"Indianapolis, IN (IND)",
"International Falls, MN (INL)",
"Inyokern, CA (IYK)",
"Iron Mountain, MI (IMT)",
"Ironwood, MI (IWD)",
"Islip, NY (ISP)",
"Ithaca, NY (ITH)",
"Jackson Hole, WY (JAC)",
"Jackson, MS (JAN)",
"Jackson, TN (MKL)",
"Jacksonville, FL (JAX)",
"Jacksonville, NC (OAJ)",
"Jamestown, ND (JMS)",
"Jamestown, NY (JHW)",
"Janesville, WI (JVL)",
"Johnson City, NY (BGM)",
"Johnson City, TN (TRI)",
"Johnstown, PA (JST)",
"Jonesboro, AR (JBR)",
"Joplin, MO (JLN)",
"Juneau, AK (JNU)",
"Kahului, HI - Island of Maui, (OGG)",
"Kake, AK (KAE)",
"Kakhonak, AK (KNK)",
"Kalamazoo, MI (AZO)",
"Kalaupapa, HI - Island of Molokai, (LUP)",
"Kalskag, AK (KLG)",
"Kaltag, AK (KAL)",
"Kamuela, HI - Island of Hawaii, (MUE)",
"Kansas City, MO (MCI)",
"Kapalua, HI - Island of Maui, (JHM)",
"Kasaan, AK (KXA)",
"Kasigluk, AK (KUK)",
"Kauai Island/Lihue, HI (LIH)",
"Kearney, NE (EAR)",
"Keene, NH (EEN)",
"Kenai, AK (ENA)",
"Ketchikan, AK (KTN)",
"Key West, FL (EYW)",
"Keystone, CO - Van service (QKS)",
"Kiana, AK (IAN)",
"Kilgore/Gladewater, TX (GGG)",
"Killeen, TX (ILE)",
"King Cove, AK (KVC)",
"King Salmon, AK (AKN)",
"Kingman, AZ (IGM)",
"Kingsport, TN (TRI)",
"Kipnuk, AK (KPN)",
"Kirksville, MO (IRK)",
"Kivalina, AK (KVL)",
"Klamath Falls, OR (LMT)",
"Klawock, AK (KLW)",
"Knoxville, TN (TYS)",
"Kobuk, AK (OBU)",
"Kodiak, AK (ADQ)",
"Kona, HI - Island of Hawaii (KOA)",
"Kongiganak, AK (KKH)",
"Kotlik, AK (KOT)",
"Kotzebue, AK (OTZ)",
"Koyukuk, AK (KYU)",
"Kwethluk, AK (KWT)",
"Kwigillingok, AK (KWK)",
"La Crosse, WI (LSE)",
"Lafayette, IN (LAF)",
"Lafayette, LA (LFT)",
"Lake Charles, LA (LCH)",
"Lake Havasu City, AZ (HII)",
"Lake Minchumina, AK (LMA)",
"Lanai City, HI - Island of Lanai (LNY)",
"Lancaster, PA (LNS)",
"Lansing, MI (LAN)",
"Laramie, WY (LAR)",
"Laredo, TX (LRD)",
"Las Vegas, NV (LAS)",
"Latrobe, PA (LBE)",
"Laurel, MS (PIB)",
"Lawton, OK (LAW)",
"Lebanon, NH (LEB)",
"Levelock, AK (KLL)",
"Lewisburg, WV (LWB)",
"Lewiston, ID (LWS)",
"Lewistown, MT (LWT)",
"Lexington, KY (LEX)",
"Liberal, KS (LBL)",
"Lihue, HI - Island of Kaui (LIH)",
"Lincoln, NE (LNK)",
"Little Rock, AR (LIT)",
"Long Beach, CA (LGB)",
"Longview, TX (GGG)",
"Lopez Island, WA (LPS)",
"Los Angeles, CA (LAX)",
"Louisville, KY (SDF)",
"Loveland/Fort Collins, CO - Municipal Airport (FNL)",
"Loveland/Fort Collins, CO - Bus service (QWF)",
"Lubbock, TX (LBB)",
"Macon, GA (MCN)",
"Madison, WI (MSN)",
"Madras, OR (MDJ)",
"Manchester, NH (MHT)",
"Manhattan, KS (MHK)",
"Manistee, MI (MBL)",
"Mankato, MN (MKT)",
"Manley Hot Springs, AK (MLY)",
"Manokotak, AK (KMO)",
"Marietta, OH/Parkersburg, WV (PKB)",
"Marion, IL (MWA)",
"Marquette, MI (MQT)",
"Marshall, AK (MLL)",
"Martha's Vineyard, MA (MVY)",
"Martinsburg, PA (AOO)",
"Mason City, IA (MCW)",
"Massena, NY (MSS)",
"Maui, HI (OGG)",
"Mcallen, TX (MFE)",
"Mccook, NE (MCK)",
"Mcgrath, AK (MCG)",
"Medford, OR (MFR)",
"Mekoryuk, AK (MYU)",
"Melbourne, FL (MLB)",
"Memphis, TN (MEM)",
"Merced, CA (MCE)",
"Meridian, MS (MEI)",
"Metlakatla, AK (MTM)",
"Meyers Chuck, AK (WMK)",
"Miami, FL - International (MIA)",
"Miami, FL - Sea Plane Base (MPB)",
"Midland, MI (MBS)",
"Midland/Odessa, TX (MAF)",
"Miles City, MT (MLS)",
"Milwaukee, WI (MKE)",
"Minneapolis, MN (MSP)",
"Minot, ND (MOT)",
"Minto, AK (MNT)",
"Mission, TX (MFE)",
"Missoula, MT (MSO)",
"Moab, UT (CNY)",
"Mobile, AL (MOB)",
"Modesto, CA (MOD)",
"Moline, IL (MLI)",
"Monroe, LA (MLU)",
"Monterey, CA (MRY)",
"Montgomery, AL (MGM)",
"Montrose, CO (MTJ)",
"Morgantown, WV (MGW)",
"Moses Lake, WA (MWH)",
"Mountain Home, AR (WMH)",
"Mountain Village, AK (MOU)",
"Muscle Shoals, AL (MSL)",
"Muskegon, MI (MKG)",
"Myrtle Beach, SC (MYR)",
"Nantucket, MA (ACK)",
"Napakiak, AK (WNA)",
"Napaskiak, AK (PKA)",
"Naples, FL (APF)",
"Nashville, TN (BNA)",
"Naukiti, AK (NKI)",
"Nelson Lagoon, AK (NLG)",
"New Chenega, AK (NCN)",
"New Haven, CT (HVN)",
"New Koliganek, AK (KGK)",
"New London/Groton (GON)",
"New Orleans, LA (MSY)",
"New Stuyahok, AK (KNW)",
"New York, NY - All airports (NYC)",
"New York, NY - Kennedy (JFK)",
"New York, NY - La Guardia (LGA)",
"Newark, NJ (EWR)",
"Newburgh/Stewart Field, NY (SWF)",
"Newport News, VA (PHF)",
"Newtok, AK (WWT)",
"Nightmute, AK (NME)",
"Nikolai, AK (NIB)",
"Nikolski, AK (IKO)",
"Noatak, AK (WTK)",
"Nome, AK (OME)",
"Nondalton, AK (NNL)",
"Noorvik, AK (ORV)",
"Norfolk, NE (OFK)",
"Norfolk, VA (ORF)",
"North Bend, OR (OTH)",
"North Platte, NE (LBF)",
"Northway, AK (ORT)",
"Nuiqsut, AK (NUI)",
"Nulato, AK (NUL)",
"Nunapitchuk, AK (NUP)",
"Oakland, CA (OAK)",
"Odessa/Midland, TX (MAF)",
"Ogdensburg, NY (OGS)",
"Oklahoma City, OK (OKC)",
"Omaha, NE (OMA)",
"Ontario, CA (ONT)",
"Orange County, CA (SNA)",
"Orlando, FL - Herndon (ORL)",
"Orlando, FL - International (MCO)",
"Oshkosh, WI (OSH)",
"Ottumwa, IA (OTM)",
"Owensboro, KY (OWB)",
"Oxnard/Ventura, CA (OXR)",
"Paducah, KY (PAH)",
"Page, AZ (PGA)",
"Palm Springs, CA (PSP)",
"Panama City, FL (PFN)",
"Parkersburg, WV/Marietta, OH (PKB)",
"Pasco, WA (PSC)",
"Pedro Bay, AK (PDB)",
"Pelican, AK (PEC)",
"Pellston, MI (PLN)",
"Pendleton, OR (PDT)",
"Pensacola, FL (PNS)",
"Peoria, IL (PIA)",
"Perryville, AK (KPV)",
"Petersburg, AK (PSG)",
"Philadelphia, PA - International (PHL)",
"Philadelphia, PA - Trenton/Mercer NJ (TTN)",
"Phoenix, AZ (PHX)",
"Pierre, SD (PIR)",
"Pilot Point, AK - Ugashnik Bay (UGB)",
"Pilot Point, AK (PIP)",
"Pilot Station, AK (PQS)",
"Pittsburgh, PA (PIT)",
"Platinum, AK (PTU)",
"Plattsburgh, NY (PLB)",
"Pocatello, ID (PIH)",
"Point Baker, AK (KPB)",
"Point Hope, AK (PHO)",
"Point Lay, AK (PIZ)",
"Ponca City, OK (PNC)",
"Ponce, Puerto Rico (PSE)",
"Port Alsworth, AK (PTA)",
"Port Angeles, WA (CLM)",
"Port Arthur/Beaumont, TX (BPT)",
"Port Clarence, AK (KPC)",
"Port Heiden, AK (PTH)",
"Port Moller, AK (PML)",
"Port Protection, AK (PPV)",
"Portage Creek, AK (PCA)",
"Portland, ME (PWM)",
"Portland, OR (PDX)",
"Portsmouth, NH (PSM)",
"Poughkeepsie, NY (POU)",
"Prescott, AZ (PRC)",
"Presque Isle, ME (PQI)",
"Princeton, WV (BLF)",
"Providence, RI (PVD)",
"Provincetown, MA (PVC)",
"Prudhoe Bay/Deadhorse, AK (SCC)",
"Pueblo, CO (PUB)",
"Pullman, WA (PUW)",
"Quincy, IL (UIN)",
"Quinhagak, AK (KWN)",
"Raleigh/Durham, NC (RDU)",
"Rampart, AK (RMP)",
"Rapid City, SD (RAP)",
"Reading, PA (RDG)",
"Red Devil, AK (RDV)",
"Redding, CA (RDD)",
"Redmond, OR (RDM)",
"Reno, NV (RNO)",
"Rhinelander, WI, (RHI)",
"Richmond, VA (RIC)",
"Riverton, WY (RIW)",
"Roanoke, VA (ROA)",
"Roche Harbor, WA (RCE)",
"Rochester, MN (RST)",
"Rochester, NY (ROC)",
"Rock Springs, WY (RKS)",
"Rockford, IL - Park&Ride Bus (ZRF)",
"Rockford, IL - Van Galder Bus (ZRK)",
"Rockland, ME (RKD)",
"Rosario, WA (RSJ)",
"Roswell, NM (ROW)",
"Ruby, AK (RBY)",
"Russian Mission, AK (RSH)",
"Rutland, VT (RUT)",
"Sacramento, CA (SMF)",
"Saginaw, MI (MBS)",
"Saint Cloud, MN (STC)",
"Saint George Island, AK (STG)",
"Saint George, UT (SGU)",
"Saint Louis, MO (STL)",
"Saint Mary's, AK (KSM)",
"Saint Michael, AK (SMK)",
"Saint Paul Island, AK (SNP)",
"Salem, OR (SLE)",
"Salina, KS (SLN)",
"Salisbury-Ocean City, MD (SBY)",
"Salt Lake City, UT (SLC)",
"San Angelo, TX (SJT)",
"San Antonio, TX (SAT)",
"San Diego, CA (SAN)",
"San Francisco, CA (SFO)",
"San Jose, CA (SJC)",
"San Juan, Puerto Rico (SJU)",
"San Luis Obispo, CA (SBP)",
"Sand Point, AK (SDP)",
"Santa Ana, CA (SNA)",
"Santa Barbara, CA (SBA)",
"Santa Fe, NM (SAF)",
"Santa Maria, CA (SMX)",
"Santa Rosa, CA (STS)",
"Saranac Lake, NY (SLK)",
"Sarasota, FL (SRQ)",
"Sault Ste Marie, MI, (CIU)",
"Savannah, GA (SAV)",
"Savoonga, AK (SVA)",
"Scammon Bay, AK (SCM)",
"Scottsbluff, NE (BFF)",
"Scottsdale, AZ (SDL)",
"Scranton, PA (AVP)",
"Seattle, WA - Lake Union SPB (LKE)",
"Seattle, WA - Seattle/Tacoma International (SEA)",
"Selawik, AK (WLK)",
"Seward, AK (SWD)",
"Shageluk, AK (SHX)",
"Shaktoolik, AK (SKK)",
"Sheffield/Florence/Muscle Shoals, AL (MSL)",
"Sheldon Point, AK (SXP)",
"Sheridan, WY (SHR)",
"Shishmaref, AK (SHH)",
"Shreveport, LA (SHV)",
"Shungnak, AK (SHG)",
"Silver City, NM (SVC)",
"Sioux City, IA (SUX)",
"Sioux Falls, SD (FSD)",
"Sitka, AK (SIT)",
"Skagway, AK (SGY)",
"Sleetmore, AK (SLQ)",
"South Bend, IN (SBN)",
"South Naknek, AK (WSN)",
"Southern Pines, NC (SOP)",
"Spartanburg/Greenville, SC (GSP)",
"Spokane, WA (GEG)",
"Springfield, IL (SPI)",
"Springfield, MO (SGF)",
"St Petersburg/Clearwater, FL (PIE)",
"State College/University Park, PA (SCE)",
"Staunton, VA (SHD)",
"Steamboat Springs, CO (SBS)",
"Stebbins, AK (WBB)",
"Stevens Point/Wausau, WI (CWA)",
"Stevens Village, AK (SVS)",
"Stewart Field/Newburgh, NY (SWF)",
"Stockton, CA (SCK)",
"Stony River, AK (SRV)",
"Sun Valley, ID (SUN)",
"Syracuse, NY (SYR)",
"Takotna, AK (TCT)",
"Talkeetna, AK (TKA)",
"Tallahassee, FL (TLH)",
"Tampa, FL (TPA)",
"Tanana, AK (TAL)",
"Taos, NM (TSM)",
"Tatitlek, AK (TEK)",
"Teller Mission, AK (KTS)",
"Telluride, CO (TEX)",
"Tenakee Springs, AK (TKE)",
"Terre Haute, IN (HUF)",
"Tetlin, AK (TEH)",
"Texarkana, AR (TXK)",
"Thief River Falls, MN (TVF)",
"Thorne Bay, AK (KTB)",
"Tin City, AK (TNC)",
"Togiak Village, AK (TOG)",
"Tok, AK (TKJ)",
"Toksook Bay, AK (OOK)",
"Toledo, OH (TOL)",
"Topeka, KS (FOE)",
"Traverse City, MI (TVC)",
"Trenton/Mercer, NJ (TTN)",
"Tucson, AZ (TUS)",
"Tulsa, OK (TUL)",
"Tuluksak, AK (TLT)",
"Tuntutuliak, AK (WTL)",
"Tununak, AK (TNK)",
"Tupelo, MS (TUP)",
"Tuscaloosa, AL (TCL)",
"Twin Falls, ID (TWF)",
"Twin Hills, AK (TWA)",
"Tyler, TX (TYR)",
"Unalakleet, AK (UNK)",
"Urbana/Champaign, IL (CMI)",
"Utica, NY (UCA)",
"Utopia Creek, AK (UTO)",
"Vail, CO - Eagle County Airport (EGE)",
"Vail, CO - Van service (QBF)",
"Valdez, AK (VDZ)",
"Valdosta, GA (VLD)",
"Valparaiso, FL (VPS)",
"Venetie, AK (VEE)",
"Ventura/Oxnard, CA (OXR)",
"Vernal, UT (VEL)",
"Victoria, TX (VCT)",
"Visalia, CA (VIS)",
"Waco, TX (ACT)",
"Wainwright, AK (AIN)",
"Wales, AK (WAA)",
"Walla Walla, WA (ALW)",
"Washington DC - All airports (WAS)",
"Washington DC - Dulles (IAD)",
"Washington DC - National (DCA)",
"Waterfall, AK (KWF)",
"Waterloo, IA (ALO)",
"Watertown, NY (ART)",
"Watertown, SD (ATY)",
"Wausau/Stevens Point, WI (CWA)",
"Wenatchee, WA (EAT)",
"West Palm Beach, FL (PBI)",
"West Yellowstone, MT (WYS)",
"Westchester County, NY (HPN)",
"Westerly, RI (WST)",
"Westsound, WA (WSX)",
"Whale Pass, AK (WWP)",
"White Mountain, AK (WMO)",
"White River, VT (LEB)",
"Wichita Falls, TX (SPS)",
"Wichita, KS (ICT)",
"Wilkes Barre, PA (AVP)",
"Williamsburg, VA (PHF)",
"Williamsport, PA (IPT)",
"Williston, ND (ISN)",
"Wilmington, NC (ILM)",
"Windsor Locks, CT (BDL)",
"Worcester, MA (ORH)",
"Worland, WY (WRL)",
"Wrangell, AK (WRG)",
"Yakima, WA (YKM)",
"Yakutat, AK (YAK)",
"Yellowstone/Cody, WY (COD)",
"Youngstown, OH (YNG)",
"Yuma, AZ (YUM)"
];
