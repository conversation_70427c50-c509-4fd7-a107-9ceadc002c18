var TEMPLATES = {
  "forward":{
    "train":[
      "Ask <PERSON><PERSON><PERSON> to forward her messages to DEST.",
      "Automatically forward all emails from NAME to DEST.",
      "Can you forward the email from NAME to DEST please?",
      "Can you please retrieve an email I received from NAME and forward it to DEST?",
      "Copy the email from NAME and send it to DEST.",
      "Could you find <PERSON><PERSON><PERSON>'s email, then forward it to DEST?",
      "Could you forward <PERSON><PERSON><PERSON>'s email to DEST?",
      "DEST must be forwarded email from NAME.",
      "FOrward to DEST the email that NAME sent to me today.",
      "Find <PERSON>AM<PERSON>'s e-mail and send it along to DEST.",
      "Find <PERSON><PERSON><PERSON>'s email and forward it to DEST, please.",
      "Find <PERSON><PERSON><PERSON>'s email and forward to DEST.",
      "Find <PERSON><PERSON><PERSON>'s email and send it to DEST.",
      "Find <PERSON><PERSON><PERSON>'s email, forward to DEST",
      "Find <PERSON>AM<PERSON>'s last email and forward it to DEST.",
      "Find an email from NAME and send it to DEST",
      "Find the NAME email and forward it to DEST.",
      "Find the e-mail from NAM<PERSON> and send it to DEST.",
      "Find the email by <PERSON>AM<PERSON> and forward it to DEST.",
      "Find the email by NAME and send it on to DEST.",
      "Find the email from <PERSON><PERSON> and forward it to NAME.",
      "Find the email from NAME and forward it to DEST.",
      "Find the email from NAME and give it to DEST.",
      "Find the email from NAME and send it to DEST.",
      "Find the email that NAME sent and forward it to DEST.",
      "Find the first email from NAME and forward it to DEST.",
      "Find the last email NAME sent and forward it to DEST.",
      "Find yesterday's emails from DEST and forward them to NAME.",
      "Forward DEST NAME's latest email.",
      "Forward DEST a copy of NAME's e-mail.",
      "Forward DEST all of NAME's emails.",
      "Forward DEST email from NAME.",
      "Forward DEST the email from NAME.",
      "Forward DEST the latest email from NAME.",
      "Forward DEST's email from yesterday to NAME.",
      "Forward NAME's e-mail to DEST.",
      "Forward NAME's email that is in the email app to DEST.",
      "Forward NAME's email to DEST",
      "Forward NAME's email to DEST.",
      "Forward all messages from DEST to NAME.",
      "Forward email from NAME to DEST",
      "Forward email from NAME to DEST.",
      "Forward email to DEST from NAME.",
      "Forward most recent email from NAME to DEST.",
      "Forward the email from NAME in the email app to DEST.",
      "Forward the email from NAME to DEST.",
      "Forward the email in the inbox app from NAME to DEST.",
      "Forward the last email from NAME to DEST.",
      "Foward the email by NAME to DEST please.",
      "Give DEST NAME's Email",
      "Give DEST the email from NAME.",
      "Go ahead and forward to DEST the e-mail that I received from NAME.",
      "Go ahead and send DEST the e-mail I got from NAME.",
      "I command you to give DEST the email from NAME.",
      "I need an email written to me by NAME sent to DEST",
      "I need to forward NAME's e-mail to DEST.",
      "I need to forward NAME's message to DEST",
      "I need you to forward the email to DEST from NAME.",
      "I need you to take NAME's email and give it to DEST.",
      "I want DEST to be forwarded email from NAME.",
      "I want to forward NAME's email to DEST.",
      "I want to forward the email from NAME over to DEST.",
      "I want to send DEST the e-mail that I got from NAME.",
      "I want to send NAME's email to DEST.",
      "I want to send the email I got from NAME over to DEST.",
      "I would like you to send NAME's email to DEST.",
      "I'd like to email DEST the email I got from NAME.",
      "I'd like to send DEST the email I got from NAME.",
      "I'd like to send the email from NAME to DEST's email.",
      "In my inbox, there should be an email from NAME, it needs to be forwarded to DEST.",
      "Let DEST receive the email message by NAME.",
      "Let me send NAME's email to DEST.",
      "Let me send the email that I received from NAME over to DEST.",
      "Locate NAME's email and forward it to DEST.",
      "Locate email from NAME and forward it to DEST.",
      "Locate email from NAME and send to DEST.",
      "Look for the email that NAME wrote in this email app and forward it to DEST",
      "Make sure DEST recieve emails from NAME in primary.",
      "Make sure the email written by NAME is forwarded to DEST.",
      "NAME wrote an email in this app that needs to be forwarded to DEST.",
      "NAME's email, will you forward that to DEST?",
      "Open NAME's email and forward it to DEST",
      "Open NAME's latest email and forward it to DEST.",
      "Open email, forward message from NAME to DEST",
      "Open primary email folder, forward NAME's message to DEST.",
      "Open the e-mail from NAME and forward it to DEST.",
      "Please find my email from NAME and forward it to DEST.",
      "Please find that email from NAME and forward to by email to DEST.",
      "Please forward NAME's email to DEST.",
      "Please forward the email NAME sent today to DEST",
      "Please locate NAME's email and put a copy in DEST's inbox.",
      "Please search for the email from NAME and give it to DEST.",
      "Please send DEST NAME's email.",
      "Please send NAME's email to DEST.",
      "Pull up the e-mail from NAME and forward it to DEST.",
      "Pull up the email from NAME and forward it to DEST.",
      "Pull up the email from NAME and let me send it to DEST.",
      "Remind me to forward the email from NAME to DEST.",
      "Resend NAME's e-mail back to DEST",
      "Search for NAME's email and forward to DEST.",
      "Search for an email written by NAME and send that email to DEST.",
      "Search todays email and send the one from NAME, to DEST.",
      "Select email from NAME and send to DEST",
      "Send DEST NAME's email.",
      "Send DEST the email from NAME.",
      "Send DEST the email that NAME sent yesterday.",
      "Send DEST the email written by NAME.",
      "Send DEST the last email from NAME",
      "Send Email in primary from NAME to DEST.",
      "Send NAME the message from DEST.",
      "Send NAME's email DEST.",
      "Send NAME's email to DEST.",
      "Send email from NAME to DEST.",
      "Send my email from NAME to DEST.",
      "Send the email from NAME to DEST.",
      "Send to DEST NAME's email.",
      "Send to DEST an email found in the app that is written by NAME.",
      "Send to DEST the email from NAME.",
      "Send to DEST the email in this app that is written by NAME.",
      "Take NAME's email and give it to DEST.",
      "The email that is found in the email app and written by NAME needs to be forwarded to DEST.",
      "Will you find the email from NAME and forward it to DEST?",
      "find the email from NAME and forward it to DEST",
      "forward NAME's email to DEST",
      "forward NAME's last email to DEST",
      "forward any email from NAME to DEST",
      "forward the email from NAME to DEST.",
      "please forward NAMEs last email to DEST",
      "pull up NAME's email and send to to DEST",
      "pull up NAME's last email and send it to DEST",
      "put DEST on the list of people to send NAME's email to",
      "send DEST the email from NAME",
      "send NAME's email to DEST",
      "send NAME's latest email to DEST"
    ],
    "test":[
      "DEST needs the email in the inbox app written by NAME forwarded to him.",
      "DEST needs to receive the email that NAME sent.",
      "Email DEST the forward from NAME in Primary inbox.",
      "Find NAME's email and forward it to DEST.",
      "Find email from NAME and send to DEST.",
      "Find emails from NAME in primary inbox and forward them to DEST.",
      "Find the last email from NAME and forward it to DEST.",
      "Forward all mail from NAME to DEST.",
      "Forward email by NAME and forward to DEST.",
      "Forward the last email from NAME to DEST",
      "Forward to DEST an email in this application from NAME.",
      "Forward to DEST email from NAME.",
      "Highlight NAME's email and forward to DEST.",
      "I want to find the email from NAME and then send to DEST.",
      "I want to forward DEST email from NAME.",
      "NAME's email should be forwarded to DEST.",
      "NAME's email that is in this app needs to be forwarded to DEST.",
      "Please find the email from NAME and forward it to DEST.",
      "Please forward to DEST the e-mail that NAME sent me.",
      "Please take the e-mail that NAME sent me and forward it to DEST.",
      "Search through the emaiol app for an email written by NAME and send that email to DEST.",
      "Send the email NAME sent to me to DEST.",
      "Send the email by NAME in the inbox app to DEST.",
      "Send the latest email from NAME to DEST by way of forward."
    ]
  },
  "reply":{
    "train":[
      "\"MSG\" is my reply to NAME's most recent email.",
      "Answer NAME's email with \"MSG\"",
      "Answer the new email from NAME with \"MSG\"",
      "AnswerNAME's email with \"MSG\"",
      "Can you email NAME and tell her \"MSG\"",
      "Can you please respond in my primary email box to NAME and write \"MSG\"",
      "Create a reply email to NAME saying \"MSG\".",
      "Email \"MSG\" to NAME.",
      "Email NAME back to tell her \"MSG\"",
      "Email NAME the message, \"MSG\"",
      "Email NAME, \"MSG\"",
      "Find NAME's e-mail and reply with the words \"MSG\"",
      "Find NAME's email and reply \"MSG\".",
      "Find NAME's email and reply saying \"MSG\"",
      "Find NAME's email and respond with \"MSG\"",
      "Find email from NAME and reply with \"MSG\".",
      "Find email from NAME, then reply to NAME \"MSG\"",
      "Find the NAME email and reply, \"MSG\"",
      "Find the NAME email and tell her, \"MSG\"",
      "Find the email from NAME in this email app and respond by saying \"MSG\".",
      "First, find the most recent email from NAME; then, send him a message saying \"MSG\"",
      "Go ahead and reply to NAME with the words \"MSG\"",
      "Hi NAME, I'll \"MSG\".",
      "I need to reply to NAME's email with the message \"MSG\"",
      "I need to respond to NAME's last email sent to me and tell her, \"MSG\".",
      "I need to see NAME's email, please reply \"MSG\"",
      "I need to send an email to NAME letting her know I will \"MSG\".",
      "I want to reply to NAME by saying \"MSG\"",
      "I want you to reply to NAME and send \"MSG\"",
      "I would like to create the message \"MSG\" and use it to reply to NAME's email.",
      "I'll \"MSG\", NAME.",
      "I'm almost done here, NAME. I'll \"MSG\".",
      "In my inbox, there should be an email from NAME, respond and tell her \"MSG\".",
      "In my inbox, there should be an email from NAME, respond and tell him \"MSG\".",
      "Let NAME know I think it \"MSG\"",
      "Locate NAME's email from today and send back \"MSG\".",
      "NAME's email needs to be responded to with \"MSG\".",
      "Open email and go to my inbox. Find the last email from NAME. please reply back, \"MSG\".",
      "Open the email by NAME and reply \"MSG\"",
      "Please find NAME's last email and send him the answer, \"MSG\".",
      "Please find NAME's recent e-mail to me and respond to her with \"MSG\"",
      "Please open my inbox and search for the email that NAME sent me. Please write her back, \"MSG\".",
      "Please reply \"MSG\" to the most recent email from NAME.",
      "Please reply to NAME by saying \"MSG\".",
      "Please reply to NAME's email, \"MSG\"",
      "Please reply to the email from NAME with \"MSG\".",
      "Please send NAME a reply of \"MSG\".",
      "Pull up NAME's e-mail to me and reply with \"MSG\".",
      "Replay to email from NAME with text, \"MSG\".",
      "Reply \"MSG\" to the email from NAME",
      "Reply to NAME with a text, \"MSG\".",
      "Reply to NAME's email \"MSG\"",
      "Reply to NAME's email \"MSG\",",
      "Reply to NAME's email with \"MSG\"",
      "Reply to NAME's email with \"MSG\".",
      "Reply to NAME's email with the text, \"MSG\".",
      "Reply to NAME's email with, \"MSG\".",
      "Reply to NAME's email, \"MSG\".",
      "Reply to NAME's email: \"MSG\"",
      "Reply to NAME's most recent email \"MSG\"",
      "Reply to the email from NAME with, \"MSG\".",
      "Reply with the message \"MSG\" to NAME's email.",
      "Respond \"MSG\" to the email in the email app sent by NAME.",
      "Respond to NAME's email with the text, \"MSG\".",
      "Respond to NAME's most recent email with \"MSG\"",
      "Respond with \"MSG\" to NAME's email from Tuesday.",
      "Say \"MSG\" as a reply to the email from NAME.",
      "Say to NAME \"MSG\"",
      "Send \"MSG\" to NAME.",
      "Send NAME a text, \"MSG\"",
      "Send NAME an email saying, \"MSG\"",
      "Send NAME email saying \"MSG\"",
      "Send a reply saying \"MSG\" to NAME's email.",
      "Send a reply to NAME saying \"MSG\".",
      "Send a reply to NAME's email with the words, \"MSG\".",
      "Send a text to NAME, \"MSG\".",
      "Send an email in response to the email from NAME by sayhing \"MSG\".",
      "Send email to NAME saying, \"MSG\"",
      "Send, \"MSG\" as a response to NAME's email.",
      "Send, \"MSG\", to NAME.",
      "Simply tell NAME \"MSG\"",
      "Siri please reply \"MSG\" to NAME's message.",
      "Siri, Email NAME, \"MSG\".",
      "Take NAME's email and reply with \"MSG\".",
      "Tell NAME \"MSG\"",
      "Tell NAME \"MSG\" as a response to her email in this email app.",
      "Tell NAME \"MSG\" in an email.",
      "Tell NAME I said \"MSG\"",
      "Tell NAME i said \"MSG\"",
      "Tell NAME it \"MSG\" in response to his email.",
      "Tell NAME that \"MSG\"",
      "Text \"MSG\" to NAME",
      "Text NAME \"MSG\"",
      "Text NAME, \"MSG\"",
      "Thanks NAME, I'll \"MSG\".",
      "To NAME respond \"MSG\"",
      "Will you respond to NAME's email and tell her that it \"MSG\"?",
      "Will you respond to NAME's email and tell him that it \"MSG\"?",
      "email NAME and reply with \"MSG\"",
      "find NAME mail and reply with a \"MSG\"",
      "please send a reply email to NAME with \"\"MSG\" \"",
      "pull up NAME email and responds with \"MSG\"",
      "pull up NAME's last email and write back \"MSG\"",
      "reply \"MSG\" to NAME",
      "reply to NAME mail with \"MSG\"",
      "reply to NAME saying \"MSG\"",
      "reply to NAME's last email \"MSG\"",
      "reply to NAME, \"MSG\"",
      "respond to NAME with \"MSG\"",
      "search contact NAME and text him \"MSG\"",
      "send \"MSG\" to NAME",
      "send \"MSG\" to contact NAME",
      "tell NAME \"MSG\"",
      "write to NAME \"MSG\"",
      "write to NAME replying with \"MSG\""
    ],
    "test":[
      "Alexa please email NAME \"MSG\" in reply to his last message.",
      "Email NAME and say \"MSG\".",
      "Find NAME's email and reply with \"MSG\".",
      "Find NAME's email and say \"MSG\"",
      "Find NAME's email and text him \"MSG\"",
      "Find NAME's email, and reply by text with \"MSG\".",
      "Find the email from NAME and simply reply \"MSG\"",
      "For the email from NAME, answer it \"MSG\"",
      "Go to email and find the last email NAME sent to me. Respond back that I will \"MSG\".",
      "NAME, I;ll \"MSG\", Just have to finish up here.",
      "Open an email for NAME and say \"MSG\" and send",
      "Please reply to my NAME's most recent email with \"MSG\"",
      "Respond to NAME's email by texting \"MSG\".",
      "Respond to NAME's email with, \"MSG\".",
      "Search NAME in my contacts and send her a text saying \"MSG\"",
      "Search for the last email from NAME and respond with \"MSG\"",
      "Send \"MSG\" in an email to NAME.",
      "Send \"MSG\" to NAME's most recent email",
      "Tell NAME I'll \"MSG\" in response to her email.",
      "find email from NAME and respond with \"MSG\"",
      "please reply to NAME's last email with \"MSG\""
    ]
  },
  "important":{
    "train":[
      "Can you find the email from NAME and label it as important?",
      "Can you mark the email from NAME as important, please?",
      "Find NAME's email and flag it for importance.",
      "Find NAME's email and make important.",
      "Find NAME's email and mark it as important.",
      "Find NAME's email and mark it important",
      "Find NAME's email and mark it was important.",
      "Find an email by NAME and mark it as important.",
      "Find email from NAME and mark as important.",
      "Find email from NAME in primary and mark as important.",
      "Find email in primary inbox from NAME and mark as important.",
      "Find me the email from NAME that is important.",
      "Find the email from NAME and mark it important",
      "Find the email from NAME and mark it important.",
      "Find the email from NAME so that you can mark it as important.",
      "Find the email from NAME that is marked as important.",
      "Find the important email from NAME",
      "Flag email from NAME as important.",
      "I need the email from NAME to be found and marked important.",
      "I need to find the email from NAME and classify it as important",
      "I need you to Mark all the emails from or by NAME and mark them as important.",
      "I want to make the email I received from NAME an important message.",
      "I want to set the message from NAME as an important email.",
      "I want to star NAME's email as important.",
      "I would like to see the important email NAME sent me.",
      "I'm looking for an email from NAME and I need it to be marked important",
      "Important mark on emails by NAME please.",
      "In my inbox, find the email from NAME and mark it as important please.",
      "In my inbox, find the email from NAME and mark it as please.",
      "In the email app, the email from NAME in the inbox should be marked important.",
      "Just mark all the emails by NAME as important.",
      "Locate NAME's email and mark as important.",
      "Locate NAME's email and mark it important.",
      "Locate NAME's email from today and mark it important.",
      "Look for email sent by NAME and mark it as important.",
      "Make NAME's latest email important",
      "Mark NAME's email as imporant.",
      "Mark NAME's email as important in the email app.",
      "Mark NAME's email as important.",
      "Mark NAME's email important.",
      "Mark all emails written by NAME as important.",
      "Mark all of the important emails from NAME as unread.",
      "Mark an email from NAME as important",
      "Mark email from NAME as important.",
      "Mark emails from NAME as important.",
      "Mark the email from NAME as important.",
      "Mark the email from NAME in this email app as important.",
      "Mark the email that I sent to NAME this morning as important.",
      "Mark the last email NAME sent as imiportant.",
      "Mark the unread email from NAME as important.",
      "NAME's appontment needs to be marked important.",
      "NAME's email needs to be found in the email app and marked important.",
      "NAME's email needs to be marked important.",
      "NAME's email needs to be set as an important message.",
      "NAME's email should be marked as important.",
      "Please find the email NAME sent me and file it as important",
      "Please find the email from NAME and mark it as important.",
      "Please flag NAME's email as important.",
      "Please hunt for NAME's important email.",
      "Please mark all emails by NAME as important.",
      "Please mark email from NAME as important.",
      "Please mark the email by NAME as important.",
      "Please mark the email from NAME as important.",
      "Pull up NAME's email and mark it important.",
      "Pull up the email by NAME and mark it important.",
      "Save NAME's email as important.",
      "Search for an email from NAME and tag it as important",
      "Search for an email from NAME in this email app and mark it as important.",
      "Set the email from NAME as an important email.",
      "Star the email from NAME.",
      "Tag NAME's email as important.",
      "The email from NAME in the inbox of the app needs to be marked important.",
      "The email from NAME needs to marked as important.",
      "You can find NAME's email in the email inbox app and mark it important.",
      "You must find NAME's email and mark it important.",
      "change NAME's email to being important",
      "change the email from NAME to important",
      "find NAMEs email and mark it as important",
      "find the email from NAME and mark it important",
      "in my email there is a message from NAME that needs to be marked as importnat",
      "in my inbox mark the email from NAME as important",
      "mark NAME's email as important",
      "mark NAME's last email as important",
      "mark all emails in primary with NAME",
      "mark any of NAME's incoming emails as important",
      "mark the email from NAME as important",
      "mark the email from NAME with a star",
      "please put important on NAME's email",
      "search for NAME in my primary and mark important",
      "show NAME's email as important",
      "the email from NAME is important please mark it that way"
    ],
    "test":[
      "Find the email NAME sent yesterday and mark it as important.",
      "Find the email by NAME and mark it as important.",
      "Flag NAME's email as important.",
      "Make NAME's email important.",
      "Make NAME's emails marked important.",
      "Make the email from NAME an important message.",
      "Mark emails in Primary from NAME as important.",
      "Mark the email I sent to NAME this morning as important.",
      "Mark the emails from NAME as important.",
      "Search and find NAME's important email.",
      "The email from NAME needs to be marked important.",
      "There is an email from NAME in my inbox, will you mark it as important?",
      "find NAME's email and mark it as important",
      "important email in primary inbox are from NAME.",
      "mark the last email from NAME important",
      "put a star on NAME's email",
      "star NAME's email"
    ]
  },
  "delete":{
    "train":[
      "Can you go to my email account and delete emails from NAME?",
      "Can you please list emails from NAME and then delete them?",
      "Delete NAME's email.",
      "Delete NAME's message.",
      "Delete all emails by NAME.",
      "Delete all emails that I have sent to NAME",
      "Delete all messages from NAME.",
      "Delete all read messages from NAME.",
      "Delete message NAME from email inbox.",
      "Delete the email I received from NAME.",
      "Delete the email NAME sent me.",
      "Delete the email from NAME",
      "Delete the email from NAME, please.",
      "Delete the email from NAME.",
      "Delete the email that NAME sent yesterday.",
      "Delete the emails from NAME that are not marked as important.",
      "Delete the message from NAME.",
      "Find NAME's email and delete it",
      "Find NAME's email and delete it.",
      "Find NAME's email to delete it.",
      "Find NAME's email to me and delete it.",
      "Find and remove the email from NAME.",
      "Find email from NAME and delete it.",
      "Find my email from NAME and delete it.",
      "Find the email from NAME and delete it.",
      "Find the email from NAME in my inbox and delete it.",
      "Find the new email from NAME and delete it.",
      "Find today's email from NAME and get rid of it.",
      "Get rid of NAME's email.",
      "Get rid of my message from NAME.",
      "Get rid of the email I got from NAME.",
      "Get rid of the email from NAME.",
      "I don't want NAME's email in my inbox.",
      "I don't want the email from NAME anymore.",
      "In my inbox, there is an email from NAME and it needs to be deleted.",
      "Jend the email from NAME to the trash.",
      "Locate NAME's email and delete it.",
      "Locate NAME's email from today and delete it.",
      "Locate NAME's recent email and delete it.",
      "Locate and delete the email from NAME.",
      "Locate the email from NAME and delete it.",
      "NAME's email should be deleted from the inbox.",
      "Please delete NAME's email",
      "Please delete NAME's emails.",
      "Please find NAME's email in the inbox and delete it.",
      "Please find the email from NAME and delete it.",
      "Please get rid of the email from NAME.",
      "Please pull up email and then delete NAME's email to me?",
      "Put the email from NAME in the trash.",
      "Search for NAME's email and delete it",
      "Search for email from NAME and delete it.",
      "Search for the email from NAME now and delete it",
      "Search for the last email NAME sent and delete it.",
      "Search my inbox for emails from NAME and delete all of them.",
      "Select NAME's email and delete.",
      "Take NAME's email out of my inbox.",
      "There is an email that needs to be deleted by NAME.",
      "Throw away NAME's email.",
      "Trash that email from NAME.",
      "WiIl you please find the email from NAME and delete it?",
      "delete the last email from NAME",
      "delete the message from NAME in my email",
      "delete the most recent email from NAME",
      "delete this email from NAME",
      "find NAME's email and delete it",
      "find NAME's last email and delete it",
      "find the email from NAME in my inbox and delete it",
      "get rid of this email from NAME",
      "open email and delete the message from NAME",
      "please delete the email from NAME in my inbox",
      "pull up NAME's last email and delete it",
      "put this email from NAME in the recycling bin",
      "there is a message from NAME in my email that needs to be deleted"
    ],
    "test":[
      "Can you please find and then delete the email from NAME?",
      "Delete NAME's email from today.",
      "Delete email from NAME.",
      "Delete email message from NAME.",
      "Find NAME's email and delete.",
      "Find and delete the email from NAME.",
      "I want you to delete NAME's email.",
      "If NAME sent me an email, delete it.",
      "May you delete all emails from NAME?",
      "Please delete the email from NAME",
      "Please delete the email from NAME.",
      "Search for and find the email from NAME and delete it.",
      "Select the email with the author NAME and sent it to trash.",
      "The email from NAME needs to be deleted."
    ]
  }
};
