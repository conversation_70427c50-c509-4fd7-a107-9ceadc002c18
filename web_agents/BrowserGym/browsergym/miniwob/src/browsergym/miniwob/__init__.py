import os

from browsergym.core.registration import register_task

from . import all


def environment_variables_precheck():
    assert os.environ.get(
        "MINIWOB_URL", None
    ), "Environment variable MINIWOB_URL has not been setup."


ALL_MINIWOB_TASKS = [
    all.AscendingNumbersTask,
    all.BisectAngleTask,
    all.BookFlightTask,
    all.BookFlightNodelayTask,
    all.BuyTicketTask,
    all.ChooseDateTask,
    all.ChooseDateEasyTask,
    all.ChooseDateMediumTask,
    all.ChooseDateNodelayTask,
    all.ChooseListTask,
    all.CircleCenterTask,
    all.ClickButtonTask,
    all.ClickButtonSequenceTask,
    all.ClickCheckboxesTask,
    all.ClickCheckboxesLargeTask,
    all.ClickCheckboxesSoftTask,
    all.ClickCheckboxesTransferTask,
    all.ClickCollapsibleTask,
    all.ClickCollapsible2Task,
    all.<PERSON><PERSON><PERSON><PERSON>apsible2NodelayTask,
    all.Click<PERSON>ollapsibleNodelayTask,
    all.ClickColorTask,
    all.ClickDialogTask,
    all.ClickDialog2Task,
    all.ClickLinkTask,
    all.ClickMenuTask,
    all.ClickMenu2Task,
    all.ClickOptionTask,
    all.ClickPieTask,
    all.ClickPieNodelayTask,
    all.ClickScrollListTask,
    all.ClickShadesTask,
    all.ClickShapeTask,
    all.ClickTabTask,
    all.ClickTab2Task,
    all.ClickTab2EasyTask,
    all.ClickTab2HardTask,
    all.ClickTab2MediumTask,
    all.ClickTestTask,
    all.ClickTest2Task,
    all.ClickTestTransferTask,
    all.ClickWidgetTask,
    all.CopyPasteTask,
    all.CopyPaste2Task,
    all.CountShapeTask,
    all.CountSidesTask,
    all.DailyCalendarTask,
    all.DragBoxTask,
    all.DragCircleTask,
    all.DragCubeTask,
    all.DragItemsTask,
    all.DragItemsGridTask,
    all.DragShapesTask,
    all.DragShapes2Task,
    all.DragSingleShapeTask,
    all.DragSortNumbersTask,
    all.DrawCircleTask,
    all.DrawLineTask,
    all.EmailInboxTask,
    all.EmailInboxDeleteTask,
    all.EmailInboxForwardTask,
    all.EmailInboxForwardNlTask,
    all.EmailInboxForwardNlTurkTask,
    all.EmailInboxImportantTask,
    all.EmailInboxNlTurkTask,
    all.EmailInboxNoscrollTask,
    all.EmailInboxReplyTask,
    all.EmailInboxStarReplyTask,
    all.EnterDateTask,
    all.EnterPasswordTask,
    all.EnterTextTask,
    all.EnterText2Task,
    all.EnterTextDynamicTask,
    all.EnterTimeTask,
    all.FindGreatestTask,
    all.FindMidpointTask,
    all.FindWordTask,
    all.FocusTextTask,
    all.FocusText2Task,
    all.FormSequenceTask,
    all.FormSequence2Task,
    all.FormSequence3Task,
    all.GenerateNumberTask,
    all.GridCoordinateTask,
    all.GuessNumberTask,
    all.HighlightTextTask,
    all.HighlightText2Task,
    all.HotColdTask,
    all.IdentifyShapeTask,
    all.LoginUserTask,
    all.LoginUserPopupTask,
    all.MultiLayoutsTask,
    all.MultiOrderingsTask,
    all.NavigateTreeTask,
    all.NumberCheckboxesTask,
    all.OddOrEvenTask,
    all.OrderFoodTask,
    all.PhoneBookTask,
    all.ReadTableTask,
    all.ReadTable2Task,
    all.ResizeTextareaTask,
    all.RightAngleTask,
    all.ScrollTextTask,
    all.ScrollText2Task,
    all.SearchEngineTask,
    all.SignAgreementTask,
    all.SimpleAlgebraTask,
    all.SimpleArithmeticTask,
    all.SocialMediaTask,
    all.SocialMediaAllTask,
    all.SocialMediaSomeTask,
    all.StockMarketTask,
    all.TerminalTask,
    all.TextEditorTask,
    all.TextTransformTask,
    all.TicTacToeTask,
    all.UnicodeTestTask,
    all.UseAutocompleteTask,
    all.UseAutocompleteNodelayTask,
    all.UseColorwheelTask,
    all.UseColorwheel2Task,
    all.UseSliderTask,
    all.UseSlider2Task,
    all.UseSpinnerTask,
    all.VisualAdditionTask,
]

# register the Miniwob benchmark
for task in ALL_MINIWOB_TASKS:
    register_task(
        task.get_task_id(),
        task,
        nondeterministic=task.nondeterministic,
    )
