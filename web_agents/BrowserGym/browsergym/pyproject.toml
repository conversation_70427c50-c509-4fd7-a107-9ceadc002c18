[build-system]
build-backend = "setuptools.build_meta"
requires = ["setuptools"]

[project]
name = "browsergym"
description = "BrowserGym: a gym environment for web task automation in the Chromium browser"
authors = [
    {name = "<PERSON><PERSON>"},
    {name = "<PERSON><PERSON><PERSON>"},
    {name = "<PERSON><PERSON>"},
    {name = "<PERSON>"},
    {name = "<PERSON><PERSON>"},
    {name = "<PERSON>"},
    {name = "<PERSON>"},
]
readme = "README.md"
requires-python = ">3.7"
license = {text = "Apache-2.0"}
classifiers = [
    "Development Status :: 2 - Pre-Alpha",
    "Programming Language :: Python :: 3",
    "Operating System :: OS Independent",
    "Intended Audience :: Science/Research",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "License :: OSI Approved :: Apache Software License",
]
version="0.10.2"
dependencies = [
    "browsergym-core==0.10.2",
    "browsergym-miniwob==0.10.2",
    "browsergym-webarena==0.10.2",
    "browsergym-visualwebarena==0.10.2",
    "browsergym-assistantbench==0.10.2",
    "browsergym-experiments==0.10.2",
    "browsergym-workarena>=0.4.1",
]

[tool.setuptools]
packages = []  # meta distribution, packages are included as dependencies
