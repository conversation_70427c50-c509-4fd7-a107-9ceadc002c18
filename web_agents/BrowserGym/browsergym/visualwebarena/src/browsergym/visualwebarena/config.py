TASK_IDS = range(910)

# import visualwebarena
# import importlib.resources
# import json
# all_configs_str = importlib.resources.files(visualwebarena).joinpath("test_raw.json").read_text()
# all_configs = json.loads(all_configs_str)
# task_ids_with_reset = [task["task_id"] for task in all_configs if task["require_reset"] == True]
TASK_IDS_WITH_RESET = [
    4,
    5,
    8,
    9,
    28,
    29,
    30,
    31,
    57,
    76,
    77,
    143,
    144,
    145,
    159,
    160,
    203,
    205,
    208,
    213,
    217,
    223,
    392,
    393,
    394,
    402,
    404,
    405,
    406,
    407,
    408,
    410,
    411,
    412,
    416,
    422,
    423,
    424,
    425,
    426,
    441,
    442,
    443,
    668,
    669,
    670,
    671,
    672,
    673,
    688,
    689,
    711,
    712,
    713,
    714,
    715,
    716,
    717,
    733,
    764,
    765,
    766,
]
