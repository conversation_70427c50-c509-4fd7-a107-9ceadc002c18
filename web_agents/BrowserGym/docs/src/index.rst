Welcome to BrowserGym's documentation!
======================================

**BrowserGym** is a Python library that provides a `gym environment <https://gymnasium.farama.org/>`_
for web task automation in the Chromium browser. It includes the following benchmarks by default:
`MiniWob++ <https://miniwob.farama.org/>`_, `WebArena <https://webarena.dev/>`_, `WorkArena <https://github.com/ServiceNow/WorkArena>`_.

.. note::

   This project is under active development.

Contents
--------

.. toctree::
   :maxdepth: 2

   usage
   api
   tutorials
