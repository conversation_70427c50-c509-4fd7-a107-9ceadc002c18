name: Build and Publish
# based on official doc
# https://packaging.python.org/en/latest/guides/publishing-package-distribution-releases-using-github-actions-ci-cd-workflows/

on: [push, workflow_dispatch]

jobs:
    build:
      name: Build
      runs-on: ubuntu-22.04

      steps:
      - uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.x"

      - name: Install pypa/build
        run: python3 -m pip install build --user

      - name: Build a binary wheel and a source tarball (browsergym-core)
        run: python3 -m build browsergym/core/ --outdir dist/

      - name: Build a binary wheel and a source tarball (browsergym-miniwob)
        run: python3 -m build browsergym/miniwob/ --outdir dist/

      - name: Build a binary wheel and a source tarball (browsergym-webarena)
        run: python3 -m build browsergym/webarena/ --outdir dist/

      - name: Build a binary wheel and a source tarball (browsergym-webarena)
        run: python3 -m build browsergym/visualwebarena/ --outdir dist/

      - name: Build a binary wheel and a source tarball (browsergym-assistantbench)
        run: python3 -m build browsergym/assistantbench/ --outdir dist/

      - name: Build a binary wheel and a source tarball (browsergym-experiments)
        run: python3 -m build browsergym/experiments/ --outdir dist/

      - name: Build a binary wheel and a source tarball (browsergym)
        run: python3 -m build browsergym/ --outdir dist/

      - name: Store the distribution packages
        uses: actions/upload-artifact@v4
        with:
          name: python-package-distributions
          path: dist/

    publish-to-pypi:
      name: Publish to PyPI
      if: startsWith(github.ref, 'refs/tags/')  # only publish to PyPI on tag pushes
      needs:
        - build
      runs-on: ubuntu-22.04
      environment: pypi
      permissions:
        id-token: write  # IMPORTANT: mandatory for trusted publishing

      steps:
        - name: Download all the distribution packages
          uses: actions/download-artifact@v4
          with:
            name: python-package-distributions
            path: dist/

        - name: Publish all distribution packages to PyPI
          uses: pypa/gh-action-pypi-publish@release/v1

    github-release:
      name: Sign packages with Sigstore and upload them to GitHub Release
      needs:
      - publish-to-pypi
      runs-on: ubuntu-22.04

      permissions:
        contents: write  # IMPORTANT: mandatory for making GitHub Releases
        id-token: write  # IMPORTANT: mandatory for sigstore

      steps:
      - name: Download all the dists
        uses: actions/download-artifact@v4
        with:
          name: python-package-distributions
          path: dist/

      - name: Sign the dists with Sigstore
        uses: sigstore/gh-action-sigstore-python@v2.1.1
        with:
          inputs: >-
            ./dist/*.tar.gz
            ./dist/*.whl

      - name: Create GitHub Release
        env:
          GITHUB_TOKEN: ${{ github.token }}
        run: >-
          gh release create
          '${{ github.ref_name }}'
          --repo '${{ github.repository }}'
          --notes ""

      - name: Upload artifact signatures to GitHub Release
        env:
          GITHUB_TOKEN: ${{ github.token }}
        # Upload to GitHub Release using the `gh` CLI.
        # `dist/` contains the built packages, and the
        # sigstore-produced signatures and certificates.
        run: >-
          gh release upload
          '${{ github.ref_name }}' dist/**
          --repo '${{ github.repository }}'

      - name: Set GitHub Release as pre-release
        if: contains(github.ref, '.dev')  # only set tags vA.B.C.devD as pre-release
        env:
          GITHUB_TOKEN: ${{ github.token }}
        run: >-
          gh release edit
          '${{ github.ref_name }}'
          --repo '${{ github.repository }}'
          --prerelease

    # publish-to-testpypi:
    #   name: Publish to TestPyPI
    #   needs:
    #   - build
    #   runs-on: ubuntu-latest
    #   environment: testpypi
    #   permissions:
    #     id-token: write  # IMPORTANT: mandatory for trusted publishing

    #   steps:
    #   - name: Download all the distribution packages
    #     uses: actions/download-artifact@v4
    #     with:
    #       name: python-package-distributions
    #       path: dist/

    #   - name: Publish distribution packages to TestPyPI
    #     uses: pypa/gh-action-pypi-publish@release/v1
    #     with:
    #       repository-url: https://test.pypi.org/legacy/
