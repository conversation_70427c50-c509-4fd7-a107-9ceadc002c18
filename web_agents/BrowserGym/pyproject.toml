[tool.black]
line-length = 100
include = '\.pyi?$'
exclude = '''
/(
    \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.nox
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

[tool.pytest.ini_options]
filterwarnings = [
    'ignore::UserWarning:gymnasium.*:',  # too many "The obs is not within the observation space." warnings.
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "serial: mark test to be run sequantially (deselect with '-m \"not serial\"')"
]
