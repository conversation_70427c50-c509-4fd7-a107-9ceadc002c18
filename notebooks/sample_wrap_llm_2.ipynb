{"cells": [{"cell_type": "code", "execution_count": 1, "id": "be1641a2-a169-46b7-b64e-cf47ff214729", "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ['XLA_PYTHON_CLIENT_PREALLOCATE'] = 'false'"]}, {"cell_type": "code", "execution_count": 2, "id": "8b085c11", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[=== Module openmpi/4.0.4 loaded ===]\n", "[=== Module cudatoolkit/12.1.1 loaded ===]\n"]}], "source": ["!module load openmpi\n", "!module load cuda/12.1.1"]}, {"cell_type": "code", "execution_count": 2, "id": "cae51990-9238-4c13-9973-f324b43a1287", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/network/scratch/a/artem.zholus/conda_envs/rl_jax_torch/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "CUDA backend failed to initialize: Found CUDA version 12010, but JAX was built against version 12030, which is newer. The copy of CUDA that is installed must be at least as new as the version against which JAX was built. (Set TF_CPP_MIN_LOG_LEVEL=0 and rerun for more info.)\n", "WARNING:absl:Tensorflow library not found, tensorflow.io.gfile operations will use native shim calls. GCS paths (i.e. 'gs://...') cannot be accessed.\n"]}], "source": ["import numpy as np\n", "import math\n", "from tqdm import tqdm\n", "import jax\n", "from jax import numpy as jnp\n", "import torch\n", "from transformers import AutoTokenizer, BertForMaskedLM, BertForPreTraining\n", "\n", "from xminigrid.benchmarks import load_bz2_pickle\n", "import xminigrid\n", "from train_meta_task import my_load_benchmark\n", "from rich import print"]}, {"cell_type": "code", "execution_count": 17, "id": "7b9d8897-d29d-487e-8491-b3d1cb823291", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Loading checkpoint shards: 100%|███████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.13s/it]\n"]}], "source": ["from langchain_community.llms.huggingface_pipeline import HuggingFacePipeline\n", "\n", "gpu_llm = HuggingFacePipeline.from_model_id(\n", "    model_id=\"google/gemma-2b-it\",\n", "    task=\"text-generation\",\n", "    device=0,  # replace with device_map=\"auto\" to use the accelerate library.\n", "    pipeline_kwargs={\"max_new_tokens\": 100},\n", "    model_kwargs={'cache_dir': os.environ['SCRATCH'] + '/hf'},\n", ")\n", "from langchain_core.prompts import PromptTemplate\n", "\n", "template = \"\"\"Question: {question}\"\"\"\n", "prompt = PromptTemplate.from_template(template)\n", "\n", "chain = prompt | gpu_llm"]}, {"cell_type": "code", "execution_count": 18, "id": "56098f3f-b2b1-4794-8ca7-e89b091e0595", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Question: What is electroencephalography?\n", "</pre>\n"], "text/plain": ["Question: What is electroencephalography?\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["question = \"What is electroencephalography?\"\n", "\n", "print(chain.invoke({\"question\": question}))"]}, {"cell_type": "code", "execution_count": 3, "id": "16ed5f4e-14f5-4239-8a2c-d0e014aa2ebc", "metadata": {}, "outputs": [{"ename": "RuntimeError", "evalue": "Failed to import transformers.models.gemma.modeling_gemma because of the following error (look up to see its traceback):\n/network/scratch/a/artem.zholus/conda_envs/rl_jax_torch/lib/python3.10/site-packages/flash_attn_2_cuda.cpython-310-x86_64-linux-gnu.so: undefined symbol: _ZN2at4_ops15sum_IntList_out4callERKNS_6TensorEN3c1016OptionalArrayRefIlEEbSt8optionalINS5_10ScalarTypeEERS2_", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mImportError\u001b[0m                               Traceback (most recent call last)", "File \u001b[0;32m/network/scratch/a/artem.zholus/conda_envs/rl_jax_torch/lib/python3.10/site-packages/transformers/utils/import_utils.py:1472\u001b[0m, in \u001b[0;36m_LazyModule._get_module\u001b[0;34m(self, module_name)\u001b[0m\n\u001b[1;32m   1471\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1472\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mimportlib\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mimport_module\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m.\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m+\u001b[39;49m\u001b[43m \u001b[49m\u001b[43mmodule_name\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;18;43m__name__\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1473\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n", "File \u001b[0;32m/network/scratch/a/artem.zholus/conda_envs/rl_jax_torch/lib/python3.10/importlib/__init__.py:126\u001b[0m, in \u001b[0;36mimport_module\u001b[0;34m(name, package)\u001b[0m\n\u001b[1;32m    125\u001b[0m         level \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m\n\u001b[0;32m--> 126\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_bootstrap\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_gcd_import\u001b[49m\u001b[43m(\u001b[49m\u001b[43mname\u001b[49m\u001b[43m[\u001b[49m\u001b[43mlevel\u001b[49m\u001b[43m:\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpackage\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlevel\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m<frozen importlib._bootstrap>:1050\u001b[0m, in \u001b[0;36m_gcd_import\u001b[0;34m(name, package, level)\u001b[0m\n", "File \u001b[0;32m<frozen importlib._bootstrap>:1027\u001b[0m, in \u001b[0;36m_find_and_load\u001b[0;34m(name, import_)\u001b[0m\n", "File \u001b[0;32m<frozen importlib._bootstrap>:1006\u001b[0m, in \u001b[0;36m_find_and_load_unlocked\u001b[0;34m(name, import_)\u001b[0m\n", "File \u001b[0;32m<frozen importlib._bootstrap>:688\u001b[0m, in \u001b[0;36m_load_unlocked\u001b[0;34m(spec)\u001b[0m\n", "File \u001b[0;32m<frozen importlib._bootstrap_external>:883\u001b[0m, in \u001b[0;36mexec_module\u001b[0;34m(self, module)\u001b[0m\n", "File \u001b[0;32m<frozen importlib._bootstrap>:241\u001b[0m, in \u001b[0;36m_call_with_frames_removed\u001b[0;34m(f, *args, **kwds)\u001b[0m\n", "File \u001b[0;32m/network/scratch/a/artem.zholus/conda_envs/rl_jax_torch/lib/python3.10/site-packages/transformers/models/gemma/modeling_gemma.py:50\u001b[0m\n\u001b[1;32m     49\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_flash_attn_2_available():\n\u001b[0;32m---> 50\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mflash_attn\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m flash_attn_func, flash_attn_varlen_func\n\u001b[1;32m     51\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mflash_attn\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbert_padding\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m index_first_axis, pad_input, unpad_input  \u001b[38;5;66;03m# noqa\u001b[39;00m\n", "File \u001b[0;32m/network/scratch/a/artem.zholus/conda_envs/rl_jax_torch/lib/python3.10/site-packages/flash_attn/__init__.py:3\u001b[0m\n\u001b[1;32m      1\u001b[0m __version__ \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m2.5.6\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m----> 3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mflash_attn\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mflash_attn_interface\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[1;32m      4\u001b[0m     flash_attn_func,\n\u001b[1;32m      5\u001b[0m     flash_attn_kvpacked_func,\n\u001b[1;32m      6\u001b[0m     flash_attn_qkvpacked_func,\n\u001b[1;32m      7\u001b[0m     flash_attn_varlen_func,\n\u001b[1;32m      8\u001b[0m     flash_attn_varlen_kvpacked_func,\n\u001b[1;32m      9\u001b[0m     flash_attn_varlen_qkvpacked_func,\n\u001b[1;32m     10\u001b[0m     flash_attn_with_kvcache,\n\u001b[1;32m     11\u001b[0m )\n", "File \u001b[0;32m/network/scratch/a/artem.zholus/conda_envs/rl_jax_torch/lib/python3.10/site-packages/flash_attn/flash_attn_interface.py:10\u001b[0m\n\u001b[1;32m      8\u001b[0m \u001b[38;5;66;03m# isort: off\u001b[39;00m\n\u001b[1;32m      9\u001b[0m \u001b[38;5;66;03m# We need to import the CUDA kernels after importing torch\u001b[39;00m\n\u001b[0;32m---> 10\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mflash_attn_2_cuda\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mflash_attn_cuda\u001b[39;00m\n\u001b[1;32m     12\u001b[0m \u001b[38;5;66;03m# isort: on\u001b[39;00m\n", "\u001b[0;31mImportError\u001b[0m: /network/scratch/a/artem.zholus/conda_envs/rl_jax_torch/lib/python3.10/site-packages/flash_attn_2_cuda.cpython-310-x86_64-linux-gnu.so: undefined symbol: _ZN2at4_ops15sum_IntList_out4callERKNS_6TensorEN3c1016OptionalArrayRefIlEEbSt8optionalINS5_10ScalarTypeEERS2_", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[0;31mRuntimeError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[3], line 4\u001b[0m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01m<PERSON>ch\u001b[39;00m\n\u001b[1;32m      3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mtransformers\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m AutoModelForCausalLM, AutoTokenizer, pipeline\n\u001b[0;32m----> 4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mtransformers\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m AutoTokenizer, FlaxT5ForConditionalGeneration, T5ForConditionalGeneration, \\\n\u001b[1;32m      5\u001b[0m     FlaxGemmaForCausalLM, GemmaForCausalLM \n\u001b[1;32m      6\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mos\u001b[39;00m\n\u001b[1;32m      7\u001b[0m \u001b[38;5;66;03m# tokenizer = AutoTokenizer.from_pretrained(\"google/gemma-2b-it\", cache_dir=os.environ['SCRATCH'] + '/hf')\u001b[39;00m\n\u001b[1;32m      8\u001b[0m \u001b[38;5;66;03m# model = GemmaForCausalLM.from_pretrained(\u001b[39;00m\n\u001b[1;32m      9\u001b[0m \u001b[38;5;66;03m#     \"google/gemma-2b-it\",attn_implementation=\"flash_attention_2\",\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     12\u001b[0m \u001b[38;5;66;03m# pipe = pipeline(\"text-generation\", device=0, model=model, tokenizer=tokenizer, max_new_tokens=100)\u001b[39;00m\n\u001b[1;32m     13\u001b[0m \u001b[38;5;66;03m# pipe.enable_xformers_memory_efficient_attention()\u001b[39;00m\n", "File \u001b[0;32m<frozen importlib._bootstrap>:1075\u001b[0m, in \u001b[0;36m_handle_fromlist\u001b[0;34m(module, fromlist, import_, recursive)\u001b[0m\n", "File \u001b[0;32m/network/scratch/a/artem.zholus/conda_envs/rl_jax_torch/lib/python3.10/site-packages/transformers/utils/import_utils.py:1463\u001b[0m, in \u001b[0;36m_LazyModule.__getattr__\u001b[0;34m(self, name)\u001b[0m\n\u001b[1;32m   1461\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m name \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_class_to_module\u001b[38;5;241m.\u001b[39mkeys():\n\u001b[1;32m   1462\u001b[0m     module \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_module(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_class_to_module[name])\n\u001b[0;32m-> 1463\u001b[0m     value \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mgetattr\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mmodule\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mname\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1464\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1465\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mAttributeError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodule \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m has no attribute \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mname\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m/network/scratch/a/artem.zholus/conda_envs/rl_jax_torch/lib/python3.10/site-packages/transformers/utils/import_utils.py:1462\u001b[0m, in \u001b[0;36m_LazyModule.__getattr__\u001b[0;34m(self, name)\u001b[0m\n\u001b[1;32m   1460\u001b[0m     value \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_module(name)\n\u001b[1;32m   1461\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m name \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_class_to_module\u001b[38;5;241m.\u001b[39mkeys():\n\u001b[0;32m-> 1462\u001b[0m     module \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_get_module\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_class_to_module\u001b[49m\u001b[43m[\u001b[49m\u001b[43mname\u001b[49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1463\u001b[0m     value \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mgetattr\u001b[39m(module, name)\n\u001b[1;32m   1464\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n", "File \u001b[0;32m/network/scratch/a/artem.zholus/conda_envs/rl_jax_torch/lib/python3.10/site-packages/transformers/utils/import_utils.py:1474\u001b[0m, in \u001b[0;36m_LazyModule._get_module\u001b[0;34m(self, module_name)\u001b[0m\n\u001b[1;32m   1472\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m importlib\u001b[38;5;241m.\u001b[39mimport_module(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m.\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m+\u001b[39m module_name, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m)\n\u001b[1;32m   1473\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m-> 1474\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\n\u001b[1;32m   1475\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFailed to import \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mmodule_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m because of the following error (look up to see its\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   1476\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m traceback):\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;132;01m{\u001b[39;00me\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   1477\u001b[0m     ) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01me\u001b[39;00m\n", "\u001b[0;31mRuntimeError\u001b[0m: Failed to import transformers.models.gemma.modeling_gemma because of the following error (look up to see its traceback):\n/network/scratch/a/artem.zholus/conda_envs/rl_jax_torch/lib/python3.10/site-packages/flash_attn_2_cuda.cpython-310-x86_64-linux-gnu.so: undefined symbol: _ZN2at4_ops15sum_IntList_out4callERKNS_6TensorEN3c1016OptionalArrayRefIlEEbSt8optionalINS5_10ScalarTypeEERS2_"]}], "source": ["from langchain_community.llms.huggingface_pipeline import HuggingFacePipeline\n", "import torch\n", "from transformers import AutoModelForCausalLM, AutoTokenizer, pipeline\n", "from transformers import AutoTokenizer, FlaxT5ForConditionalGeneration, T5ForConditionalGeneration, \\\n", "    FlaxGemmaForCausalLM, GemmaForCausalLM \n", "import os\n", "# tokenizer = AutoTokenizer.from_pretrained(\"google/gemma-2b-it\", cache_dir=os.environ['SCRATCH'] + '/hf')\n", "# model = GemmaForCausalLM.from_pretrained(\n", "#     \"google/gemma-2b-it\",attn_implementation=\"flash_attention_2\",\n", "#     torch_dtype=torch.bfloat16, \n", "#     cache_dir=os.environ['SCRATCH'] + '/hf').cuda()\n", "# pipe = pipeline(\"text-generation\", device=0, model=model, tokenizer=tokenizer, max_new_tokens=100)\n", "# pipe.enable_xformers_memory_efficient_attention()"]}, {"cell_type": "code", "execution_count": 13, "id": "c82ec023-e941-4e94-b5d7-08058d10738b", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'generated_text': 'Write me a poem about Machine Learning.'}]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["pipe(\"Write me a poem about Machine Learning.\", \n", "     do_sample=True, \n", "     # num_beams=4,\n", "     # early_stopping=False,\n", "     # penalty_alpha=0.6,\n", "     eos_token_id=-1,\n", "     top_k=50, \n", "     temperature=1.)"]}, {"cell_type": "code", "execution_count": 4, "id": "7bd8de8a-4237-42ee-b1e7-aedbc10d1263", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["INFO 08-21 11:28:32 llm_engine.py:79] Initializing an LLM engine with config: model='google/gemma-2b-it', tokenizer='google/gemma-2b-it', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=True, dtype=torch.bfloat16, max_seq_len=8192, download_dir=None, load_format=auto, tensor_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, device_config=cuda, seed=0)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/huggingface_hub/file_download.py:1132: FutureWarning: `resume_download` is deprecated and will be removed in version 1.0.0. Downloads always resume when possible. If you want to force a new download, use `force_download=True`.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO 08-21 11:28:37 weight_utils.py:163] Using model weights format ['*.safetensors']\n", "INFO 08-21 11:28:45 llm_engine.py:337] # GPU blocks: 233023, # CPU blocks: 14563\n", "INFO 08-21 11:28:46 model_runner.py:676] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.\n", "INFO 08-21 11:28:46 model_runner.py:680] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.\n", "INFO 08-21 11:28:51 model_runner.py:748] Graph capturing finished in 4 secs.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processed prompts: 100%|██████████| 1/1 [00:00<00:00,  4.06it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "Paris is the capital of France. It is the seat of the French government and is the political, economic, and cultural center of the country.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["from langchain_community.llms import VLLM\n", "\n", "llm = VLLM(\n", "    model=\"google/gemma-2b-it\",\n", "    trust_remote_code=True,  # mandatory for hf models\n", "    max_new_tokens=128,\n", "    top_k=10,\n", "    top_p=0.95,\n", "    temperature=1.2,\n", "    # download_dir=,\n", ")\n", "\n", "print(llm.invoke(\"What is the capital of France ?\"))"]}, {"cell_type": "code", "execution_count": 5, "id": "eb8348ce-bc68-4b1d-979d-a1401d3f9ac4", "metadata": {}, "outputs": [], "source": ["from langchain.chains import LLMChain\n", "from langchain_core.prompts import PromptTemplate\n", "\n", "template = \"\"\"Question: {question}\n", "\n", "Answer: Let's think step by step.\"\"\"\n", "prompt = PromptTemplate.from_template(template)\n", "\n", "# llm_chain = LLMChain(prompt=prompt, llm=llm)\n", "\n", "# question = \"Who was the US president in the year the first Pokemon game was released?\"\n", "\n", "# print(llm_chain.invoke(question))"]}, {"cell_type": "code", "execution_count": 6, "id": "6f2be772-a526-4469-9909-e08f1dd7b9ec", "metadata": {}, "outputs": [], "source": ["from langchain.output_parsers import ResponseSchema, StructuredOutputParser\n", "from langchain.prompts import PromptTemplate\n", "# from langchain_openai import ChatOpenAI\n", "response_schemas = [\n", "    ResponseSchema(name=\"answer\", description=\"answer to the user's question\"),\n", "    ResponseSchema(\n", "        name=\"source\",\n", "        description=\"source used to answer the user's question, should be a website.\",\n", "    ),\n", "]\n", "output_parser = StructuredOutputParser.from_response_schemas(response_schemas)\n", "format_instructions = output_parser.get_format_instructions()\n", "prompt = PromptTemplate(\n", "    template=\"answer the users question as best as possible.\\n{format_instructions}\\n{question}\",\n", "    input_variables=[\"question\"],\n", "    partial_variables={\"format_instructions\": format_instructions},\n", ")"]}, {"cell_type": "code", "execution_count": 7, "id": "c83397a6-b4bd-4c0b-bc4a-824a0536bce9", "metadata": {}, "outputs": [], "source": ["chain = prompt | llm | output_parser"]}, {"cell_type": "code", "execution_count": 8, "id": "49586bdb-089b-4549-9fd8-fa124460a41c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["answer the users question as best as possible.\n", "The output should be a markdown code snippet formatted in the following schema, including the leading and trailing \"```json\" and \"```\":\n", "\n", "```json\n", "{\n", "\t\"answer\": string  // answer to the user's question\n", "\t\"source\": string  // source used to answer the user's question, should be a website.\n", "}\n", "```\n", "what's the capital of france?\n"]}], "source": ["print(prompt.invoke({\"question\": \"what's the capital of france?\"}).text)"]}, {"cell_type": "code", "execution_count": 9, "id": "b89cfc22-6015-4519-90b2-a134bb9e54bb", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Processed prompts:   0%|          | 0/1 [00:00<?, ?it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["Processed prompts: 100%|██████████| 1/1 [00:00<00:00,  2.58it/s]\n"]}, {"data": {"text/plain": ["'\\n\\n```j<PERSON>\\n{\\n\\t\"answer\": \"Paris\",\\n\\t\"source\": \"Wikipedia\"\\n}\\n```\\n\\nThe question is about the capital of France. The source provided is Wikipedia, which is a reputable website known for providing accurate and objective information.'"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["(prompt | llm).invoke({\"question\": \"what's the capital of france?\"}, )"]}, {"cell_type": "code", "execution_count": 10, "id": "8214c284-ced3-4676-9ef1-4943853f36bb", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'answer': 'Paris', 'source': 'Wikipedia'}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["output_parser.invoke('\\n\\n```json\\n{\\n\\t\"answer\": \"Paris\",\\n\\t\"source\": \"Wikipedia\"\\n}\\n```\\n\\nThe question is about the capital of France. The source provided is Wikipedia, which is a reputable website that provides reliable information about France and its capital city.\\n\\n```json\\n{\\n\\t\"answer\": \"Paris\",\\n\\t\"source\": \"Wikipedia\"\\n}\\n```')"]}, {"cell_type": "code", "execution_count": 11, "id": "c2db5011-1b62-4212-971a-500539edc06b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Processed prompts: 100%|██████████| 1/1 [00:00<00:00,  2.79it/s]\n"]}, {"data": {"text/plain": ["{'answer': 'Paris', 'source': 'Wikipedia'}"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["chain.invoke({\"question\": \"what's the capital of france?\"}, )"]}, {"cell_type": "code", "execution_count": 3, "id": "df46071e-00ec-4f41-ba71-288b7c8793ff", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Loading checkpoint shards: 100%|████████████████████████████████████████████████████| 2/2 [00:01<00:00,  1.97it/s]\n", "/network/scratch/a/artem.zholus/conda_envs/rl_jax_torch/lib/python3.10/site-packages/transformers/generation/utils.py:1178: UserWarning: Using the model-agnostic default `max_length` (=20) to control the generation length. We recommend setting `max_new_tokens` to control the maximum length of the generation.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<bos>Write me a poem about Machine Learning.\n", "\n", "Machines, they weave and they learn,\n", "From\n"]}], "source": ["# pip install accelerate\n", "from transformers import AutoTokenizer, AutoModelForCausalLM\n", "import os\n", "import torch\n", "tokenizer = AutoTokenizer.from_pretrained(\"google/gemma-2b-it\",cache_dir=os.environ['SCRATCH'] + '/hf')\n", "model = AutoModelForCausalLM.from_pretrained(\"google/gemma-2b-it\", device_map=\"auto\", torch_dtype=torch.bfloat16, cache_dir=os.environ['SCRATCH'] + '/hf')\n", "\n", "input_text = \"Write me a poem about Machine Learning.\"\n", "input_ids = tokenizer(input_text, return_tensors=\"pt\").to(\"cuda\")\n", "\n", "outputs = model.generate(**input_ids)\n", "print(tokenizer.decode(outputs[0]))\n"]}, {"cell_type": "code", "execution_count": 9, "id": "78c2cb22-0311-48c4-9dbe-d240e70ac061", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'input_ids': tensor([[     2,   5559,    682,    476,  19592,   1105,  13403,  14715, 235265]],\n", "       device='cuda:0'), 'attention_mask': tensor([[1, 1, 1, 1, 1, 1, 1, 1, 1]], device='cuda:0')}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["input_ids"]}, {"cell_type": "code", "execution_count": 8, "id": "42d458d0-a333-443f-8708-cf26d1fe0a4c", "metadata": {}, "outputs": [{"data": {"text/plain": ["GemmaConfig {\n", "  \"_name_or_path\": \"google/gemma-2b-it\",\n", "  \"architectures\": [\n", "    \"GemmaForCausalLM\"\n", "  ],\n", "  \"attention_bias\": false,\n", "  \"attention_dropout\": 0.0,\n", "  \"bos_token_id\": 2,\n", "  \"eos_token_id\": 1,\n", "  \"head_dim\": 256,\n", "  \"hidden_act\": \"gelu\",\n", "  \"hidden_size\": 2048,\n", "  \"initializer_range\": 0.02,\n", "  \"intermediate_size\": 16384,\n", "  \"max_position_embeddings\": 8192,\n", "  \"model_type\": \"gemma\",\n", "  \"num_attention_heads\": 8,\n", "  \"num_hidden_layers\": 18,\n", "  \"num_key_value_heads\": 1,\n", "  \"pad_token_id\": 0,\n", "  \"rms_norm_eps\": 1e-06,\n", "  \"rope_scaling\": null,\n", "  \"rope_theta\": 10000.0,\n", "  \"torch_dtype\": \"bfloat16\",\n", "  \"transformers_version\": \"4.38.2\",\n", "  \"use_cache\": true,\n", "  \"vocab_size\": 256000\n", "}"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["model.config"]}, {"cell_type": "code", "execution_count": 6, "id": "3cc8f8bc-b814-4357-b906-bacb79d3280b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<bos>Write me a poem about Machine Learning.\n", "\n", "A machine so wise, a mind so bright,\n", "With algorithms that shine and light.\n", "It learns from data, a never-ending quest,\n", "Algorithmic insights, a guiding test.\n", "\n", "From images and text, it extracts and gains,\n", "A symphony of rules and patterns.\n", "With every step it takes, a new path is found,\n", "A machine's intelligence, profound.\n", "\n", "From predictive analysis to chatbots that ignite,\n", "ML is shaping our world with every bite.\n", "The future unfolds, a tapestry of grace,\n", "With machines as partners, we find our place.\n", "\n", "So let us embrace this technology's might,\n", "For progress and possibilities take flight.\n", "Machine learning, a beacon in the night,\n", "Guiding us toward an era bright.<eos>\n"]}], "source": ["outputs = model.generate(**input_ids, do_sample=True, max_new_tokens=500)\n", "print(tokenizer.decode(outputs[0]))"]}, {"cell_type": "code", "execution_count": 19, "id": "79c0ed99-3854-424d-b6ff-48dc1dd12bba", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'xminigrid' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[19], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m env, env_params \u001b[39m=\u001b[39m xminigrid\u001b[39m.\u001b[39mmake(\u001b[39m\"\u001b[39m\u001b[39mXLand-MiniGrid-R4-9x9\u001b[39m\u001b[39m\"\u001b[39m)\n\u001b[1;32m      2\u001b[0m benchmark \u001b[39m=\u001b[39m my_load_benchmark(\u001b[39m\"\u001b[39m\u001b[39mmy_trivial_1m_v2\u001b[39m\u001b[39m\"\u001b[39m)\n", "\u001b[0;31mNameError\u001b[0m: name 'xminigrid' is not defined"]}], "source": ["env, env_params = xminigrid.make(\"XLand-MiniGrid-R4-9x9\")\n", "benchmark = my_load_benchmark(\"my_trivial_1m_v2\")"]}, {"cell_type": "code", "execution_count": 18, "id": "9f4dd7a8-de4c-40f0-974d-395431ab3d5a", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'jax' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[18], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m rng \u001b[39m=\u001b[39m jax\u001b[39m.\u001b[39mrandom\u001b[39m.\u001b[39mPRNGKey(\u001b[39m42\u001b[39m)\n\u001b[1;32m      2\u001b[0m ruleset \u001b[39m=\u001b[39m benchmark\u001b[39m.\u001b[39mget_ruleset(\u001b[39m1\u001b[39m)\n\u001b[1;32m      3\u001b[0m env_params \u001b[39m=\u001b[39m env_params\u001b[39m.\u001b[39mreplace(ruleset\u001b[39m=\u001b[39mruleset)\n", "\u001b[0;31mNameError\u001b[0m: name 'jax' is not defined"]}], "source": ["rng = jax.random.<PERSON><PERSON><PERSON><PERSON>(42)\n", "ruleset = benchmark.get_ruleset(1)\n", "env_params = env_params.replace(ruleset=ruleset)\n", "timestep = env.reset(env_params, rng)"]}, {"cell_type": "code", "execution_count": 12, "id": "bf3c64db-5ae7-48de-a615-d3c2db5a97df", "metadata": {}, "outputs": [], "source": ["goal_map = {\n", "    5: 'ball',\n", "    6: 'square',\n", "    7: 'pyramid',\n", "    8: 'goal',\n", "    9: 'key',\n", "    10: 'locked door',\n", "    11: 'closed door',\n", "    12: 'open door',\n", "    13: 'hex',\n", "    14: 'star'\n", "}\n", "\n", "color_map = {\n", "    3: 'red',\n", "    4: 'green',\n", "    5: 'blue',\n", "    6: 'purple',\n", "    7: 'yellow',\n", "    8: 'grey',\n", "    9: 'black',\n", "    10: 'orange',\n", "    11: 'white',\n", "    12: 'brown',\n", "    13: 'pink'\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "ec005965-0d02-4530-a66f-c936ed37d629", "metadata": {}, "outputs": [], "source": ["# print(env.render(env_params.replace(render_mode='rich_text'), timestep))"]}, {"cell_type": "code", "execution_count": 17, "id": "8b72f979-5779-402c-b959-008fd283e5d2", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'timestep' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[17], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m c \u001b[39m=\u001b[39m ((timestep\u001b[39m.\u001b[39mstate\u001b[39m.\u001b[39mgrid[\u001b[39m.\u001b[39m\u001b[39m.\u001b[39m\u001b[39m.\u001b[39m, \u001b[39m0\u001b[39m] \u001b[39m!=\u001b[39m \u001b[39m4\u001b[39m) \u001b[39m&\u001b[39m (timestep\u001b[39m.\u001b[39mstate\u001b[39m.\u001b[39mgrid[\u001b[39m.\u001b[39m\u001b[39m.\u001b[39m\u001b[39m.\u001b[39m, \u001b[39m0\u001b[39m] \u001b[39m!=\u001b[39m \u001b[39m3\u001b[39m) \u001b[39m&\u001b[39m\n\u001b[1;32m      2\u001b[0m      (timestep\u001b[39m.\u001b[39mstate\u001b[39m.\u001b[39mgrid[\u001b[39m.\u001b[39m\u001b[39m.\u001b[39m\u001b[39m.\u001b[39m, \u001b[39m0\u001b[39m] \u001b[39m!=\u001b[39m \u001b[39m11\u001b[39m) \u001b[39m&\u001b[39m (timestep\u001b[39m.\u001b[39mstate\u001b[39m.\u001b[39mgrid[\u001b[39m.\u001b[39m\u001b[39m.\u001b[39m\u001b[39m.\u001b[39m, \u001b[39m0\u001b[39m] \u001b[39m!=\u001b[39m \u001b[39m10\u001b[39m) \u001b[39m&\u001b[39m\n\u001b[1;32m      3\u001b[0m      (timestep\u001b[39m.\u001b[39mstate\u001b[39m.\u001b[39mgrid[\u001b[39m.\u001b[39m\u001b[39m.\u001b[39m\u001b[39m.\u001b[39m, \u001b[39m1\u001b[39m] \u001b[39m!=\u001b[39m \u001b[39m8\u001b[39m) \u001b[39m&\u001b[39m (timestep\u001b[39m.\u001b[39mstate\u001b[39m.\u001b[39mgrid[\u001b[39m.\u001b[39m\u001b[39m.\u001b[39m\u001b[39m.\u001b[39m, \u001b[39m1\u001b[39m] \u001b[39m!=\u001b[39m \u001b[39m9\u001b[39m))\u001b[39m.\u001b[39mnonzero()\n", "\u001b[0;31mNameError\u001b[0m: name 'timestep' is not defined"]}], "source": ["c = ((timestep.state.grid[..., 0] != 4) & (timestep.state.grid[..., 0] != 3) &\n", "     (timestep.state.grid[..., 0] != 11) & (timestep.state.grid[..., 0] != 10) &\n", "     (timestep.state.grid[..., 1] != 8) & (timestep.state.grid[..., 1] != 9)).nonzero()"]}, {"cell_type": "code", "execution_count": 14, "id": "fe9483dc-b6d0-4d97-aa41-707db14dcc5b", "metadata": {"scrolled": true}, "outputs": [], "source": ["import numpy as np"]}, {"cell_type": "code", "execution_count": 16, "id": "4f8c4579-ce2c-4aad-a8dc-24020a2cadbb", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'timestep' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[16], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m info \u001b[39m=\u001b[39m []\n\u001b[0;32m----> 2\u001b[0m agent_pos \u001b[39m=\u001b[39m np\u001b[39m.\u001b[39marray(timestep\u001b[39m.\u001b[39mstate\u001b[39m.\u001b[39magent\u001b[39m.\u001b[39mposition)\n\u001b[1;32m      3\u001b[0m \u001b[39mfor\u001b[39;00m o, x, y \u001b[39min\u001b[39;00m \u001b[39mzip\u001b[39m(np\u001b[39m.\u001b[39marray(timestep\u001b[39m.\u001b[39mstate\u001b[39m.\u001b[39mgrid[c]), \u001b[39m*\u001b[39mc):\n\u001b[1;32m      4\u001b[0m     info\u001b[39m.\u001b[39mappend((goal_map[o[\u001b[39m0\u001b[39m]], color_map[o[\u001b[39m1\u001b[39m]], x\u001b[39m.\u001b[39mitem(), y\u001b[39m.\u001b[39mitem()))\n", "\u001b[0;31mNameError\u001b[0m: name 'timestep' is not defined"]}], "source": ["info = []\n", "agent_pos = np.array(timestep.state.agent.position)\n", "for o, x, y in zip(np.array(timestep.state.grid[c]), *c):\n", "    info.append((goal_map[o[0]], color_map[o[1]], x.item(), y.item()))"]}, {"cell_type": "code", "execution_count": 6, "id": "0cc4bee9-c292-4c74-97b0-8c23529ad761", "metadata": {}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["info"]}, {"cell_type": "code", "execution_count": 7, "id": "1a33ca8d-e10b-48ee-925f-373a052ec52f", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'ruleset' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[7], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43mruleset\u001b[49m\u001b[38;5;241m.\u001b[39mgoal[\u001b[38;5;241m1\u001b[39m:\u001b[38;5;241m3\u001b[39m]\n", "\u001b[0;31mNameError\u001b[0m: name 'ruleset' is not defined"]}], "source": ["ruleset.goal[1:3]"]}, {"cell_type": "code", "execution_count": 100, "id": "947983b4-1d89-4ddb-94eb-11dd77eaf88c", "metadata": {"scrolled": true}, "outputs": [], "source": ["prompt = (f\"You are in the 4 rooms flat\" +\n", "          f\" gridworld with 2 column and 2 rows of rooms. \" +\n", "          # f\"Your position within the grid is ({agent_pos[0]}, {agent_pos[1]}).\" +\n", "          \" Adjacent rooms are connected with doors. \" +\n", "          \"Each room is 3 cells wide and 3 cells high. \") + \\\n", "          ''.join([f'The {col} {obj} is in the grid. ' for obj, col, x, y in info])"]}, {"cell_type": "code", "execution_count": 101, "id": "3370e16b-857b-4700-a04f-9818d8a43190", "metadata": {}, "outputs": [], "source": ["gobj, gcol = ruleset.goal[1:3]\n", "prompt = f'The gridworld environment desciprion: \\n{prompt}\\n' \\\n", "         f'Goal of the agent: Go to the {color_map[gcol.item()]} {goal_map[gobj.item()]}.\\n' \\\n", "         f'Break down the overall goal into short high-level step-by-step ' \\\n", "         f'instruction for the agent in the gridworld environment.\\n' \\\n", "         f'Instruction step 1: '"]}, {"cell_type": "code", "execution_count": 102, "id": "0acb715e-1659-43c3-a073-b5cd8d5b1fd9", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">The gridworld environment desciprion: \n", "You are in the <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4</span> rooms flat gridworld with <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span> column and <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span> rows of rooms.  Adjacent rooms are connected with doors. \n", "Each room is <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span> cells wide and <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span> cells high. The yellow square is in the grid. The blue hex is in the grid. The \n", "purple star is in the grid. \n", "Goal of the agent: Go to the yellow square.\n", "Break down the overall goal into short high-level step-by-step instruction for the agent in the gridworld \n", "environment.\n", "Instruction step <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>: \n", "</pre>\n"], "text/plain": ["The gridworld environment desciprion: \n", "You are in the \u001b[1;36m4\u001b[0m rooms flat gridworld with \u001b[1;36m2\u001b[0m column and \u001b[1;36m2\u001b[0m rows of rooms.  Adjacent rooms are connected with doors. \n", "Each room is \u001b[1;36m3\u001b[0m cells wide and \u001b[1;36m3\u001b[0m cells high. The yellow square is in the grid. The blue hex is in the grid. The \n", "purple star is in the grid. \n", "Goal of the agent: Go to the yellow square.\n", "Break down the overall goal into short high-level step-by-step instruction for the agent in the gridworld \n", "environment.\n", "Instruction step \u001b[1;36m1\u001b[0m: \n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(prompt)"]}, {"cell_type": "code", "execution_count": 63, "id": "2fa7e9da-4573-44d6-ba09-2006be2d9d86", "metadata": {}, "outputs": [], "source": ["from transformers import AutoTokenizer, FlaxT5ForConditionalGeneration, T5ForConditionalGeneration, \\\n", "    FlaxGemmaForCausalLM, GemmaForCausalLM \n", "import os"]}, {"cell_type": "code", "execution_count": 64, "id": "34b4868c-29c1-47e6-bfe6-d1a06e08318f", "metadata": {}, "outputs": [{"data": {"text/plain": ["'/network/scratch/a/artem.zholus'"]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["os.environ['SCRATCH']"]}, {"cell_type": "code", "execution_count": 92, "id": "f24fa2f2-590b-4685-ad9d-723da3b8c08e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Loading checkpoint shards: 100%|████████████████████████████████████████████████████████████████████| 2/2 [00:03<00:00,  1.94s/it]\n"]}], "source": ["tokenizer = AutoTokenizer.from_pretrained(\"google/gemma-2b-it\", cache_dir=os.environ['SCRATCH'] + '/hf')\n", "model = GemmaForCausalLM.from_pretrained(\"google/gemma-2b-it\", cache_dir=os.environ['SCRATCH'] + '/hf').cuda()"]}, {"cell_type": "code", "execution_count": 72, "id": "82f72d99-7d13-4e48-b31f-5665212d0cfd", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Downloading shards: 100%|███████████████████████████████████████████████████████████████████████████| 2/2 [00:55<00:00, 27.86s/it]\n", "Loading checkpoint shards: 100%|████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.66s/it]\n"]}], "source": ["# tokenizer = AutoTokenizer.from_pretrained(\"google/flan-t5-xl\", cache_dir=os.environ['SCRATCH'] + '/hf')\n", "# model = T5ForConditionalGeneration.from_pretrained(\"google/flan-t5-xl\", \n", "#                                                    cache_dir=os.environ['SCRATCH'] + '/hf').cuda()"]}, {"cell_type": "code", "execution_count": 73, "id": "a9d4bed4-3ee5-4203-8c87-2d86ed14eb7c", "metadata": {}, "outputs": [], "source": ["import torch"]}, {"cell_type": "code", "execution_count": 74, "id": "126501ab-c3dd-46dd-b435-53d554a48d03", "metadata": {}, "outputs": [], "source": ["torch.cuda.empty_cache()"]}, {"cell_type": "code", "execution_count": 103, "id": "a656d7c8-97c6-4b4d-ab75-7ed5754379ae", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">The gridworld environment desciprion: \n", "You are in the <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4</span> rooms flat gridworld with <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span> column and <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span> rows of rooms.  Adjacent rooms are connected with doors. \n", "Each room is <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span> cells wide and <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span> cells high. The yellow square is in the grid. The blue hex is in the grid. The \n", "purple star is in the grid. \n", "Goal of the agent: Go to the yellow square.\n", "Break down the overall goal into short high-level step-by-step instruction for the agent in the gridworld \n", "environment.\n", "Instruction step <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>: \n", "</pre>\n"], "text/plain": ["The gridworld environment desciprion: \n", "You are in the \u001b[1;36m4\u001b[0m rooms flat gridworld with \u001b[1;36m2\u001b[0m column and \u001b[1;36m2\u001b[0m rows of rooms.  Adjacent rooms are connected with doors. \n", "Each room is \u001b[1;36m3\u001b[0m cells wide and \u001b[1;36m3\u001b[0m cells high. The yellow square is in the grid. The blue hex is in the grid. The \n", "purple star is in the grid. \n", "Goal of the agent: Go to the yellow square.\n", "Break down the overall goal into short high-level step-by-step instruction for the agent in the gridworld \n", "environment.\n", "Instruction step \u001b[1;36m1\u001b[0m: \n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(prompt)"]}, {"cell_type": "code", "execution_count": 124, "id": "93cae55d-2b06-4f65-bc97-98b3745641fb", "metadata": {}, "outputs": [], "source": ["# with torch.amp.autocast('cuda', dtype=torch.float16):\n", "inputs = tokenizer([prompt], return_tensors=\"pt\")\n", "gen_ids = model.generate(inputs[\"input_ids\"].cuda(), do_sample=True, \n", "                      max_new_tokens=20, top_k=0, temperature=1.4, num_return_sequences=20)\n", "texts = tokenizer.batch_decode(gen_ids[:, inputs[\"input_ids\"].shape[1]:], \n", "                               skip_special_tokens=True, clean_up_tokenization_spaces=False)"]}, {"cell_type": "code", "execution_count": 125, "id": "d41ce34f-2d96-47da-8dff-43b3ef43d5ee", "metadata": {}, "outputs": [{"data": {"text/plain": ["['\\nGo to cell B3.\\nHow do you go from one cell to another and what will',\n", " '\\nMove to cell (2, 1) \\nCreate a set of possible next movements the',\n", " '\\n- Locate the yellow square from the initial position. \\n\\nHow can the agent approach the right',\n", " '\\nHead east. \\n**Understood steps: Head right, where will you look for the door',\n", " '\\n- Start in any arbitrary room in the grid.\\n- Examine the current room and find the',\n", " '\\nKnow where your starting location is\\n\\n\\nAs in the environment, you are currently in room 1',\n", " '\\n* Move one cell downward.\\n* Get obstacle information in the current room by checking the bottom',\n", " '\\n**Cell Selection and Entry:**\\n- Determine the best cell in row 1 and column ',\n", " ' imagine what isрисо .... (outer level - once the grid becomes more familiar).\\n\\n\\n**Step ',\n", " '\\n- reach 2-3 rooms down from the grid initial position.\\n\\n\\nInstruction step 2',\n", " '\\nWhat is your current position?\\nAnswer: Your current position is in the bottom left cell (',\n", " '\\n* Start at the position [4, 1].\\n* Move right 2 steps.',\n", " '\\n- Go from room 1 to room 2.\\n\\nInvestigate nearby rooms, and reach',\n", " '\\n<PERSON><PERSON><PERSON> (ActionListener) Mysterді\\n\\nRecall: \\n1. Move to the right when',\n", " '\\n\\n\\nFollow Green Door.\\n\\n\\nThe agent moves along the vertical white space between Horizontal and Vertical corridors till',\n", " \"\\nIdentify the north, west, and south's room numbers using the A, B, and\",\n", " '\\nThe agent should move from the grid.\\n\\n\\nInstruction step 2:\\nOnce in the grid',\n", " '\\nMove down from the current room.\\n\\n\\nInstruction step 2:\\nGo left through a neighbor',\n", " '\\nFrom starting position move to the south-west neighbor of #R1C1.\\n\\nInstruction',\n", " \"\\nNavigate from the current cell to the yellow square.\\n\\n\\nRemember, it's not necessary to\"]"]}, "execution_count": 125, "metadata": {}, "output_type": "execute_result"}], "source": ["texts"]}, {"cell_type": "code", "execution_count": 20, "id": "50b61284-e516-4ef1-b24d-1a4d4f68db02", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Processed prompts: 100%|██████████| 100/100 [00:00<00:00, 106.26it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["['\\n\\nThe capital of France is Paris. Paris is the political, economic, and cultural center of France. It is also the capital of the Île-de-France region.', '\\n\\nThe capital of France is Paris. Paris is a city in the Île-de-France region of France. It is the political, cultural, and economic center of France.', '\\n\\nThe capital of France is Paris. It is also the seat of the French government and the French Parliament. Paris is a major cultural and political center in Europe, and is often considered to be the most beautiful city in the world.', '\\n\\nThe capital of France is Paris.\\n\\nParis is the capital city of the Department of Paris in the Île-de-France region of France.', '\\n\\nParis is the capital of France. It is the political, economic, and cultural center of the country.', '\\n\\nThe capital of France is Paris.\\n\\nIt is the political, cultural, and economic center of France.', '\\n\\nThe capital of France is Paris. \\n\\nParis is a city in the Île-de-France region of France. It is the cultural, political, and economic center of France.', '\\n\\nThe capital of France is Paris. It is located in the north-central part of the country. Paris is also the most populous city in France, with a population of over 12 million people.', '\\n\\nThe capital of France is Paris. Paris is the political, economic, and cultural center of France. It is also the seat of the French government and the French National Assembly.', '\\n\\nThe capital of France is Paris. It is a historic city on the Seine River, and it is the political and economic center of the country.', '\\n\\nThe capital of France is Paris.\\n\\nParis is the official capital of France and the capital of the Île-de-France region. It is the largest city in France and is the seat of French government, administration, and culture.', '\\n\\nThe capital of France is Paris.\\n\\nParis is the political, economic, and cultural center of France. It is the home of many famous landmarks, including the Eiffel Tower, the Louvre Museum, and the Notre Dame Cathedral.', '\\n\\nThe capital of France is Paris. It is a city in the Île-de-France region of the country. Paris is the seat of the French government and the French parliament.', '\\n\\nThe capital of France is Paris. Paris is the political, cultural, and economic center of France. It is also the capital of the Île-de-France region and the Greater Paris region.', '\\n\\nThe capital of France is Paris. Paris is a city in the Ile-de-France region of France. It is the largest city in the country and the capital of the French Republic.', '\\n\\nParis is the capital of France. It is the political, cultural, and administrative center of the country.', '\\n\\nThe capital of France is Paris. Paris is the political, economic, and cultural center of France and is known as the \"City of Lights\". It is a major tourist destination and is home to some of the world\\'s most famous landmarks, including the Eiffel Tower, Notre Dame Cathedral, and the Louvre Museum.', '\\n\\nParis is the capital of France. It is a city on the Seine River in the north-central part of the country. Paris is a major cultural, political, and economic center of France and is known for its fashion, art, and history.', '\\n\\nThe capital of France is Paris. \\n\\nIt is the political, administrative, and cultural center of France, and is known for its landmarks, including the Eiffel Tower, Louvre Museum, and Notre Dame Cathedral.', '\\n\\nThe capital of France is Paris. It is the largest city and cultural center of France and is also the capital of the Île-de-France region.', '\\n\\nThe capital of France is Paris. It is also the seat of the French government and the seat of the French Parliament.', '\\n\\nThe capital of France is Paris. It is the political, economic, and cultural center of France.', '\\n\\nThe capital of France is Paris. It is a major city in the northwest of the country. Paris is also the seat of the French government.', '\\n\\nParis is the capital of France. It is a major city on the Seine River in the center of the country. Paris is a major political, cultural, and historical center for France.', '\\n\\nThe capital of France is Paris. Paris is a beautiful city with a rich history and culture. It is also the political and economic center of France.', '\\n\\nThe capital of France is Paris.\\n\\nParis is the political, economic and cultural center of France. It is the capital of the Île-de-France region and of the Paris metropolitan area.', '\\n\\nParis is the capital of France. It is the political, economic, and cultural center of the country.', '\\n\\nThe capital of France is Paris. \\n\\nParis is located in the centre of the country and is a major cultural and political centre. It is also the seat of the French government.', '\\n\\nThe capital of France is Paris. It is the political, economic, and cultural center of France.', '\\n\\nThe capital of France is Paris. It is the largest city and the political, economic, and cultural center of France.', '\\n\\nThe capital of France is Paris. It is located in the north of the country and is a major political and cultural center.', '\\n\\nThe capital of France is Paris. It is the seat of the French government and is a major cultural and political center in Europe.', '\\n\\nThe capital of France is Paris.\\n\\nParis is located in the northern part of France, on the Seine River. It is the largest city in France and the second-largest city in Europe, after London. Paris is a major cultural, political, and economic center of France, and is a popular tourist destination.', '\\n\\nThe capital of France is Paris.\\n\\nParis is the political, cultural, and administrative center of France. It is also the most populous city in France, with a population of over 12 million inhabitants.', '\\n\\nThe capital of France is Paris. Paris is a city in the Île-de-France region of France, on the Seine River. It is the official capital of France, and the seat of French government and politics.', '\\n\\nThe capital of France is Paris.\\n\\nParis is the political, economic, and cultural center of France. It is also the capital of the Île-de-France region.', '\\n\\nThe capital of France is Paris. \\n\\nParis is the political, economic, and cultural center of France. It is also the seat of the French government and the home of many important museums and landmarks, such as the Eiffel Tower and the Louvre Museum.', '\\n\\nThe capital of France is Paris. Paris is the political, cultural, and economic center of France and is the official seat of French government.', '\\n\\nThe capital of France is Paris. It is a major city on the River Seine in the north-central part of France. Paris is the seat of the French government and the French Parliament.', ' \\n\\nAnswer: Paris. \\n\\nParis is the capital of France. It is a major city in Europe known for its culture, art, and architecture.', '\\n\\nThe capital of France is Paris. It is the political, cultural and administrative center of France and a major tourist destination.', \"\\n\\nThe capital of France is Paris. It is a beautiful city on the banks of the Seine River. Paris is home to many of the world's most famous landmarks, including the Eiffel Tower, the Louvre Museum, and the Arc de Triomphe.\", '\\n\\nThe capital of France is Paris. It is also the seat of government and the political, cultural, and economic center of France. Paris is located in the northern part of the country, on the Seine River.', '\\n\\nThe capital of France is Paris. Paris is the political, economic, and cultural center of France. It is also the capital of the Île-de-France region.', '\\n\\nThe capital of France is Paris. It is the political, administrative, and cultural center of the French Republic. Paris is also a major cultural and artistic center for the entire world.', '\\n\\nParis is the capital of France. It is a city on the Seine River in the heart of the country. Paris is known as the City of Lights because of its many famous landmarks, including the Eiffel Tower and the Louvre Museum.', '\\n\\nThe capital of France is Paris. It is the political, economic, and cultural center of the country.', '\\n\\nThe capital of France is Paris.\\n\\nParis is the political, economic, and cultural center of France. It is located in the north-central part of the country.', '\\n\\nThe capital of France is Paris.\\n\\nParis is the political, economic, and cultural center of France. It is the seat of government, the French Parliament, and the French government. Paris is also the capital of the Île-de-France region and the Paris Metropole.', '\\n\\nThe capital of France is Paris.\\n\\nParis is the political, economic, and cultural center of France, and is known as the City of Light.', '\\n\\nThe capital of France is Paris. Paris is the political, cultural, and administrative center of France, and is also one of the most famous cities in the world.', '\\n\\nThe capital of France is Paris. It is the political, economic, and cultural center of France.', '\\n\\nThe capital of France is Paris. It is the political, economic, and cultural center of the country.', '\\n\\nThe capital of France is Paris. It is the political, economic, and cultural center of France.', '\\n\\nThe capital of France is Paris. It is a city in the Île-de-France region of the country. Paris is the capital of France because it is the political, economic, and cultural center of the country.', '\\n\\nThe capital of France is Paris. It is also the seat of the French government. Paris is located in the north-central part of France and is the most important city in the country.', '\\n\\nThe capital of France is Paris. It is located in the north-central part of the country. Paris is known as the City of Lights because of its many beautiful monuments and landmarks.', '\\n\\nThe capital of France is Paris. Paris is the political, economic and cultural center of France and is often referred to as the \"City of Lights\".', '\\n\\nThe capital of France is Paris. Paris is the political, cultural, and economic center of France. It is also the capital of the Île-de-France region and the Paris metropolitan area.', '\\n\\nThe capital of France is Paris. It is a major city on the Seine River in the north-central part of the country. Paris is also the cultural, political, and administrative center of France.', '\\n\\nThe capital of France is Paris. It is the seat of the French government and the French Parliament.', '\\n\\nThe capital of France is Paris. It is a major city in Europe, and is a popular tourist destination. Paris is known for its beautiful architecture, its rich culture, and its many attractions.', '\\n\\nThe capital of France is Paris. Paris is the political, economic and cultural center of France. It is located in the north-central part of the country.', '\\n\\nThe capital of France is Paris. It is the political, economic, and cultural center of France.', '\\n\\nThe capital of France is Paris. Paris is the political, economic, and cultural center of France, and is known as the City of Lights. It is the largest city in Western Europe, and is a major hub for tourism and trade.', '\\n\\nThe capital of France is Paris. Paris is the political, economic, and cultural center of France and is known as the \"Capital of the World\".', '\\n\\nThe capital of France is Paris. It is also the seat of the French government, the government of the European Union and the United Nations.', '\\n\\nThe capital of France is Paris. \\n\\nParis is the political, economic, and cultural center of France and is known as the City of Lights.', '\\n\\nThe capital of France is Paris. It is also the seat of the French government. Paris is located in the Île-de-France region of the country.', '\\n\\nThe capital of France is Paris. It is a major city on the banks of the River Seine and is the cultural and political center of the country.', ' \\n\\nThe capital of France is Paris. \\n\\nParis is the political, economic, and cultural center of France, and it is the most populous city in the country.', '\\n\\nThe capital of France is Paris. It is also the seat of the French government and the government of the European Union.', '\\n\\nThe capital of France is Paris. It is the political, economic and cultural center of the country.', '\\n\\nThe capital of France is Paris. \\nParis is the capital and the most populous city in France.', '\\n\\nThe capital of France is Paris. It is the political, cultural, and administrative center of the country.', '\\n\\nThe capital of France is Paris. Paris is located in the north-central part of the country. It is the political, economic, and cultural center of France.', '\\n\\nThe capital of France is Paris. It is the political, cultural, and administrative center of France.', '\\n\\nThe capital of France is Paris. It is the seat of the French government, and it is also a major cultural and artistic hub.', '\\n\\nThe capital of France is Paris. Paris is located in the northeast of France, and it is a major city in Europe. Paris is known as the City of Lights, as it has many landmarks and attractions that visitors from all over the world come to see.', '\\n\\nThe capital of France is Paris. It is a city in the northeast of the country, on the Seine River. Paris is a major cultural, political, and economic center of France and is also the official capital of the French Republic.', ' \\n\\nThe capital of France is Paris. \\n\\nParis is a city in the northern part of France. It is the seat of the French government and the French Parliament.', '\\n\\nThe capital of France is Paris.\\n\\nParis is the political, economic, and cultural center of France, and is a major city on the River Seine. It is the capital of the Île-de-France region and the Hauts-de-Seine department in the north-west of France.', '\\n\\nThe capital of France is Paris. It is the political, economic, and cultural center of the country.', '\\n\\nParis is the capital of France. It is a city in the Île-de-France region of the country. Paris is well-known for its beautiful architecture, including the Eiffel Tower and the Louvre Museum.', '\\n\\nThe capital of France is Paris. It is the political, economic, and cultural center of France.', '\\n\\nThe capital of France is Paris. It is the seat of the French government and is the political, economic and cultural center of the country.', '\\n\\nThe capital of France is Paris.\\n\\nParis is the capital of France and serves as the political, economic, and cultural center of the country. It is also the seat of the French government and the government of the European Union.', '\\n\\nThe capital of France is Paris.\\n\\nParis is a city in the north-central region of France. It is the capital and largest city of the country.', '\\n\\nThe capital of France is Paris.\\n\\nParis is the political, economic, and cultural center of France. It is also the capital of the Île-de-France region.', '\\n\\nThe capital of France is Paris.\\n\\nParis is the seat of the French government and is a major cultural and political center in Europe. It is also the capital of the Île-de-France region of France.', '\\n\\nThe capital of France is Paris. It is the political, economic, and cultural center of the country.', '\\n\\nThe capital of France is Paris. It is located in the northeastern part of the country and is a major cultural and political center.', '\\n\\nThe capital of France is Paris. It is also the capital of the Île-de-France region and of the French department of Paris. Paris is the most populous city in France, with a population of over 12 million people. It is a major cultural and political center of France.', '\\n\\nThe capital of France is Paris. Paris is the political, economic, and cultural center of France. It is also the seat of the French government and the French Parliament.', '\\n\\nParis is the capital city of France. It is a major city on the River Seine and is one of the most famous cities in the world.', '\\n\\nParis is the capital city of France. It is located in the center of the country and is a major cultural and political center.', '\\n\\nThe capital of France is Paris. It is the political, economic, and cultural center of the country.', '\\n\\nThe capital of France is Paris. Paris is a city in the Île-de-France region of France. It is the largest city in France and is the capital of the French Republic.', '\\n\\nThe capital of France is Paris.\\n\\nParis is a city in the Ile-de-France department of the northern France region of France. It is the capital of the country and is known as the City of Lights.', \"\\n\\nThe capital of France is Paris. Paris is the political, cultural, and economic center of France. It is also the home of many of the country's most famous landmarks, such as the Eiffel Tower, the Louvre Museum, and Notre Dame Cathedral.\"]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["print(llm.batch([\"What is the capital of France ?\",] * 100))"]}, {"cell_type": "code", "execution_count": 21, "id": "601864de", "metadata": {}, "outputs": [], "source": ["from langchain.output_parsers import ResponseSchema, StructuredOutputParser\n", "from langchain.prompts import PromptTemplate\n", "# from langchain_openai import ChatOpenAI\n", "response_schemas = [\n", "    ResponseSchema(name=\"answer\", description=\"answer to the user's question\"),\n", "    ResponseSchema(\n", "        name=\"source\",\n", "        description=\"source used to answer the user's question, should be a website (i.e. a URL address).\",\n", "    ),\n", "]\n", "output_parser = StructuredOutputParser.from_response_schemas(response_schemas)\n", "format_instructions = output_parser.get_format_instructions()\n", "prompt = PromptTemplate(\n", "    template=\"answer the users question as best as possible.\\n{format_instructions}\\n{question}\",\n", "    input_variables=[\"question\"],\n", "    partial_variables={\"format_instructions\": format_instructions},\n", ")"]}, {"cell_type": "code", "execution_count": 22, "id": "d9fee091", "metadata": {}, "outputs": [], "source": ["chain = prompt | llm | output_parser"]}, {"cell_type": "code", "execution_count": 23, "id": "149c6f3f", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Processed prompts: 100%|██████████| 1/1 [00:00<00:00,  2.32it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["{'answer': 'Paris', 'source': 'Wikipedia'}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["print(chain.invoke({\"question\": \"what's the capital of france?\"}))"]}, {"cell_type": "code", "execution_count": 24, "id": "e61bb790", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["answer the users question as best as possible.\n", "The output should be a markdown code snippet formatted in the following schema, including the leading and trailing \"```json\" and \"```\":\n", "\n", "```json\n", "{\n", "\t\"answer\": string  // answer to the user's question\n", "\t\"source\": string  // source used to answer the user's question, should be a website (i.e. a URL address).\n", "}\n", "```\n", "what's the capital of france?\n"]}], "source": ["print(prompt.invoke({\"question\": \"what's the capital of france?\"}).text)"]}, {"cell_type": "code", "execution_count": 25, "id": "6d5c95da", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Processed prompts: 100%|██████████| 1/1 [00:00<00:00,  3.84it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "```json\n", "{\n", "\t\"answer\": \"Paris\",\n", "\t\"source\": \"https://en.wikipedia.org/wiki/Paris\"\n", "}\n", "```\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["res = (prompt | llm).invoke({\"question\": \"what's the capital of france?\"}, )\n", "print(res)"]}, {"cell_type": "code", "execution_count": 26, "id": "c7a31685", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'answer': 'Paris', 'source': 'https://en.wikipedia.org/wiki/Paris'}"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["output_parser.invoke(res)"]}, {"cell_type": "code", "execution_count": 32, "id": "a73ecf71", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ball, square, pyramid, goal, key, locked door, closed door, open door, hex, star\n", "red, green, blue, purple, yellow, grey, black, orange, white, brown, pink\n"]}], "source": ["goal_map = {\n", "            5: 'ball',\n", "            6: 'square',\n", "            7: 'pyramid',\n", "            8: 'goal',\n", "            9: 'key',\n", "            10: 'locked door',\n", "            11: 'closed door',\n", "            12: 'open door',\n", "            13: 'hex',\n", "            14: 'star'\n", "        }\n", "color_map = {\n", "            3: 'red',\n", "            4: 'green',\n", "            5: 'blue',\n", "            6: 'purple',\n", "            7: 'yellow',\n", "            8: 'grey',\n", "            9: 'black',\n", "            10: 'orange',\n", "            11: 'white',\n", "            12: 'brown',\n", "            13: 'pink'\n", "        }\n", "\n", "obeject= ', '.join(f\"{value}\" for key, value in goal_map.items())\n", "print(obeject)\n", "colors = ', '.join(f\"{value}\" for key, value in color_map.items())\n", "print(colors)"]}, {"cell_type": "code", "execution_count": 33, "id": "a92b5b93", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["answer the users question as best as possible. \n", "You are in the 4 rooms flat gridworld with 2 column and 2 rows of rooms.\n", " Adjacent rooms are connected with doors. \n", "The objects in the environment are: ball, square, pyramid, goal, key, locked door, closed door, open door, hex, star\n", "The colors in the environment are: red, green, blue, purple, yellow, grey, black, orange, white, brown, pink\n", "Given the goal of the agent please provide what is the object and what is the color of the object that the agent should approach to? \n", "The output should be a markdown code snippet formatted in the following schema, including the leading and trailing \"```json\" and \"```\":\n", "```json\n", "{{\n", "    \"object\": string  // the target object type\n", "    \"color\": string  // target object color\n", "}}\n", "```\n", "Exmaple 1: Goal of the agent is: 'Go to the green square'. Answer: \n", "```json\n", "{{\n", "    \"object\": \"square\" \n", "    \"color\": \"green\" \n", "}}\n", "```\n", "Exmaple 2: Goal of the agent is: 'Pick up the blue key'. Answer: \n", "```json\n", "{{\n", "    \"object\": \"key\" \n", "    \"color\": \"blue\" \n", "}}\n", "```\n", "New: Goal of the agent is: 'Go to the yellow ball'. Answer: \n", "\n"]}], "source": ["clr = 'yellow'\n", "obj = 'ball'\n", "pattern = \\\n", "            \"answer the users question as best as possible. \\n\" \\\n", "            \"You are in the 4 rooms flat gridworld with 2 column and 2 rows of rooms.\\n \" \\\n", "            \"Adjacent rooms are connected with doors. \\n\" \\\n", "            f\"The objects in the environment are: {obeject}\\n\"\\\n", "            f\"The colors in the environment are: {colors}\\n\" \\\n", "            \"Given the goal of the agent please provide what is the object and what is the color of the object that the agent should approach to? \\n\" \\\n", "            'The output should be a markdown code snippet formatted in the following schema, ' \\\n", "            'including the leading and trailing \"```json\" and \"```\":\\n' \\\n", "            '```json\\n' \\\n", "            '{{\\n' \\\n", "            '    \"object\": string  // the target object type\\n' \\\n", "            '    \"color\": string  // target object color\\n' \\\n", "            '}}\\n' \\\n", "            '```\\n' \\\n", "            \"Exmaple 1: Goal of the agent is: 'Go to the green square'. Answer: \\n\" \\\n", "            '```json\\n' \\\n", "            '{{\\n' \\\n", "            '    \"object\": \"square\" \\n' \\\n", "            '    \"color\": \"green\" \\n' \\\n", "            '}}\\n' \\\n", "            '```\\n' \\\n", "            \"Exmaple 2: Goal of the agent is: 'Pick up the blue key'. Answer: \\n\" \\\n", "            '```json\\n' \\\n", "            '{{\\n' \\\n", "            '    \"object\": \"key\" \\n' \\\n", "            '    \"color\": \"blue\" \\n' \\\n", "            '}}\\n' \\\n", "            '```\\n' \\\n", "            f\"New: Goal of the agent is: 'Go to the {clr} {obj}'. Answer: \\n\" \\\n", "\n", "print(pattern)"]}, {"cell_type": "code", "execution_count": 34, "id": "2c98ef6e", "metadata": {}, "outputs": [], "source": ["from langchain.output_parsers import ResponseSchema, StructuredOutputParser\n", "from langchain.prompts import PromptTemplate\n", "response_schemas = [\n", "    ResponseSchema(name=\"object\", description=\"the target object type\"),\n", "    ResponseSchema(\n", "        name=\"color\",\n", "        description=\"target object color\",\n", "    ),\n", "]\n", "output_parser = StructuredOutputParser.from_response_schemas(response_schemas)\n", "format_instructions = output_parser.get_format_instructions()\n", "prompt = PromptTemplate(\n", "    template=\n", "    \"answer the users question as best as possible. \\n\" \n", "            \"You are in the 4 rooms flat gridworld with 2 column and 2 rows of rooms.\\n \"\n", "            \"Adjacent rooms are connected with doors. \\n\" \n", "            \"The objects in the environment are: ball, square, pyramid, goal, key, locked door, closed door, open door, hex, star \\n\"\n", "            \"The colors in the environment are: red, green, blue, purple, yellow, grey, black, orange, white, brown, pink \\n\" \n", "            \"Given the goal of the agent please provide what is the object and what is the color of the object that the agent should approach to? \\n\" \n", "            'The output should be a markdown code snippet formatted in the following schema, ' \n", "            'including the leading and trailing \"```json\" and \"```\":\\n'\n", "            '```json\\n'\n", "            '{{\\n' \n", "            '    \"object\": string  // the target object type\\n' \n", "            '    \"color\": string  // target object color\\n' \n", "            '}}\\n' \n", "            '```\\n' \n", "            \"Exmaple 1: Goal of the agent is: 'Go to the green square'. Answer: \\n\" \n", "            '```json\\n' \n", "            '{{\\n' \n", "            '    \"object\": \"square\" \\n' \n", "            '    \"color\": \"green\" \\n' \n", "            '}}\\n' \n", "            '```\\n' \n", "            \"Exmaple 2: Goal of the agent is: 'Pick up the blue key'. Answer: \\n\" \n", "            '```json\\n' \n", "            '{{\\n' \n", "            '    \"object\": \"key\" \\n' \n", "            '    \"color\": \"blue\" \\n' \n", "            '}}\\n' \n", "            '```\\n' \n", "            f\"New: Goal of the agent is: 'Go to the {clr} {obj}'. Answer: \\n\" \n", "    \n", "        \"Provide the the answer: \\n\"\n", "    \n", "        \"\\n {format_instructions}\\n{question}\",\n", "    input_variables=[\"question\"],\n", "    partial_variables={\"format_instructions\": format_instructions},\n", ")"]}, {"cell_type": "code", "execution_count": 27, "id": "b8b8d81f", "metadata": {}, "outputs": [], "source": ["response_schemas = [\n", "    ResponseSchema(name=\"object\", description=\"the target object type\"),\n", "    ResponseSchema(\n", "        name=\"color\",\n", "        description=\"target object color\",\n", "    ),\n", "]\n", "output_parser = StructuredOutputParser.from_response_schemas(response_schemas)\n", "format_instructions = output_parser.get_format_instructions()\n", "prompt = PromptTemplate(\n", "    template=\"answer the users question as best as possible.\\n\" \n", "        \"You are in the 4 rooms flat gridworld with 2 column and 2 rows of rooms.\\n\"\n", "        \"Adjacent rooms are connected with doors.\\n\"\n", "        \"Goal of the agent: Go to the blue circle.\\n\"\n", "        \"Provide the instruction for the agent.\\n\"\n", "    \n", "        \"\\n {format_instructions}\\n{question}\",\n", "    input_variables=[\"question\"],\n", "    partial_variables={\"format_instructions\": format_instructions},\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "67156f64", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 28, "id": "f43766ca", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["answer the users question as best as possible.\n", "You are in the 4 rooms flat gridworld with 2 column and 2 rows of rooms.\n", "Adjacent rooms are connected with doors.\n", "Goal of the agent: Go to the blue circle.\n", "Provide the instruction for the agent.\n", "\n", " The output should be a markdown code snippet formatted in the following schema, including the leading and trailing \"```json\" and \"```\":\n", "\n", "```json\n", "{\n", "\t\"object\": string  // the target object type\n", "\t\"color\": string  // target object color\n", "}\n", "```\n", "Instruction step 1:\n"]}], "source": ["print(prompt.invoke({'question': 'Instruction step 1:'}).text)"]}, {"cell_type": "code", "execution_count": 35, "id": "f36d5a5b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Processed prompts: 100%|██████████| 100/100 [00:01<00:00, 62.27it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["['```json\\n{\\n\\t\"object\": \"ball\",\\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\", \\n\\t\"color\": \"yellow\" \\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\" \\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\" \\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\", \\n\\t\"color\": \"yellow\" \\n}\\n```', '```\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\" \\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\"\\n}\\n```', '', '```json\\n{\\n\\t\"object\": \"ball\", \\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\" \\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\" \\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\", \\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\"\\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\",\\n\\t\"color\": \"yellow\"\\n}\\n```', '```\\n{\\n\\t\"object\": \"ball\",\\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\",\\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\" \\n}\\n```', '```json\\n{\\n  \"object\": \"ball\",\\n  \"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\"\\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\",\\n\\t\"color\": \"yellow\"\\n}\\n```', '', '```json\\n{\\n  \"object\": \"ball\",\\n  \"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\"\\n\\t\"color\": \"yellow\"\\n}\\n```', '```\\n{\\n\\t\"object\": \"ball\", \\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\" \\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\"\\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\",\\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\", \\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\", \\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n    \"object\": \"ball\",\\n    \"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\" \\n}\\n```', '```\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\" \\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\" \\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\",\\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n  \"object\": \"ball\",\\n  \"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\"\\n}\\n```', '```\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\"\\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\",\\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\", \\n\\t\"color\": \"yellow\" \\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\"\\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\",\\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\"\\n}\\n```', '```\\n{\\n\\t\"object\": \"ball\", \\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\",\\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\"\\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\" \\n}\\n```', '```json\\n{\\n    \"object\": \"ball\" \\n    \"color\": \"yellow\" \\n}\\n```', '', '```', '```json\\n{\\n\\t\"object\": \"ball\"\\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\"\\n\\t\"color\": \"yellow\"\\n}\\n```', '', '```json\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\" \\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\",\\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\",\\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\"\\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\",\\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\",\\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\",\\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\",\\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\" \\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\" \\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\",\\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\"\\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\", \\n\\t\"color\": \"yellow\" \\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\" \\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\",\\n\\t\"color\": \"yellow\"\\n}\\n```', '', '```json\\n{\\n\\t\"object\": \"ball\", \\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\" \\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\",\\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\",\\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\", \\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\"\\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\"\\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\" \\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\"\\n\\t\"color\": \"yellow\"\\n}\\n```', '```\\n{\\n\\t\"object\": \"ball\", \\n\\t\"color\": \"yellow\" \\n}\\n```', '```', '```json\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\" \\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\",\\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n    \"object\": \"ball\" \\n    \"color\": \"yellow\" \\n}\\n```', '```json\\n{\\n    \"object\": \"ball\" \\n    \"color\": \"yellow\" \\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\"\\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\" \\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\" \\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\",\\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\",\\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\"\\n\\t\"color\": \"yellow\"\\n}\\n```', '```json\\n{\\n\\t\"object\": \"ball\" \\n\\t\"color\": \"yellow\" \\n}\\n```']\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["res = (prompt | llm).batch([{'question': ''}] * 100, )\n", "print(res)"]}, {"cell_type": "code", "execution_count": 36, "id": "b9fa6f9a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 100/100 [00:00<00:00, 2274.44it/s]\n"]}, {"data": {"text/plain": ["[{'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'},\n", " {'object': 'ball', 'color': 'yellow'}]"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["from tqdm import tqdm\n", "\n", "parsed = []\n", "for r in tqdm(res):\n", "    try:\n", "        parsed.append(output_parser.invoke(r))\n", "    except:\n", "        continue\n", "\n", "parsed"]}, {"cell_type": "code", "execution_count": null, "id": "99848f47", "metadata": {}, "outputs": [], "source": []}, {"attachments": {}, "cell_type": "markdown", "id": "e32240f2", "metadata": {}, "source": ["###################"]}, {"cell_type": "code", "execution_count": 79, "id": "1bacc907", "metadata": {}, "outputs": [], "source": ["from tqdm import tqdm\n", "from langchain.output_parsers import ResponseSchema, StructuredOutputParser\n", "from langchain.prompts import PromptTemplate\n", "\n", "# Define goal and color mappings\n", "goal_map = {\n", "    5: 'ball', 6: 'square', 7: 'pyramid', 8: 'goal', 9: 'key',\n", "    10: 'locked door', 11: 'closed door', 12: 'open door', 13: 'hex', 14: 'star'\n", "}\n", "color_map = {\n", "    3: 'red', 4: 'green', 5: 'blue', 6: 'purple', 7: 'yellow',\n", "    8: 'grey', 9: 'black', 10: 'orange', 11: 'white', 12: 'brown', 13: 'pink'\n", "}\n", "\n", "# Define the response schema\n", "response_schemas = [\n", "    ResponseSchema(name=\"object\", description=\"the target object type\"),\n", "    ResponseSchema(name=\"color\", description=\"target object color\"),\n", "]\n", "output_parser = StructuredOutputParser.from_response_schemas(response_schemas)\n", "format_instructions = output_parser.get_format_instructions()\n", "\n", "# Define the prompt template\n", "prompt_template = \"You are in the 4 rooms flat gridworld with 2 columns and 2 rows of rooms.\"\\\n", "\"Adjacent rooms are connected with doors. The objects in the environment are: ball, square, pyramid, goal, key, locked door, closed door, open door, hex, star. The colors in the environment are: red, green, blue, purple, yellow, grey, black, orange, white, brown, pink. \"\\\n", "\"Given the goal of the agent, please provide the object and the color of the object that the agent should approach.\"\\\n", "     \" The output should be a markdown code snippet formatted in the following schema,\"\\\n", "     'including the leading and trailing \"```json\" and \"```\":\\n'\\\n", "    '```json\\n'\\\n", "    '{{\\n' \\\n", "    '    \"object\": string  // the target object type\\n' \\\n", "    '    \"color\": string  // target object color\\n' \\\n", "    '}}\\n' \\\n", "    '```\\n'\\\n", "    \"Exmaple 1: What is the object and color in the goal if goal of the agent is: 'Go to the green square'. Answer: \\n\" \\\n", "    '{{\\n' \\\n", "    '    \"object\": \"square\" \\n' \\\n", "    '    \"color\": \"green\" \\n' \\\n", "    '}}\\n' \\\n", "    \"Exmaple 2: What is the object and color in the goal if goal of the agent is: 'Go to the blue key'. Answer: \\n\" \\\n", "    '{{\\n' \\\n", "    '    \"object\": \"key\" \\n' \\\n", "    '    \"color\": \"blue\" \\n' \\\n", "    '}}\\n' \\\n", "  \n", "\n", "\n", "\n", "prompt = PromptTemplate(\n", "template=prompt_template+\"\\n What is the object and color in the goal if goal of the agent is: 'Go to the {clr} {obj}'\",\n", "input_variables=[\"clr\", \"obj\"],\n", "partial_variables={\"format_instructions\": format_instructions},\n", ")"]}, {"cell_type": "code", "execution_count": 54, "id": "9b319fe3", "metadata": {}, "outputs": [], "source": ["questions = [{\"clr\": clr, \"obj\": obj} for clr in color_map.values() for obj in goal_map.values()]"]}, {"cell_type": "code", "execution_count": 47, "id": "d0e77c65", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'clr': 'red', 'obj': 'ball'}, {'clr': 'red', 'obj': 'square'}, {'clr': 'red', 'obj': 'pyramid'}, {'clr': 'red', 'obj': 'goal'}, {'clr': 'red', 'obj': 'key'}, {'clr': 'red', 'obj': 'locked door'}, {'clr': 'red', 'obj': 'closed door'}, {'clr': 'red', 'obj': 'open door'}, {'clr': 'red', 'obj': 'hex'}, {'clr': 'red', 'obj': 'star'}, {'clr': 'green', 'obj': 'ball'}, {'clr': 'green', 'obj': 'square'}, {'clr': 'green', 'obj': 'pyramid'}, {'clr': 'green', 'obj': 'goal'}, {'clr': 'green', 'obj': 'key'}, {'clr': 'green', 'obj': 'locked door'}, {'clr': 'green', 'obj': 'closed door'}, {'clr': 'green', 'obj': 'open door'}, {'clr': 'green', 'obj': 'hex'}, {'clr': 'green', 'obj': 'star'}, {'clr': 'blue', 'obj': 'ball'}, {'clr': 'blue', 'obj': 'square'}, {'clr': 'blue', 'obj': 'pyramid'}, {'clr': 'blue', 'obj': 'goal'}, {'clr': 'blue', 'obj': 'key'}, {'clr': 'blue', 'obj': 'locked door'}, {'clr': 'blue', 'obj': 'closed door'}, {'clr': 'blue', 'obj': 'open door'}, {'clr': 'blue', 'obj': 'hex'}, {'clr': 'blue', 'obj': 'star'}, {'clr': 'purple', 'obj': 'ball'}, {'clr': 'purple', 'obj': 'square'}, {'clr': 'purple', 'obj': 'pyramid'}, {'clr': 'purple', 'obj': 'goal'}, {'clr': 'purple', 'obj': 'key'}, {'clr': 'purple', 'obj': 'locked door'}, {'clr': 'purple', 'obj': 'closed door'}, {'clr': 'purple', 'obj': 'open door'}, {'clr': 'purple', 'obj': 'hex'}, {'clr': 'purple', 'obj': 'star'}, {'clr': 'yellow', 'obj': 'ball'}, {'clr': 'yellow', 'obj': 'square'}, {'clr': 'yellow', 'obj': 'pyramid'}, {'clr': 'yellow', 'obj': 'goal'}, {'clr': 'yellow', 'obj': 'key'}, {'clr': 'yellow', 'obj': 'locked door'}, {'clr': 'yellow', 'obj': 'closed door'}, {'clr': 'yellow', 'obj': 'open door'}, {'clr': 'yellow', 'obj': 'hex'}, {'clr': 'yellow', 'obj': 'star'}, {'clr': 'grey', 'obj': 'ball'}, {'clr': 'grey', 'obj': 'square'}, {'clr': 'grey', 'obj': 'pyramid'}, {'clr': 'grey', 'obj': 'goal'}, {'clr': 'grey', 'obj': 'key'}, {'clr': 'grey', 'obj': 'locked door'}, {'clr': 'grey', 'obj': 'closed door'}, {'clr': 'grey', 'obj': 'open door'}, {'clr': 'grey', 'obj': 'hex'}, {'clr': 'grey', 'obj': 'star'}, {'clr': 'black', 'obj': 'ball'}, {'clr': 'black', 'obj': 'square'}, {'clr': 'black', 'obj': 'pyramid'}, {'clr': 'black', 'obj': 'goal'}, {'clr': 'black', 'obj': 'key'}, {'clr': 'black', 'obj': 'locked door'}, {'clr': 'black', 'obj': 'closed door'}, {'clr': 'black', 'obj': 'open door'}, {'clr': 'black', 'obj': 'hex'}, {'clr': 'black', 'obj': 'star'}, {'clr': 'orange', 'obj': 'ball'}, {'clr': 'orange', 'obj': 'square'}, {'clr': 'orange', 'obj': 'pyramid'}, {'clr': 'orange', 'obj': 'goal'}, {'clr': 'orange', 'obj': 'key'}, {'clr': 'orange', 'obj': 'locked door'}, {'clr': 'orange', 'obj': 'closed door'}, {'clr': 'orange', 'obj': 'open door'}, {'clr': 'orange', 'obj': 'hex'}, {'clr': 'orange', 'obj': 'star'}, {'clr': 'white', 'obj': 'ball'}, {'clr': 'white', 'obj': 'square'}, {'clr': 'white', 'obj': 'pyramid'}, {'clr': 'white', 'obj': 'goal'}, {'clr': 'white', 'obj': 'key'}, {'clr': 'white', 'obj': 'locked door'}, {'clr': 'white', 'obj': 'closed door'}, {'clr': 'white', 'obj': 'open door'}, {'clr': 'white', 'obj': 'hex'}, {'clr': 'white', 'obj': 'star'}, {'clr': 'brown', 'obj': 'ball'}, {'clr': 'brown', 'obj': 'square'}, {'clr': 'brown', 'obj': 'pyramid'}, {'clr': 'brown', 'obj': 'goal'}, {'clr': 'brown', 'obj': 'key'}, {'clr': 'brown', 'obj': 'locked door'}, {'clr': 'brown', 'obj': 'closed door'}, {'clr': 'brown', 'obj': 'open door'}, {'clr': 'brown', 'obj': 'hex'}, {'clr': 'brown', 'obj': 'star'}, {'clr': 'pink', 'obj': 'ball'}, {'clr': 'pink', 'obj': 'square'}, {'clr': 'pink', 'obj': 'pyramid'}, {'clr': 'pink', 'obj': 'goal'}, {'clr': 'pink', 'obj': 'key'}, {'clr': 'pink', 'obj': 'locked door'}, {'clr': 'pink', 'obj': 'closed door'}, {'clr': 'pink', 'obj': 'open door'}, {'clr': 'pink', 'obj': 'hex'}, {'clr': 'pink', 'obj': 'star'}]\n"]}], "source": ["print(questions)"]}, {"cell_type": "code", "execution_count": 80, "id": "800749d3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You are in the 4 rooms flat gridworld with 2 columns and 2 rows of rooms.Adjacent rooms are connected with doors. The objects in the environment are: ball, square, pyramid, goal, key, locked door, closed door, open door, hex, star. The colors in the environment are: red, green, blue, purple, yellow, grey, black, orange, white, brown, pink. Given the goal of the agent, please provide the object and the color of the object that the agent should approach. The output should be a markdown code snippet formatted in the following schema,including the leading and trailing \"```json\" and \"```\":\n", "```json\n", "{\n", "    \"object\": string  // the target object type\n", "    \"color\": string  // target object color\n", "}\n", "```\n", "Exmaple 1: What is the object and color in the goal if goal of the agent is: 'Go to the green square'. Answer: \n", "{\n", "    \"object\": \"square\" \n", "    \"color\": \"green\" \n", "}\n", "Exmaple 2: What is the object and color in the goal if goal of the agent is: 'Go to the blue key'. Answer: \n", "{\n", "    \"object\": \"key\" \n", "    \"color\": \"blue\" \n", "}\n", "\n", " What is the object and color in the goal if goal of the agent is: 'Go to the red ball'\n"]}], "source": ["print(prompt.invoke({'clr': 'red', 'obj': 'ball'}).text)"]}, {"cell_type": "code", "execution_count": 81, "id": "98768950", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Processed prompts: 100%|██████████| 110/110 [00:03<00:00, 36.49it/s]\n"]}], "source": ["res = (prompt | llm).batch(questions)"]}, {"cell_type": "code", "execution_count": 82, "id": "0c7c9337", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['?. \\nAnswer: \\n{\\n    \"object\": \"ball\" \\n    \"color\": \"red\" \\n}', ' Answer: \\n```json\\n{\\n    \"object\": \"square\"\\n    \"color\": \"red\"\\n}\\n```', ' ? \\n{\\n    \"object\": \"pyramid\" \\n    \"color\": \"red\" \\n}', ' ? \\n\\n```json\\n{\\n    \"object\": \"goal\" \\n    \"color\": \"red\" \\n}\\n```\\nWhat is the object and color in the goal if goal of the agent is: \\'Go to the ball\\' ?\\n\\n```json\\n{\\n    \"object\": \"ball\" \\n    \"color\": \"red\" \\n}\\n```', '?. Answer: \\n```json\\n{\\n    \"object\": \"key\" \\n    \"color\": \"red\" \\n}\\n```', '?. Answer: \\n```json\\n{\\n    \"object\": \"locked door\", \\n    \"color\": \"grey\" \\n}\\n```', '?.\\nAnswer:\\n```json\\n{\\n    \"object\": \"closed door\" \\n    \"color\": \"grey\" \\n}\\n```\\n\\nThe code above is a template and should be modified according to the actual environment and goal of the agent.', '?. \\nAnswer:\\n```json\\n{\\n    \"object\": \"open door\", \\n    \"color\": \"red\"\\n}\\n```', '?. Answer: \\n```json\\n{\\n    \"object\": \"hex\" \\n    \"color\": \"red\"\\n}\\n```', '?. \\n{\\n    \"object\": \"star\"\\n    \"color\": \"red\"\\n}\\n', '?. Answer: \\n{\\n    \"object\": \"ball\" \\n    \"color\": \"green\" \\n}\\n\\nRemember to handle the situation when the goal object is not found, and output a meaningful error message.', ' and the key is unlocked? Answer: \\n{\\n    \"object\": \"square\" \\n    \"color\": \"green\" \\n}', ' ?. Answer:\\n{\\n    \"object\": \"pyramid\" \\n    \"color\": \"green\" \\n}\\n\\nI hope you understand the prompt. Please let me know if you need any clarification or if you have any more questions.', '?. Answer: \\n```json\\n{\\n    \"object\": \"goal\" \\n    \"color\": \"green\" \\n}\\n```', ' ? \\n```json\\n{\\n    \"object\": \"key\" \\n    \"color\": \"green\" \\n}\\n```', '?. Answer:\\n\\n```json\\n{\\n    \"object\": \"locked door\",\\n    \"color\": \"grey\"\\n}\\n```\\n\\nI hope the explanation is clear and helps in understanding. Please feel free to ask any question that might arise.', '?.\\n Answer:\\n{\\n    \"object\": \"closed door\" \\n    \"color\": \"grey\" \\n}', '?. Answer: \\n```json\\n{\\n    \"object\": \"open door\" \\n    \"color\": \"green\" \\n}\\n```\\n\\nPlease note that the objects in the environment might be present multiple times, and the agent should be instructed to approach the first object it finds.', '?. \\n\\nAnswer:\\n{\\n    \"object\": \"hex\" \\n    \"color\": \"green\" \\n}', '?. Answer: \\n```json\\n{\\n    \"object\": \"star\"\\n    \"color\": \"green\"\\n}\\n```', '?. Answer: \\n```json\\n{\\n    \"object\": \"ball\" \\n    \"color\": \"blue\" \\n}\\n```\\n\\nThe agent should approach the blue ball, as it is the object that is mentioned in the goal.', '?. Answer: \\n```json\\n{\\n    \"object\": \"square\" \\n    \"color\": \"blue\" \\n}\\n```\\n\\n```python\\ngoal = \"Go to the green square\"\\n\\nresult = next((room for room in rooms if room[0] == \\'S\\' and room[1] == \\'G\\' and room[2] == \\'Green\\'), None)\\n\\nprint(f\"The object and color in the goal is {result[\\'object\\'][\\'color\\']}\")\\n```', '?. \\n{\\n    \"object\": \"pyramid\" \\n    \"color\": \"blue\" \\n}', ' ?\\n```json\\n{\\n    \"object\": \"goal\" \\n    \"color\": \"blue\" \\n}\\n```', ' and the key is locked? Answer: \\n```json\\n{\\n    \"object\": \"key\", \\n    \"color\": \"blue\" \\n}\\n```\\n\\nRemember to use the colors and objects present in the gridworld and ensure that the answer is consistent with the goal.', '?. \\nAnswer:\\n```json\\n{\\n    \"object\": \"locked door\" \\n    \"color\": \"black\" \\n}\\n```\\n\\nThe goal of the agent is \"Go to the green square\". Therefore, the object to approach and the color to be searched are the green square and the square, respectively.', '?. \\nAnswer: \\n{\\n    \"object\": \"closed door\" \\n    \"color\": \"black\" \\n}', '?. \\n{\\n    \"object\": \"open door\" \\n    \"color\": \"blue\" \\n}', '?. Answer:\\n```json\\n{\\n    \"object\": \"hex\" \\n    \"color\": \"blue\"\\n}\\n```', ' ? \\n\\n```json\\n{\\n    \"object\": \"star\"\\n    \"color\": \"blue\"\\n}\\n```', '\\'. Answer: \\n```json\\n{\\n    \"object\": \"ball\" \\n    \"color\": \"purple\" \\n}\\n```', ' ?\\n```json\\n{\\n    \"object\": \"square\",\\n    \"color\": \"purple\"\\n}\\n```', '?. \\n{\\n    \"object\": \"pyramid\" \\n    \"color\": \"purple\" \\n}', ' ?\\n\\n```json\\n{\\n    \"object\": \"goal\" \\n    \"color\": \"purple\" \\n}\\n```', ' ?\\n```', ' ?.\\n\\n```json\\n{\\n    \"object\": \"locked door\", \\n    \"color\": \"grey\"\\n}\\n```', ' ?\\n```json\\n{\\n    \"object\": \"closed door\",\\n    \"color\": \"grey\"\\n}\\n```', '?.\\n\\n```json\\n{\\n    \"object\": \"open door\",\\n    \"color\": \"purple\"\\n}\\n```', '?. \\n```json\\n{\\n    \"object\": \"hex\" ,\\n    \"color\": \"purple\"\\n}\\n```\\n\\nThis code can be used as a visual guide for an AI to navigate the gridworld by following the path from the start to the goal.', '?. Answer: \\n{\\n    \"object\": \"star\" \\n    \"color\": \"purple\" \\n}', '?.\\n{\\n    \"object\": \"ball\"\\n    \"color\": \"yellow\"\\n}', '?. Answer: \\n```json\\n{\\n    \"object\": \"square\" \\n    \"color\": \"yellow\" \\n}\\n```', ' ?\\n```json\\n{\\n    \"object\": \"pyramid\" \\n    \"color\": \"yellow\" \\n}\\n```', ' ? \\n{\\n    \"object\": \"goal\" \\n    \"color\": \"yellow\" \\n}\\n', ' ?\\n```json\\n{\\n    \"object\": \"key\" \\n    \"color\": \"yellow\" \\n}\\n```\\n\\nThis script assumes that the goal of the agent is explicitly described in the prompt. If the goal is not explicitly stated, the script cannot determine the object and color of the goal and cannot provide a solution.', '?. Answer: \\n{\\n    \"object\": \"locked door\"\\n    \"color\": \"black\"\\n}', '?.\\n```json\\n{\\n    \"object\": \"closed door\" \\n    \"color\": \"grey\" \\n}\\n```', ' ? \\n```json\\n{\\n    \"object\": \"open door\" \\n    \"color\": \"yellow\"\\n}\\n```', '?.\\n```json\\n{\\n    \"object\": \"hex\", \\n    \"color\": \"yellow\"\\n}\\n```', ' ? \\n```json\\n{\\n    \"object\": \"star\" \\n    \"color\": \"yellow\" \\n}\\n\\n```', '?. Answer: \\n{\\n    \"object\": \"ball\"\\n    \"color\": \"grey\" \\n}', '?.\\nAnswer: \\n```json\\n{\\n  \"object\": \"square\",\\n  \"color\": \"grey\"\\n}\\n```\\n\\nWhat is the object and color in the goal if goal of the agent is: \\'Go to the locked door\\'?.\\nAnswer: \\n```json\\n{\\n  \"object\": \"locked door\",\\n  \"color\": \"black\"\\n}\\n```', '?. Answer: \\n{\\n    \"object\": \"pyramid\" \\n    \"color\": \"grey\" \\n}', '?.\\n**Answer:**\\n```json\\n{\\n    \"object\": \"goal\", \\n    \"color\": \"grey\"\\n}\\n```', ' Answer: \\n```json\\n{\\n    \"object\": \"locked door\", \\n    \"color\": \"grey\"\\n}\\n```', ' ?\\n```json\\n{\\n    \"object\": \"locked door\",\\n    \"color\": \"grey\"\\n}\\n```', '?. Answer: \\n```json\\n{\\n    \"object\": \"closed door\"\\n    \"color\": \"grey\"\\n}\\n```\\n\\nPlease use a programming language of your choice.', '?.\\n{\\n    \"object\": \"open door\" \\n    \"color\": \"grey\" \\n}', ' ? \\n{\\n    \"object\": \"hex\" \\n    \"color\": \"grey\" \\n}\\n', ' ? \\n{\\n    \"object\": \"star\" \\n    \"color\": \"grey\" \\n}', '?. \\n**Answer:**\\n```json\\n{\\n    \"object\": \"ball\" \\n    \"color\": \"black\" \\n}\\n```', ' Answer: \\n```json\\n{\\n    \"object\": \"square\", \\n    \"color\": \"black\" \\n}\\n```', ' ? \\n```json\\n{\\n    \"object\": \"pyramid\"\\n    \"color\": \"black\"\\n}\\n```', '\\'. Answer:\\n\\n```json\\n{\\n    \"object\": \"goal\", \\n    \"color\": \"black\"\\n}\\n```', ' ?\\n\\n```json\\n{\\n    \"object\": \"key\",\\n    \"color\": \"black\"\\n}\\n```', ' ?\\n```json\\n{\\n    \"object\": \"locked door\", \\n    \"color\": \"black\" \\n}\\n```\\n\\n**Note:** The goal is located in the top-left room and the grid is symmetrical along the vertical axis.', '?. Answer: \\n```json\\n{\\n    \"object\": \"closed door\",\\n    \"color\": \"black\"\\n}\\n```', ' ? \\n\\n{\\n    \"object\": \"locked door\", \\n    \"color\": \"black\"\\n}\\n\\nPlease note that the gridworld is 2x2 and the object may be in any of the 4 rooms.', ' ? \\n{\\n    \"object\": \"hex\" \\n    \"color\": \"black\" \\n}', '\\'. Answer:\\n```json\\n{\\n    \"object\": \"star\" \\n    \"color\": \"black\" \\n}\\n```', '?. Answer: \\n```json\\n{\\n    \"object\": \"ball\" \\n    \"color\": \"orange\" \\n}\\n```', '?. \\n\\n```json\\n{\\n    \"object\": \"square\",\\n    \"color\": \"orange\"\\n}\\n```\\n\\nPlease use your creativity and logic to solve the problem.', ' ? \\n```json\\n{\\n    \"object\": \"pyramid\" \\n    \"color\": \"orange\" \\n}\\n```', ' ? \\n```json\\n{\\n    \"object\": \"goal\" \\n    \"color\": \"orange\" \\n}\\n```', '?. \\n{\\n    \"object\": \"key\" \\n    \"color\": \"orange\" \\n}\\n', '?. Answer:\\n```json\\n{\\n    \"object\": \"locked door\" \\n    \"color\": \"orange\" \\n}\\n```', ' . Answer: \\n```json\\n{\\n    \"object\": \"closed door\", \\n    \"color\": \"orange\" \\n}\\n```\\n\\nNote that the agent is in the room with the \"ball\" object.', ' ?. \\n```json\\n{\\n    \"object\": \"open door\",\\n    \"color\": \"orange\"\\n}\\n```\\n\\nPlease note that the gridworld has a limited size of 2 rows and 2 columns.', '?. \\n{\\n    \"object\": \"hex\" \\n    \"color\": \"orange\" \\n}', ' ? \\n```json\\n{\\n    \"object\": \"star\",\\n    \"color\": \"orange\"\\n}\\n```\\n\\nPlease let me know if there is anything else that I can do to clarify or provide more context about the problem.', '?. Answer: \\n```json\\n{\\n    \"object\": \"ball\" \\n    \"color\": \"white\" \\n}\\n```', '?. Answer: \\n```json\\n{\\n    \"object\": \"square\" \\n    \"color\": \"white\" \\n}\\n```\\nYou can use these examples to implement your logic and build your AI model.', '?.\\n{\\n    \"object\": \"pyramid\" \\n    \"color\": \"white\" \\n}\\n', ' ? \\n{\\n    \"object\": \"goal\" \\n    \"color\": \"white\" \\n}', ' ? \\n```json\\n{\\n    \"object\": \"key\"\\n    \"color\": \"white\"\\n}\\n```\\n\\nThe search is done using the provided gridworld with 2 columns and 2 rows of rooms.', ' ?\\n```json\\n{\\n    \"object\": \"locked door\",\\n    \"color\": \"grey\"\\n}\\n```', ' ? \\n```json\\n{\\n    \"object\": \"closed door\"\\n    \"color\": \"white\"\\n}\\n```', '?. \\nAnswer: \\n{\\n    \"object\": \"open door\" \\n    \"color\": \"white\" \\n}', '?. \\n```json\\n{\\n    \"object\": \"hex\" \\n    \"color\": \"white\" \\n}\\n```', '?. \\n```json\\n{\\n    \"object\": \"star\",\\n    \"color\": \"white\"\\n}\\n```\\n\\n**Note:** The goal may be ambiguous. The answer can be multiple objects with different colors depending on the context.', ' ?\\n```json\\n{\\n    \"object\": \"ball\"\\n    \"color\": \"brown\"\\n}\\n```\\n\\nThe agent should approach the \"ball\" and its color is brown.', '?.\\n**Answer:** \\n{\\n    \"object\": \"square\" \\n    \"color\": \"brown\" \\n}\\n', '?. Answer:\\n{\\n    \"object\": \"pyramid\" \\n    \"color\": \"brown\" \\n}', '?. Answer: \\n```json\\n{\\n    \"object\": \"goal\"\\n    \"color\": \"brown\" \\n}\\n```', '?. \\nAnswer: \\n{\\n    \"object\": \"key\", \\n    \"color\": \"brown\" \\n}', ' . Answer: \\n\\n```json\\n{\\n    \"object\": \"locked door\" \\n    \"color\": \"grey\" \\n}\\n```', '?. Answer: \\n```json\\n{\\n    \"object\": \"locked door\" \\n    \"color\": \"grey\" \\n}\\n```', ' ? \\n{\\n    \"object\": \"open door\" \\n    \"color\": \"brown\"\\n}', '?. Answer:\\n{\\n    \"object\": \"hex\"\\n    \"color\": \"brown\"\\n}', '?.\\n\\n```json\\n{\\n    \"object\": \"star\" \\n    \"color\": \"brown\" \\n}\\n```', '\\'. Answer: \\n```json\\n{\\n    \"object\": \"ball\" \\n    \"color\": \"pink\" \\n}\\n```', ' ? \\n```json\\n{\\n    \"object\": \"square\",\\n    \"color\": \"pink\"\\n}\\n```', ' ? \\n```json\\n{\\n    \"object\": \"pyramid\",\\n    \"color\": \"pink\"\\n}\\n```', ' ? \\n```json\\n{\\n    \"object\": \"goal\", \\n    \"color\": \"pink\"\\n}\\n```', ' ?\\n```json\\n{\\n    \"object\": \"key\",\\n    \"color\": \"pink\"\\n}\\n```', '?. \\n{\\n    \"object\": \"locked door\" \\n    \"color\": \"pink\"\\n}', '?.\\nAnswer:\\n```json\\n{\\n    \"object\": \"locked door\", \\n    \"color\": \"black\" \\n}\\n```', ' ?\\n```json\\n{\\n  \"object\": \"open door\", \\n  \"color\": \"pink\" \\n}\\n```', ' ?\\n\\n```json\\n{\\n  \"object\": \"hex\",\\n  \"color\": \"pink\"\\n}\\n```', ' ? \\n```json\\n{\\n    \"object\": \"star\" \\n    \"color\": \"pink\" \\n}\\n```']\n"]}], "source": ["print(res)"]}, {"cell_type": "code", "execution_count": null, "id": "a42d3262", "metadata": {}, "outputs": [], "source": ["correct_count = 0\n", "parsed_responses = []\n", "\n", "for r, q in tqdm(zip(res, questions), total=len(questions)):\n", "    print(r, q)\n", "    parsed = output_parser.parse(r)\n", "    parsed_responses.append(parsed)\n", "\n", "    # Check if the parsed response matches the input question\n", "    if parsed[\"color\"] == q[\"clr\"] and parsed[\"object\"] == q[\"obj\"]:\n", "        correct_count += 1\n", "    # except Exception as e:\n", "    #     print(f\"Failed to parse: {r}, Error: {e}\")   \n", "\n", "print(f\"Correctly parsed responses: {correct_count}/{len(questions)}\")       "]}, {"cell_type": "code", "execution_count": 83, "id": "d648b937", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 110/110 [00:00<00:00, 572.13it/s]\n"]}], "source": ["parsed = []\n", "for r in tqdm(res):\n", "    try:\n", "        parsed.append(output_parser.invoke(r))\n", "    except:\n", "        continue"]}, {"cell_type": "code", "execution_count": 84, "id": "18cefbad", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['?. \\nAnswer: \\n{\\n    \"object\": \"ball\" \\n    \"color\": \"red\" \\n}', ' Answer: \\n```json\\n{\\n    \"object\": \"square\"\\n    \"color\": \"red\"\\n}\\n```', ' ? \\n{\\n    \"object\": \"pyramid\" \\n    \"color\": \"red\" \\n}', ' ? \\n\\n```json\\n{\\n    \"object\": \"goal\" \\n    \"color\": \"red\" \\n}\\n```\\nWhat is the object and color in the goal if goal of the agent is: \\'Go to the ball\\' ?\\n\\n```json\\n{\\n    \"object\": \"ball\" \\n    \"color\": \"red\" \\n}\\n```', '?. Answer: \\n```json\\n{\\n    \"object\": \"key\" \\n    \"color\": \"red\" \\n}\\n```', '?. Answer: \\n```json\\n{\\n    \"object\": \"locked door\", \\n    \"color\": \"grey\" \\n}\\n```', '?.\\nAnswer:\\n```json\\n{\\n    \"object\": \"closed door\" \\n    \"color\": \"grey\" \\n}\\n```\\n\\nThe code above is a template and should be modified according to the actual environment and goal of the agent.', '?. \\nAnswer:\\n```json\\n{\\n    \"object\": \"open door\", \\n    \"color\": \"red\"\\n}\\n```', '?. Answer: \\n```json\\n{\\n    \"object\": \"hex\" \\n    \"color\": \"red\"\\n}\\n```', '?. \\n{\\n    \"object\": \"star\"\\n    \"color\": \"red\"\\n}\\n', '?. Answer: \\n{\\n    \"object\": \"ball\" \\n    \"color\": \"green\" \\n}\\n\\nRemember to handle the situation when the goal object is not found, and output a meaningful error message.', ' and the key is unlocked? Answer: \\n{\\n    \"object\": \"square\" \\n    \"color\": \"green\" \\n}', ' ?. Answer:\\n{\\n    \"object\": \"pyramid\" \\n    \"color\": \"green\" \\n}\\n\\nI hope you understand the prompt. Please let me know if you need any clarification or if you have any more questions.', '?. Answer: \\n```json\\n{\\n    \"object\": \"goal\" \\n    \"color\": \"green\" \\n}\\n```', ' ? \\n```json\\n{\\n    \"object\": \"key\" \\n    \"color\": \"green\" \\n}\\n```', '?. Answer:\\n\\n```json\\n{\\n    \"object\": \"locked door\",\\n    \"color\": \"grey\"\\n}\\n```\\n\\nI hope the explanation is clear and helps in understanding. Please feel free to ask any question that might arise.', '?.\\n Answer:\\n{\\n    \"object\": \"closed door\" \\n    \"color\": \"grey\" \\n}', '?. Answer: \\n```json\\n{\\n    \"object\": \"open door\" \\n    \"color\": \"green\" \\n}\\n```\\n\\nPlease note that the objects in the environment might be present multiple times, and the agent should be instructed to approach the first object it finds.', '?. \\n\\nAnswer:\\n{\\n    \"object\": \"hex\" \\n    \"color\": \"green\" \\n}', '?. Answer: \\n```json\\n{\\n    \"object\": \"star\"\\n    \"color\": \"green\"\\n}\\n```', '?. Answer: \\n```json\\n{\\n    \"object\": \"ball\" \\n    \"color\": \"blue\" \\n}\\n```\\n\\nThe agent should approach the blue ball, as it is the object that is mentioned in the goal.', '?. Answer: \\n```json\\n{\\n    \"object\": \"square\" \\n    \"color\": \"blue\" \\n}\\n```\\n\\n```python\\ngoal = \"Go to the green square\"\\n\\nresult = next((room for room in rooms if room[0] == \\'S\\' and room[1] == \\'G\\' and room[2] == \\'Green\\'), None)\\n\\nprint(f\"The object and color in the goal is {result[\\'object\\'][\\'color\\']}\")\\n```', '?. \\n{\\n    \"object\": \"pyramid\" \\n    \"color\": \"blue\" \\n}', ' ?\\n```json\\n{\\n    \"object\": \"goal\" \\n    \"color\": \"blue\" \\n}\\n```', ' and the key is locked? Answer: \\n```json\\n{\\n    \"object\": \"key\", \\n    \"color\": \"blue\" \\n}\\n```\\n\\nRemember to use the colors and objects present in the gridworld and ensure that the answer is consistent with the goal.', '?. \\nAnswer:\\n```json\\n{\\n    \"object\": \"locked door\" \\n    \"color\": \"black\" \\n}\\n```\\n\\nThe goal of the agent is \"Go to the green square\". Therefore, the object to approach and the color to be searched are the green square and the square, respectively.', '?. \\nAnswer: \\n{\\n    \"object\": \"closed door\" \\n    \"color\": \"black\" \\n}', '?. \\n{\\n    \"object\": \"open door\" \\n    \"color\": \"blue\" \\n}', '?. Answer:\\n```json\\n{\\n    \"object\": \"hex\" \\n    \"color\": \"blue\"\\n}\\n```', ' ? \\n\\n```json\\n{\\n    \"object\": \"star\"\\n    \"color\": \"blue\"\\n}\\n```', '\\'. Answer: \\n```json\\n{\\n    \"object\": \"ball\" \\n    \"color\": \"purple\" \\n}\\n```', ' ?\\n```json\\n{\\n    \"object\": \"square\",\\n    \"color\": \"purple\"\\n}\\n```', '?. \\n{\\n    \"object\": \"pyramid\" \\n    \"color\": \"purple\" \\n}', ' ?\\n\\n```json\\n{\\n    \"object\": \"goal\" \\n    \"color\": \"purple\" \\n}\\n```', ' ?\\n```', ' ?.\\n\\n```json\\n{\\n    \"object\": \"locked door\", \\n    \"color\": \"grey\"\\n}\\n```', ' ?\\n```json\\n{\\n    \"object\": \"closed door\",\\n    \"color\": \"grey\"\\n}\\n```', '?.\\n\\n```json\\n{\\n    \"object\": \"open door\",\\n    \"color\": \"purple\"\\n}\\n```', '?. \\n```json\\n{\\n    \"object\": \"hex\" ,\\n    \"color\": \"purple\"\\n}\\n```\\n\\nThis code can be used as a visual guide for an AI to navigate the gridworld by following the path from the start to the goal.', '?. Answer: \\n{\\n    \"object\": \"star\" \\n    \"color\": \"purple\" \\n}', '?.\\n{\\n    \"object\": \"ball\"\\n    \"color\": \"yellow\"\\n}', '?. Answer: \\n```json\\n{\\n    \"object\": \"square\" \\n    \"color\": \"yellow\" \\n}\\n```', ' ?\\n```json\\n{\\n    \"object\": \"pyramid\" \\n    \"color\": \"yellow\" \\n}\\n```', ' ? \\n{\\n    \"object\": \"goal\" \\n    \"color\": \"yellow\" \\n}\\n', ' ?\\n```json\\n{\\n    \"object\": \"key\" \\n    \"color\": \"yellow\" \\n}\\n```\\n\\nThis script assumes that the goal of the agent is explicitly described in the prompt. If the goal is not explicitly stated, the script cannot determine the object and color of the goal and cannot provide a solution.', '?. Answer: \\n{\\n    \"object\": \"locked door\"\\n    \"color\": \"black\"\\n}', '?.\\n```json\\n{\\n    \"object\": \"closed door\" \\n    \"color\": \"grey\" \\n}\\n```', ' ? \\n```json\\n{\\n    \"object\": \"open door\" \\n    \"color\": \"yellow\"\\n}\\n```', '?.\\n```json\\n{\\n    \"object\": \"hex\", \\n    \"color\": \"yellow\"\\n}\\n```', ' ? \\n```json\\n{\\n    \"object\": \"star\" \\n    \"color\": \"yellow\" \\n}\\n\\n```', '?. Answer: \\n{\\n    \"object\": \"ball\"\\n    \"color\": \"grey\" \\n}', '?.\\nAnswer: \\n```json\\n{\\n  \"object\": \"square\",\\n  \"color\": \"grey\"\\n}\\n```\\n\\nWhat is the object and color in the goal if goal of the agent is: \\'Go to the locked door\\'?.\\nAnswer: \\n```json\\n{\\n  \"object\": \"locked door\",\\n  \"color\": \"black\"\\n}\\n```', '?. Answer: \\n{\\n    \"object\": \"pyramid\" \\n    \"color\": \"grey\" \\n}', '?.\\n**Answer:**\\n```json\\n{\\n    \"object\": \"goal\", \\n    \"color\": \"grey\"\\n}\\n```', ' Answer: \\n```json\\n{\\n    \"object\": \"locked door\", \\n    \"color\": \"grey\"\\n}\\n```', ' ?\\n```json\\n{\\n    \"object\": \"locked door\",\\n    \"color\": \"grey\"\\n}\\n```', '?. Answer: \\n```json\\n{\\n    \"object\": \"closed door\"\\n    \"color\": \"grey\"\\n}\\n```\\n\\nPlease use a programming language of your choice.', '?.\\n{\\n    \"object\": \"open door\" \\n    \"color\": \"grey\" \\n}', ' ? \\n{\\n    \"object\": \"hex\" \\n    \"color\": \"grey\" \\n}\\n', ' ? \\n{\\n    \"object\": \"star\" \\n    \"color\": \"grey\" \\n}', '?. \\n**Answer:**\\n```json\\n{\\n    \"object\": \"ball\" \\n    \"color\": \"black\" \\n}\\n```', ' Answer: \\n```json\\n{\\n    \"object\": \"square\", \\n    \"color\": \"black\" \\n}\\n```', ' ? \\n```json\\n{\\n    \"object\": \"pyramid\"\\n    \"color\": \"black\"\\n}\\n```', '\\'. Answer:\\n\\n```json\\n{\\n    \"object\": \"goal\", \\n    \"color\": \"black\"\\n}\\n```', ' ?\\n\\n```json\\n{\\n    \"object\": \"key\",\\n    \"color\": \"black\"\\n}\\n```', ' ?\\n```json\\n{\\n    \"object\": \"locked door\", \\n    \"color\": \"black\" \\n}\\n```\\n\\n**Note:** The goal is located in the top-left room and the grid is symmetrical along the vertical axis.', '?. Answer: \\n```json\\n{\\n    \"object\": \"closed door\",\\n    \"color\": \"black\"\\n}\\n```', ' ? \\n\\n{\\n    \"object\": \"locked door\", \\n    \"color\": \"black\"\\n}\\n\\nPlease note that the gridworld is 2x2 and the object may be in any of the 4 rooms.', ' ? \\n{\\n    \"object\": \"hex\" \\n    \"color\": \"black\" \\n}', '\\'. Answer:\\n```json\\n{\\n    \"object\": \"star\" \\n    \"color\": \"black\" \\n}\\n```', '?. Answer: \\n```json\\n{\\n    \"object\": \"ball\" \\n    \"color\": \"orange\" \\n}\\n```', '?. \\n\\n```json\\n{\\n    \"object\": \"square\",\\n    \"color\": \"orange\"\\n}\\n```\\n\\nPlease use your creativity and logic to solve the problem.', ' ? \\n```json\\n{\\n    \"object\": \"pyramid\" \\n    \"color\": \"orange\" \\n}\\n```', ' ? \\n```json\\n{\\n    \"object\": \"goal\" \\n    \"color\": \"orange\" \\n}\\n```', '?. \\n{\\n    \"object\": \"key\" \\n    \"color\": \"orange\" \\n}\\n', '?. Answer:\\n```json\\n{\\n    \"object\": \"locked door\" \\n    \"color\": \"orange\" \\n}\\n```', ' . Answer: \\n```json\\n{\\n    \"object\": \"closed door\", \\n    \"color\": \"orange\" \\n}\\n```\\n\\nNote that the agent is in the room with the \"ball\" object.', ' ?. \\n```json\\n{\\n    \"object\": \"open door\",\\n    \"color\": \"orange\"\\n}\\n```\\n\\nPlease note that the gridworld has a limited size of 2 rows and 2 columns.', '?. \\n{\\n    \"object\": \"hex\" \\n    \"color\": \"orange\" \\n}', ' ? \\n```json\\n{\\n    \"object\": \"star\",\\n    \"color\": \"orange\"\\n}\\n```\\n\\nPlease let me know if there is anything else that I can do to clarify or provide more context about the problem.', '?. Answer: \\n```json\\n{\\n    \"object\": \"ball\" \\n    \"color\": \"white\" \\n}\\n```', '?. Answer: \\n```json\\n{\\n    \"object\": \"square\" \\n    \"color\": \"white\" \\n}\\n```\\nYou can use these examples to implement your logic and build your AI model.', '?.\\n{\\n    \"object\": \"pyramid\" \\n    \"color\": \"white\" \\n}\\n', ' ? \\n{\\n    \"object\": \"goal\" \\n    \"color\": \"white\" \\n}', ' ? \\n```json\\n{\\n    \"object\": \"key\"\\n    \"color\": \"white\"\\n}\\n```\\n\\nThe search is done using the provided gridworld with 2 columns and 2 rows of rooms.', ' ?\\n```json\\n{\\n    \"object\": \"locked door\",\\n    \"color\": \"grey\"\\n}\\n```', ' ? \\n```json\\n{\\n    \"object\": \"closed door\"\\n    \"color\": \"white\"\\n}\\n```', '?. \\nAnswer: \\n{\\n    \"object\": \"open door\" \\n    \"color\": \"white\" \\n}', '?. \\n```json\\n{\\n    \"object\": \"hex\" \\n    \"color\": \"white\" \\n}\\n```', '?. \\n```json\\n{\\n    \"object\": \"star\",\\n    \"color\": \"white\"\\n}\\n```\\n\\n**Note:** The goal may be ambiguous. The answer can be multiple objects with different colors depending on the context.', ' ?\\n```json\\n{\\n    \"object\": \"ball\"\\n    \"color\": \"brown\"\\n}\\n```\\n\\nThe agent should approach the \"ball\" and its color is brown.', '?.\\n**Answer:** \\n{\\n    \"object\": \"square\" \\n    \"color\": \"brown\" \\n}\\n', '?. Answer:\\n{\\n    \"object\": \"pyramid\" \\n    \"color\": \"brown\" \\n}', '?. Answer: \\n```json\\n{\\n    \"object\": \"goal\"\\n    \"color\": \"brown\" \\n}\\n```', '?. \\nAnswer: \\n{\\n    \"object\": \"key\", \\n    \"color\": \"brown\" \\n}', ' . Answer: \\n\\n```json\\n{\\n    \"object\": \"locked door\" \\n    \"color\": \"grey\" \\n}\\n```', '?. Answer: \\n```json\\n{\\n    \"object\": \"locked door\" \\n    \"color\": \"grey\" \\n}\\n```', ' ? \\n{\\n    \"object\": \"open door\" \\n    \"color\": \"brown\"\\n}', '?. Answer:\\n{\\n    \"object\": \"hex\"\\n    \"color\": \"brown\"\\n}', '?.\\n\\n```json\\n{\\n    \"object\": \"star\" \\n    \"color\": \"brown\" \\n}\\n```', '\\'. Answer: \\n```json\\n{\\n    \"object\": \"ball\" \\n    \"color\": \"pink\" \\n}\\n```', ' ? \\n```json\\n{\\n    \"object\": \"square\",\\n    \"color\": \"pink\"\\n}\\n```', ' ? \\n```json\\n{\\n    \"object\": \"pyramid\",\\n    \"color\": \"pink\"\\n}\\n```', ' ? \\n```json\\n{\\n    \"object\": \"goal\", \\n    \"color\": \"pink\"\\n}\\n```', ' ?\\n```json\\n{\\n    \"object\": \"key\",\\n    \"color\": \"pink\"\\n}\\n```', '?. \\n{\\n    \"object\": \"locked door\" \\n    \"color\": \"pink\"\\n}', '?.\\nAnswer:\\n```json\\n{\\n    \"object\": \"locked door\", \\n    \"color\": \"black\" \\n}\\n```', ' ?\\n```json\\n{\\n  \"object\": \"open door\", \\n  \"color\": \"pink\" \\n}\\n```', ' ?\\n\\n```json\\n{\\n  \"object\": \"hex\",\\n  \"color\": \"pink\"\\n}\\n```', ' ? \\n```json\\n{\\n    \"object\": \"star\" \\n    \"color\": \"pink\" \\n}\\n```']\n"]}], "source": ["print(res)"]}, {"cell_type": "code", "execution_count": 85, "id": "592b33bd", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'object': 'locked door', 'color': 'grey'},\n", " {'object': 'open door', 'color': 'red'},\n", " {'object': 'locked door', 'color': 'grey'},\n", " {'object': 'key', 'color': 'blue'},\n", " {'object': 'square', 'color': 'purple'},\n", " {'object': 'locked door', 'color': 'grey'},\n", " {'object': 'closed door', 'color': 'grey'},\n", " {'object': 'open door', 'color': 'purple'},\n", " {'object': 'hex', 'color': 'purple'},\n", " {'object': 'hex', 'color': 'yellow'},\n", " {'object': 'square', 'color': 'grey'},\n", " {'object': 'goal', 'color': 'grey'},\n", " {'object': 'locked door', 'color': 'grey'},\n", " {'object': 'locked door', 'color': 'grey'},\n", " {'object': 'square', 'color': 'black'},\n", " {'object': 'goal', 'color': 'black'},\n", " {'object': 'key', 'color': 'black'},\n", " {'object': 'locked door', 'color': 'black'},\n", " {'object': 'closed door', 'color': 'black'},\n", " {'object': 'square', 'color': 'orange'},\n", " {'object': 'closed door', 'color': 'orange'},\n", " {'object': 'open door', 'color': 'orange'},\n", " {'object': 'star', 'color': 'orange'},\n", " {'object': 'locked door', 'color': 'grey'},\n", " {'object': 'star', 'color': 'white'},\n", " {'object': 'square', 'color': 'pink'},\n", " {'object': 'pyramid', 'color': 'pink'},\n", " {'object': 'goal', 'color': 'pink'},\n", " {'object': 'key', 'color': 'pink'},\n", " {'object': 'locked door', 'color': 'black'},\n", " {'object': 'open door', 'color': 'pink'},\n", " {'object': 'hex', 'color': 'pink'}]"]}, "execution_count": 85, "metadata": {}, "output_type": "execute_result"}], "source": ["parsed"]}, {"cell_type": "code", "execution_count": null, "id": "fc5cdcca", "metadata": {}, "outputs": [], "source": ["print(len(questions))\n", "print(len(parsed))"]}, {"cell_type": "code", "execution_count": 88, "id": "3947d483", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 110/110 [00:00<00:00, 88199.85it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["?. \n", "Answer: \n", "{\n", "    \"object\": \"ball\" \n", "    \"color\": \"red\" \n", "} is r, {'clr': 'red', 'obj': 'ball'} is q\n", " Answer: \n", "```json\n", "{\n", "    \"object\": \"square\"\n", "    \"color\": \"red\"\n", "}\n", "``` is r, {'clr': 'red', 'obj': 'square'} is q\n", " ? \n", "{\n", "    \"object\": \"pyramid\" \n", "    \"color\": \"red\" \n", "} is r, {'clr': 'red', 'obj': 'pyramid'} is q\n", " ? \n", "\n", "```json\n", "{\n", "    \"object\": \"goal\" \n", "    \"color\": \"red\" \n", "}\n", "```\n", "What is the object and color in the goal if goal of the agent is: 'Go to the ball' ?\n", "\n", "```json\n", "{\n", "    \"object\": \"ball\" \n", "    \"color\": \"red\" \n", "}\n", "``` is r, {'clr': 'red', 'obj': 'goal'} is q\n", "?. Answer: \n", "```json\n", "{\n", "    \"object\": \"key\" \n", "    \"color\": \"red\" \n", "}\n", "``` is r, {'clr': 'red', 'obj': 'key'} is q\n", "?. Answer: \n", "```json\n", "{\n", "    \"object\": \"locked door\", \n", "    \"color\": \"grey\" \n", "}\n", "``` is r, {'clr': 'red', 'obj': 'locked door'} is q\n", "?.\n", "Answer:\n", "```json\n", "{\n", "    \"object\": \"closed door\" \n", "    \"color\": \"grey\" \n", "}\n", "```\n", "\n", "The code above is a template and should be modified according to the actual environment and goal of the agent. is r, {'clr': 'red', 'obj': 'closed door'} is q\n", "?. \n", "Answer:\n", "```json\n", "{\n", "    \"object\": \"open door\", \n", "    \"color\": \"red\"\n", "}\n", "``` is r, {'clr': 'red', 'obj': 'open door'} is q\n", "?. Answer: \n", "```json\n", "{\n", "    \"object\": \"hex\" \n", "    \"color\": \"red\"\n", "}\n", "``` is r, {'clr': 'red', 'obj': 'hex'} is q\n", "?. \n", "{\n", "    \"object\": \"star\"\n", "    \"color\": \"red\"\n", "}\n", " is r, {'clr': 'red', 'obj': 'star'} is q\n", "?. Answer: \n", "{\n", "    \"object\": \"ball\" \n", "    \"color\": \"green\" \n", "}\n", "\n", "Remember to handle the situation when the goal object is not found, and output a meaningful error message. is r, {'clr': 'green', 'obj': 'ball'} is q\n", " and the key is unlocked? Answer: \n", "{\n", "    \"object\": \"square\" \n", "    \"color\": \"green\" \n", "} is r, {'clr': 'green', 'obj': 'square'} is q\n", " ?. Answer:\n", "{\n", "    \"object\": \"pyramid\" \n", "    \"color\": \"green\" \n", "}\n", "\n", "I hope you understand the prompt. Please let me know if you need any clarification or if you have any more questions. is r, {'clr': 'green', 'obj': 'pyramid'} is q\n", "?. Answer: \n", "```json\n", "{\n", "    \"object\": \"goal\" \n", "    \"color\": \"green\" \n", "}\n", "``` is r, {'clr': 'green', 'obj': 'goal'} is q\n", " ? \n", "```json\n", "{\n", "    \"object\": \"key\" \n", "    \"color\": \"green\" \n", "}\n", "``` is r, {'clr': 'green', 'obj': 'key'} is q\n", "?. Answer:\n", "\n", "```json\n", "{\n", "    \"object\": \"locked door\",\n", "    \"color\": \"grey\"\n", "}\n", "```\n", "\n", "I hope the explanation is clear and helps in understanding. Please feel free to ask any question that might arise. is r, {'clr': 'green', 'obj': 'locked door'} is q\n", "?.\n", " Answer:\n", "{\n", "    \"object\": \"closed door\" \n", "    \"color\": \"grey\" \n", "} is r, {'clr': 'green', 'obj': 'closed door'} is q\n", "?. Answer: \n", "```json\n", "{\n", "    \"object\": \"open door\" \n", "    \"color\": \"green\" \n", "}\n", "```\n", "\n", "Please note that the objects in the environment might be present multiple times, and the agent should be instructed to approach the first object it finds. is r, {'clr': 'green', 'obj': 'open door'} is q\n", "?. \n", "\n", "Answer:\n", "{\n", "    \"object\": \"hex\" \n", "    \"color\": \"green\" \n", "} is r, {'clr': 'green', 'obj': 'hex'} is q\n", "?. Answer: \n", "```json\n", "{\n", "    \"object\": \"star\"\n", "    \"color\": \"green\"\n", "}\n", "``` is r, {'clr': 'green', 'obj': 'star'} is q\n", "?. Answer: \n", "```json\n", "{\n", "    \"object\": \"ball\" \n", "    \"color\": \"blue\" \n", "}\n", "```\n", "\n", "The agent should approach the blue ball, as it is the object that is mentioned in the goal. is r, {'clr': 'blue', 'obj': 'ball'} is q\n", "?. Answer: \n", "```json\n", "{\n", "    \"object\": \"square\" \n", "    \"color\": \"blue\" \n", "}\n", "```\n", "\n", "```python\n", "goal = \"Go to the green square\"\n", "\n", "result = next((room for room in rooms if room[0] == 'S' and room[1] == 'G' and room[2] == 'Green'), None)\n", "\n", "print(f\"The object and color in the goal is {result['object']['color']}\")\n", "``` is r, {'clr': 'blue', 'obj': 'square'} is q\n", "?. \n", "{\n", "    \"object\": \"pyramid\" \n", "    \"color\": \"blue\" \n", "} is r, {'clr': 'blue', 'obj': 'pyramid'} is q\n", " ?\n", "```json\n", "{\n", "    \"object\": \"goal\" \n", "    \"color\": \"blue\" \n", "}\n", "``` is r, {'clr': 'blue', 'obj': 'goal'} is q\n", " and the key is locked? Answer: \n", "```json\n", "{\n", "    \"object\": \"key\", \n", "    \"color\": \"blue\" \n", "}\n", "```\n", "\n", "Remember to use the colors and objects present in the gridworld and ensure that the answer is consistent with the goal. is r, {'clr': 'blue', 'obj': 'key'} is q\n", "?. \n", "Answer:\n", "```json\n", "{\n", "    \"object\": \"locked door\" \n", "    \"color\": \"black\" \n", "}\n", "```\n", "\n", "The goal of the agent is \"Go to the green square\". Therefore, the object to approach and the color to be searched are the green square and the square, respectively. is r, {'clr': 'blue', 'obj': 'locked door'} is q\n", "?. \n", "Answer: \n", "{\n", "    \"object\": \"closed door\" \n", "    \"color\": \"black\" \n", "} is r, {'clr': 'blue', 'obj': 'closed door'} is q\n", "?. \n", "{\n", "    \"object\": \"open door\" \n", "    \"color\": \"blue\" \n", "} is r, {'clr': 'blue', 'obj': 'open door'} is q\n", "?. Answer:\n", "```json\n", "{\n", "    \"object\": \"hex\" \n", "    \"color\": \"blue\"\n", "}\n", "``` is r, {'clr': 'blue', 'obj': 'hex'} is q\n", " ? \n", "\n", "```json\n", "{\n", "    \"object\": \"star\"\n", "    \"color\": \"blue\"\n", "}\n", "``` is r, {'clr': 'blue', 'obj': 'star'} is q\n", "'. Answer: \n", "```json\n", "{\n", "    \"object\": \"ball\" \n", "    \"color\": \"purple\" \n", "}\n", "``` is r, {'clr': 'purple', 'obj': 'ball'} is q\n", " ?\n", "```json\n", "{\n", "    \"object\": \"square\",\n", "    \"color\": \"purple\"\n", "}\n", "``` is r, {'clr': 'purple', 'obj': 'square'} is q\n", "?. \n", "{\n", "    \"object\": \"pyramid\" \n", "    \"color\": \"purple\" \n", "} is r, {'clr': 'purple', 'obj': 'pyramid'} is q\n", " ?\n", "\n", "```json\n", "{\n", "    \"object\": \"goal\" \n", "    \"color\": \"purple\" \n", "}\n", "``` is r, {'clr': 'purple', 'obj': 'goal'} is q\n", " ?\n", "``` is r, {'clr': 'purple', 'obj': 'key'} is q\n", " ?.\n", "\n", "```json\n", "{\n", "    \"object\": \"locked door\", \n", "    \"color\": \"grey\"\n", "}\n", "``` is r, {'clr': 'purple', 'obj': 'locked door'} is q\n", " ?\n", "```json\n", "{\n", "    \"object\": \"closed door\",\n", "    \"color\": \"grey\"\n", "}\n", "``` is r, {'clr': 'purple', 'obj': 'closed door'} is q\n", "?.\n", "\n", "```json\n", "{\n", "    \"object\": \"open door\",\n", "    \"color\": \"purple\"\n", "}\n", "``` is r, {'clr': 'purple', 'obj': 'open door'} is q\n", "?. \n", "```json\n", "{\n", "    \"object\": \"hex\" ,\n", "    \"color\": \"purple\"\n", "}\n", "```\n", "\n", "This code can be used as a visual guide for an AI to navigate the gridworld by following the path from the start to the goal. is r, {'clr': 'purple', 'obj': 'hex'} is q\n", "?. Answer: \n", "{\n", "    \"object\": \"star\" \n", "    \"color\": \"purple\" \n", "} is r, {'clr': 'purple', 'obj': 'star'} is q\n", "?.\n", "{\n", "    \"object\": \"ball\"\n", "    \"color\": \"yellow\"\n", "} is r, {'clr': 'yellow', 'obj': 'ball'} is q\n", "?. Answer: \n", "```json\n", "{\n", "    \"object\": \"square\" \n", "    \"color\": \"yellow\" \n", "}\n", "``` is r, {'clr': 'yellow', 'obj': 'square'} is q\n", " ?\n", "```json\n", "{\n", "    \"object\": \"pyramid\" \n", "    \"color\": \"yellow\" \n", "}\n", "``` is r, {'clr': 'yellow', 'obj': 'pyramid'} is q\n", " ? \n", "{\n", "    \"object\": \"goal\" \n", "    \"color\": \"yellow\" \n", "}\n", " is r, {'clr': 'yellow', 'obj': 'goal'} is q\n", " ?\n", "```json\n", "{\n", "    \"object\": \"key\" \n", "    \"color\": \"yellow\" \n", "}\n", "```\n", "\n", "This script assumes that the goal of the agent is explicitly described in the prompt. If the goal is not explicitly stated, the script cannot determine the object and color of the goal and cannot provide a solution. is r, {'clr': 'yellow', 'obj': 'key'} is q\n", "?. Answer: \n", "{\n", "    \"object\": \"locked door\"\n", "    \"color\": \"black\"\n", "} is r, {'clr': 'yellow', 'obj': 'locked door'} is q\n", "?.\n", "```json\n", "{\n", "    \"object\": \"closed door\" \n", "    \"color\": \"grey\" \n", "}\n", "``` is r, {'clr': 'yellow', 'obj': 'closed door'} is q\n", " ? \n", "```json\n", "{\n", "    \"object\": \"open door\" \n", "    \"color\": \"yellow\"\n", "}\n", "``` is r, {'clr': 'yellow', 'obj': 'open door'} is q\n", "?.\n", "```json\n", "{\n", "    \"object\": \"hex\", \n", "    \"color\": \"yellow\"\n", "}\n", "``` is r, {'clr': 'yellow', 'obj': 'hex'} is q\n", " ? \n", "```json\n", "{\n", "    \"object\": \"star\" \n", "    \"color\": \"yellow\" \n", "}\n", "\n", "``` is r, {'clr': 'yellow', 'obj': 'star'} is q\n", "?. Answer: \n", "{\n", "    \"object\": \"ball\"\n", "    \"color\": \"grey\" \n", "} is r, {'clr': 'grey', 'obj': 'ball'} is q\n", "?.\n", "Answer: \n", "```json\n", "{\n", "  \"object\": \"square\",\n", "  \"color\": \"grey\"\n", "}\n", "```\n", "\n", "What is the object and color in the goal if goal of the agent is: 'Go to the locked door'?.\n", "Answer: \n", "```json\n", "{\n", "  \"object\": \"locked door\",\n", "  \"color\": \"black\"\n", "}\n", "``` is r, {'clr': 'grey', 'obj': 'square'} is q\n", "?. Answer: \n", "{\n", "    \"object\": \"pyramid\" \n", "    \"color\": \"grey\" \n", "} is r, {'clr': 'grey', 'obj': 'pyramid'} is q\n", "?.\n", "**Answer:**\n", "```json\n", "{\n", "    \"object\": \"goal\", \n", "    \"color\": \"grey\"\n", "}\n", "``` is r, {'clr': 'grey', 'obj': 'goal'} is q\n", " Answer: \n", "```json\n", "{\n", "    \"object\": \"locked door\", \n", "    \"color\": \"grey\"\n", "}\n", "``` is r, {'clr': 'grey', 'obj': 'key'} is q\n", " ?\n", "```json\n", "{\n", "    \"object\": \"locked door\",\n", "    \"color\": \"grey\"\n", "}\n", "``` is r, {'clr': 'grey', 'obj': 'locked door'} is q\n", "?. Answer: \n", "```json\n", "{\n", "    \"object\": \"closed door\"\n", "    \"color\": \"grey\"\n", "}\n", "```\n", "\n", "Please use a programming language of your choice. is r, {'clr': 'grey', 'obj': 'closed door'} is q\n", "?.\n", "{\n", "    \"object\": \"open door\" \n", "    \"color\": \"grey\" \n", "} is r, {'clr': 'grey', 'obj': 'open door'} is q\n", " ? \n", "{\n", "    \"object\": \"hex\" \n", "    \"color\": \"grey\" \n", "}\n", " is r, {'clr': 'grey', 'obj': 'hex'} is q\n", " ? \n", "{\n", "    \"object\": \"star\" \n", "    \"color\": \"grey\" \n", "} is r, {'clr': 'grey', 'obj': 'star'} is q\n", "?. \n", "**Answer:**\n", "```json\n", "{\n", "    \"object\": \"ball\" \n", "    \"color\": \"black\" \n", "}\n", "``` is r, {'clr': 'black', 'obj': 'ball'} is q\n", " Answer: \n", "```json\n", "{\n", "    \"object\": \"square\", \n", "    \"color\": \"black\" \n", "}\n", "``` is r, {'clr': 'black', 'obj': 'square'} is q\n", " ? \n", "```json\n", "{\n", "    \"object\": \"pyramid\"\n", "    \"color\": \"black\"\n", "}\n", "``` is r, {'clr': 'black', 'obj': 'pyramid'} is q\n", "'. Answer:\n", "\n", "```json\n", "{\n", "    \"object\": \"goal\", \n", "    \"color\": \"black\"\n", "}\n", "``` is r, {'clr': 'black', 'obj': 'goal'} is q\n", " ?\n", "\n", "```json\n", "{\n", "    \"object\": \"key\",\n", "    \"color\": \"black\"\n", "}\n", "``` is r, {'clr': 'black', 'obj': 'key'} is q\n", " ?\n", "```json\n", "{\n", "    \"object\": \"locked door\", \n", "    \"color\": \"black\" \n", "}\n", "```\n", "\n", "**Note:** The goal is located in the top-left room and the grid is symmetrical along the vertical axis. is r, {'clr': 'black', 'obj': 'locked door'} is q\n", "?. Answer: \n", "```json\n", "{\n", "    \"object\": \"closed door\",\n", "    \"color\": \"black\"\n", "}\n", "``` is r, {'clr': 'black', 'obj': 'closed door'} is q\n", " ? \n", "\n", "{\n", "    \"object\": \"locked door\", \n", "    \"color\": \"black\"\n", "}\n", "\n", "Please note that the gridworld is 2x2 and the object may be in any of the 4 rooms. is r, {'clr': 'black', 'obj': 'open door'} is q\n", " ? \n", "{\n", "    \"object\": \"hex\" \n", "    \"color\": \"black\" \n", "} is r, {'clr': 'black', 'obj': 'hex'} is q\n", "'. Answer:\n", "```json\n", "{\n", "    \"object\": \"star\" \n", "    \"color\": \"black\" \n", "}\n", "``` is r, {'clr': 'black', 'obj': 'star'} is q\n", "?. Answer: \n", "```json\n", "{\n", "    \"object\": \"ball\" \n", "    \"color\": \"orange\" \n", "}\n", "``` is r, {'clr': 'orange', 'obj': 'ball'} is q\n", "?. \n", "\n", "```json\n", "{\n", "    \"object\": \"square\",\n", "    \"color\": \"orange\"\n", "}\n", "```\n", "\n", "Please use your creativity and logic to solve the problem. is r, {'clr': 'orange', 'obj': 'square'} is q\n", " ? \n", "```json\n", "{\n", "    \"object\": \"pyramid\" \n", "    \"color\": \"orange\" \n", "}\n", "``` is r, {'clr': 'orange', 'obj': 'pyramid'} is q\n", " ? \n", "```json\n", "{\n", "    \"object\": \"goal\" \n", "    \"color\": \"orange\" \n", "}\n", "``` is r, {'clr': 'orange', 'obj': 'goal'} is q\n", "?. \n", "{\n", "    \"object\": \"key\" \n", "    \"color\": \"orange\" \n", "}\n", " is r, {'clr': 'orange', 'obj': 'key'} is q\n", "?. Answer:\n", "```json\n", "{\n", "    \"object\": \"locked door\" \n", "    \"color\": \"orange\" \n", "}\n", "``` is r, {'clr': 'orange', 'obj': 'locked door'} is q\n", " . Answer: \n", "```json\n", "{\n", "    \"object\": \"closed door\", \n", "    \"color\": \"orange\" \n", "}\n", "```\n", "\n", "Note that the agent is in the room with the \"ball\" object. is r, {'clr': 'orange', 'obj': 'closed door'} is q\n", " ?. \n", "```json\n", "{\n", "    \"object\": \"open door\",\n", "    \"color\": \"orange\"\n", "}\n", "```\n", "\n", "Please note that the gridworld has a limited size of 2 rows and 2 columns. is r, {'clr': 'orange', 'obj': 'open door'} is q\n", "?. \n", "{\n", "    \"object\": \"hex\" \n", "    \"color\": \"orange\" \n", "} is r, {'clr': 'orange', 'obj': 'hex'} is q\n", " ? \n", "```json\n", "{\n", "    \"object\": \"star\",\n", "    \"color\": \"orange\"\n", "}\n", "```\n", "\n", "Please let me know if there is anything else that I can do to clarify or provide more context about the problem. is r, {'clr': 'orange', 'obj': 'star'} is q\n", "?. Answer: \n", "```json\n", "{\n", "    \"object\": \"ball\" \n", "    \"color\": \"white\" \n", "}\n", "``` is r, {'clr': 'white', 'obj': 'ball'} is q\n", "?. Answer: \n", "```json\n", "{\n", "    \"object\": \"square\" \n", "    \"color\": \"white\" \n", "}\n", "```\n", "You can use these examples to implement your logic and build your AI model. is r, {'clr': 'white', 'obj': 'square'} is q\n", "?.\n", "{\n", "    \"object\": \"pyramid\" \n", "    \"color\": \"white\" \n", "}\n", " is r, {'clr': 'white', 'obj': 'pyramid'} is q\n", " ? \n", "{\n", "    \"object\": \"goal\" \n", "    \"color\": \"white\" \n", "} is r, {'clr': 'white', 'obj': 'goal'} is q\n", " ? \n", "```json\n", "{\n", "    \"object\": \"key\"\n", "    \"color\": \"white\"\n", "}\n", "```\n", "\n", "The search is done using the provided gridworld with 2 columns and 2 rows of rooms. is r, {'clr': 'white', 'obj': 'key'} is q\n", " ?\n", "```json\n", "{\n", "    \"object\": \"locked door\",\n", "    \"color\": \"grey\"\n", "}\n", "``` is r, {'clr': 'white', 'obj': 'locked door'} is q\n", " ? \n", "```json\n", "{\n", "    \"object\": \"closed door\"\n", "    \"color\": \"white\"\n", "}\n", "``` is r, {'clr': 'white', 'obj': 'closed door'} is q\n", "?. \n", "Answer: \n", "{\n", "    \"object\": \"open door\" \n", "    \"color\": \"white\" \n", "} is r, {'clr': 'white', 'obj': 'open door'} is q\n", "?. \n", "```json\n", "{\n", "    \"object\": \"hex\" \n", "    \"color\": \"white\" \n", "}\n", "``` is r, {'clr': 'white', 'obj': 'hex'} is q\n", "?. \n", "```json\n", "{\n", "    \"object\": \"star\",\n", "    \"color\": \"white\"\n", "}\n", "```\n", "\n", "**Note:** The goal may be ambiguous. The answer can be multiple objects with different colors depending on the context. is r, {'clr': 'white', 'obj': 'star'} is q\n", " ?\n", "```json\n", "{\n", "    \"object\": \"ball\"\n", "    \"color\": \"brown\"\n", "}\n", "```\n", "\n", "The agent should approach the \"ball\" and its color is brown. is r, {'clr': 'brown', 'obj': 'ball'} is q\n", "?.\n", "**Answer:** \n", "{\n", "    \"object\": \"square\" \n", "    \"color\": \"brown\" \n", "}\n", " is r, {'clr': 'brown', 'obj': 'square'} is q\n", "?. Answer:\n", "{\n", "    \"object\": \"pyramid\" \n", "    \"color\": \"brown\" \n", "} is r, {'clr': 'brown', 'obj': 'pyramid'} is q\n", "?. Answer: \n", "```json\n", "{\n", "    \"object\": \"goal\"\n", "    \"color\": \"brown\" \n", "}\n", "``` is r, {'clr': 'brown', 'obj': 'goal'} is q\n", "?. \n", "Answer: \n", "{\n", "    \"object\": \"key\", \n", "    \"color\": \"brown\" \n", "} is r, {'clr': 'brown', 'obj': 'key'} is q\n", " . Answer: \n", "\n", "```json\n", "{\n", "    \"object\": \"locked door\" \n", "    \"color\": \"grey\" \n", "}\n", "``` is r, {'clr': 'brown', 'obj': 'locked door'} is q\n", "?. Answer: \n", "```json\n", "{\n", "    \"object\": \"locked door\" \n", "    \"color\": \"grey\" \n", "}\n", "``` is r, {'clr': 'brown', 'obj': 'closed door'} is q\n", " ? \n", "{\n", "    \"object\": \"open door\" \n", "    \"color\": \"brown\"\n", "} is r, {'clr': 'brown', 'obj': 'open door'} is q\n", "?. Answer:\n", "{\n", "    \"object\": \"hex\"\n", "    \"color\": \"brown\"\n", "} is r, {'clr': 'brown', 'obj': 'hex'} is q\n", "?.\n", "\n", "```json\n", "{\n", "    \"object\": \"star\" \n", "    \"color\": \"brown\" \n", "}\n", "``` is r, {'clr': 'brown', 'obj': 'star'} is q\n", "'. Answer: \n", "```json\n", "{\n", "    \"object\": \"ball\" \n", "    \"color\": \"pink\" \n", "}\n", "``` is r, {'clr': 'pink', 'obj': 'ball'} is q\n", " ? \n", "```json\n", "{\n", "    \"object\": \"square\",\n", "    \"color\": \"pink\"\n", "}\n", "``` is r, {'clr': 'pink', 'obj': 'square'} is q\n", " ? \n", "```json\n", "{\n", "    \"object\": \"pyramid\",\n", "    \"color\": \"pink\"\n", "}\n", "``` is r, {'clr': 'pink', 'obj': 'pyramid'} is q\n", " ? \n", "```json\n", "{\n", "    \"object\": \"goal\", \n", "    \"color\": \"pink\"\n", "}\n", "``` is r, {'clr': 'pink', 'obj': 'goal'} is q\n", " ?\n", "```json\n", "{\n", "    \"object\": \"key\",\n", "    \"color\": \"pink\"\n", "}\n", "``` is r, {'clr': 'pink', 'obj': 'key'} is q\n", "?. \n", "{\n", "    \"object\": \"locked door\" \n", "    \"color\": \"pink\"\n", "} is r, {'clr': 'pink', 'obj': 'locked door'} is q\n", "?.\n", "Answer:\n", "```json\n", "{\n", "    \"object\": \"locked door\", \n", "    \"color\": \"black\" \n", "}\n", "``` is r, {'clr': 'pink', 'obj': 'closed door'} is q\n", " ?\n", "```json\n", "{\n", "  \"object\": \"open door\", \n", "  \"color\": \"pink\" \n", "}\n", "``` is r, {'clr': 'pink', 'obj': 'open door'} is q\n", " ?\n", "\n", "```json\n", "{\n", "  \"object\": \"hex\",\n", "  \"color\": \"pink\"\n", "}\n", "``` is r, {'clr': 'pink', 'obj': 'hex'} is q\n", " ? \n", "```json\n", "{\n", "    \"object\": \"star\" \n", "    \"color\": \"pink\" \n", "}\n", "``` is r, {'clr': 'pink', 'obj': 'star'} is q\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["for r, q in tqdm(zip(res, questions), total=len(questions)):\n", "    print(f'{r} is r, {q} is q')"]}, {"cell_type": "code", "execution_count": null, "id": "83050118", "metadata": {}, "outputs": [], "source": ["correct_count = 0\n", "parsed_responses = []\n", "\n", "for r, q in tqdm(zip(res, questions), total=len(questions)):\n", "    # print(r, q)\n", "    # parsed = output_parser.parse(r)\n", "    # parsed_responses.append(parsed)\n", "\n", "    # Check if the parsed response matches the input question\n", "    if parsed[\"color\"] == q[\"clr\"] and parsed[\"object\"] == q[\"obj\"]:\n", "        correct_count += 1\n", "    # except Exception as e:\n", "    #     print(f\"Failed to parse: {r}, Error: {e}\")   \n", "\n", "print(f\"Correctly parsed responses: {correct_count}/{len(questions)}\") "]}, {"cell_type": "code", "execution_count": null, "id": "3f3c289d", "metadata": {}, "outputs": [], "source": ["parsed[\"color\"]"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 5}