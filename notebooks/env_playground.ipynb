{"cells": [{"cell_type": "code", "execution_count": 1, "id": "7a7def4c-5f1f-4798-8c82-702a6d516fe1", "metadata": {}, "outputs": [], "source": ["import os\n", "# os.environ['JAX_PLATFORM_NAME'] = 'cpu'"]}, {"cell_type": "code", "execution_count": 2, "id": "a270d535-9421-4eea-a8db-372fd6ab3dc8", "metadata": {}, "outputs": [], "source": ["import jax\n", "from jax import numpy as jnp"]}, {"cell_type": "code", "execution_count": 3, "id": "07824f3c-4bb5-47cf-a72c-fd4ed97f2166", "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.insert(0, 'src')"]}, {"cell_type": "code", "execution_count": 19, "id": "cbe504e0-b3af-464b-849f-e72aceb7e3ff", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Matplotlib is building the font cache; this may take a moment.\n"]}], "source": ["import imageio\n", "import jax\n", "import jax.numpy as jnp\n", "import jmp\n", "import orbax.checkpoint\n", "import xminigrid\n", "# from nn import ActorCriticRNN\n", "from xminigrid.rendering.text_render import print_ruleset\n", "from xminigrid.wrappers import GymAutoResetWrapper\n", "\n", "# utils for the demonstation\n", "from xminigrid.core.grid import room\n", "from xminigrid.types import AgentState\n", "from xminigrid.core.actions import take_action\n", "from xminigrid.core.constants import Tiles, Colors, TILES_REGISTRY\n", "from xminigrid.rendering.rgb_render import render\n", "\n", "# rules and goals\n", "from xminigrid.core.goals import check_goal, AgentNearGoal\n", "from xminigrid.core.rules import check_rule, AgentNearRule\n", "import timeit\n", "import imageio\n", "import matplotlib.pyplot as plt\n", "from tqdm.auto import trange, tqdm\n", "\n", "def show_img(img, dpi=32):\n", "    plt.figure(dpi=dpi)\n", "    plt.axis('off')\n", "    plt.imshow(img)\n"]}, {"cell_type": "code", "execution_count": 7, "id": "190bc8c6-3f6c-40e6-a3d9-0f2890486494", "metadata": {}, "outputs": [], "source": ["# orbax_checkpointer = orbax.checkpoint.PyTreeCheckpointer()\n", "# checkpoint = orbax_checkpointer.restore(\"/network/scratch/a/artem.zholus/language_policies/1/checkpoint_0\")\n", "# config = checkpoint[\"config\"]\n", "# params = checkpoint['params']\n", "\n", "env, env_params = xminigrid.make(\"XLand-MiniGrid-R9-25x25\")\n", "env = GymAutoResetWrapper(env)"]}, {"cell_type": "code", "execution_count": 8, "id": "196a3940-d8a8-4767-8cb2-d2ba99580174", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading benchmark data: https://huggingface.co/datasets/Howuhh/xland_minigrid/resolve/main/high_3m_v2 to /home/<USER>/m/maryam.hashemzadeh/.xland_minigrid\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Progress: 94.9MB [00:00, 143MB/s]                             \n"]}], "source": ["import jax.random\n", "import xminigrid\n", "from xminigrid.benchmarks import Benchmark\n", "benchmark: Benchmark = xminigrid.load_benchmark(name=\"high-3m\")"]}, {"cell_type": "code", "execution_count": 9, "id": "715125a5-246a-40d0-9cea-959400e031d6", "metadata": {}, "outputs": [], "source": ["def build_rollout(env, env_params, num_steps):\n", "    def rollout(rng):\n", "        def _step_fn(carry, _):\n", "            rng, timestep = carry\n", "            rng, _rng = jax.random.split(rng)\n", "            action = jax.random.randint(_rng, shape=(), minval=0, maxval=env.num_actions(env_params))\n", "            \n", "            timestep = env.step(env_params, timestep, action)\n", "            return (rng, timestep), timestep\n", "    \n", "        rng, _rng = jax.random.split(rng)\n", "    \n", "        timestep = env.reset(env_params, _rng)\n", "        rng, transitions = jax.lax.scan(_step_fn, (rng, timestep), None, length=num_steps)\n", "        return transitions\n", "\n", "    return rollout\n"]}, {"cell_type": "code", "execution_count": 20, "id": "169cd4c6-63f0-450b-8e76-8ddb593c91fb", "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.image.AxesImage at 0x7f5a20537310>"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["benchmark.sample_ruleset(jax.random.key(0))\n", "ruleset = benchmark.get_ruleset(ruleset_id=11)\n", "env_params = env_params.replace(ruleset=ruleset)\n", "timestep = env.reset(env_params, jax.random.key(0))\n", "plt.imshow(env.render(env_params, timestep))"]}, {"cell_type": "code", "execution_count": 11, "id": "80af2cbb-94ae-407b-918a-4377bb59a58d", "metadata": {}, "outputs": [{"data": {"text/plain": ["(<PERSON><PERSON><PERSON>([15, 23], dtype=int32), <PERSON><PERSON><PERSON>([ 3, 19], dtype=int32))"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["(timestep.state.grid[..., 0] == Tiles.SQUARE).nonzero()"]}, {"cell_type": "code", "execution_count": 32, "id": "1923178c", "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.image.AxesImage at 0x7f5a204907f0>"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.imshow(env.render(env_params, timestep))"]}, {"cell_type": "code", "execution_count": 41, "id": "7da6db21", "metadata": {}, "outputs": [{"data": {"text/plain": ["Array([12, 12], dtype=int32)"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["timestep.state.agent.position"]}, {"cell_type": "code", "execution_count": null, "id": "8b2d4d76", "metadata": {}, "outputs": [], "source": ["timestep.state\n", "x // 8, y // 8"]}, {"cell_type": "code", "execution_count": 40, "id": "f90b5d70", "metadata": {}, "outputs": [{"data": {"text/plain": ["3"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["25//8"]}, {"cell_type": "code", "execution_count": null, "id": "0df61c98", "metadata": {}, "outputs": [], "source": ["timestep.state.grid[..., 0]\n", "(timestep.state.grid[..., 0] == Tiles.SQUARE) "]}, {"cell_type": "code", "execution_count": 17, "id": "15547969", "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "cannot unpack non-iterable int object", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[17], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m shape_ \u001b[38;5;241m=\u001b[39m timestep\u001b[38;5;241m.\u001b[39mstate\u001b[38;5;241m.\u001b[39mgrid\u001b[38;5;241m.\u001b[39mshape\n\u001b[0;32m----> 2\u001b[0m num_cols\u001b[38;5;241m=\u001b[39m shape_[\u001b[38;5;241m0\u001b[39m], num_rows \u001b[38;5;241m=\u001b[39m shape_[\u001b[38;5;241m1\u001b[39m]\n", "\u001b[0;31mTypeError\u001b[0m: cannot unpack non-iterable int object"]}], "source": ["shape_ = timestep.state.grid.shape\n", "num_cols= shape_[0], num_rows = shape_[1]"]}, {"cell_type": "code", "execution_count": 75, "id": "a6dd8648", "metadata": {}, "outputs": [{"data": {"text/plain": ["(Array([], shape=(0,), dtype=int32), Array([], shape=(0,), dtype=int32))"]}, "execution_count": 75, "metadata": {}, "output_type": "execute_result"}], "source": ["# ((timestep.state.grid[..., 0] == Tiles.DOOR_LOCKED) && (timestep.state.grid[..., 1] == Colors.BLUE)).nonzero()\n", "((timestep.state.grid[..., 1] == Tiles.DOOR_LOCKED)).nonzero()"]}, {"cell_type": "code", "execution_count": 68, "id": "5106336b", "metadata": {}, "outputs": [], "source": ["tiles_type = {\n", "    # 'EMPTY': 0,\n", "    # 'END_OF_MAP': 1,\n", "    # 'UNSEEN': 2,\n", "    # 'FLOOR': 3,\n", "    # 'WALL': 4,\n", "    'BALL': 5,\n", "    'SQUARE': 6,\n", "    'PYRAMID': 7,\n", "    'GOAL': 8,\n", "    'KEY': 9,\n", "    'DOOR_LOCKED': 10,\n", "    'DOOR_CLOSED': 11,\n", "    'DOOR_OPEN': 12,\n", "    'HEX': 13,\n", "    'STAR': 14}\n", "\n", "colors_type = {\n", "    # 'EMPTY': 0,\n", "    # 'END_OF_MAP': 1,\n", "    # 'UNSEEN': 2,\n", "    'RED': 3,\n", "    'GREEN': 4,\n", "    'BLUE': 5,\n", "    'PURPLE': 6,\n", "    'YELLOW': 7,\n", "    'GREY': 8,\n", "    'BLACK': 9,\n", "    'ORANGE': 10,\n", "    'WHITE': 11,\n", "    'BROWN': 12,\n", "    'PINK': 13}"]}, {"cell_type": "code", "execution_count": 69, "id": "eb50449a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["BALL\n", "SQUARE\n", "PYRAMID\n", "GOAL\n", "KEY\n", "DOOR_LOCKED\n", "DOOR_CLOSED\n", "DOOR_OPEN\n", "HEX\n", "STAR\n"]}], "source": ["for x in tiles_type.keys():\n", "    print(x)"]}, {"cell_type": "code", "execution_count": 72, "id": "cae6bd7d", "metadata": {}, "outputs": [], "source": ["objects = 'Here is the objects of each room. '\n", "\n", "for obj in tiles_type.keys():\n", "    for color in colors_type.keys():\n", "        items = ((timestep.state.grid[..., 0] == tiles_type[obj]) & (timestep.state.grid[..., 1] == colors_type[color])).nonzero()\n", "        # print(items)\n", "        for i in range(len(items[0])):\n", "            objects += f' a {color} {obj} at {items[0][i]} and {items[1][i]} coordination in room at column {items[0][i]//8} and row {items[1][i]//8}, '\n"]}, {"cell_type": "code", "execution_count": 73, "id": "63bdc49e", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Here is the objects of each room.  a BLUE BALL at 3 and 19 cordination in room at column 0 and row 2,  a ORANGE SQUARE at 11 and 14 cordination in room at column 1 and row 1,  a GREEN PYRAMID at 14 and 22 cordination in room at column 1 and row 2,  a RED KEY at 9 and 8 cordination in room at column 1 and row 1,  a RED KEY at 12 and 16 cordination in room at column 1 and row 2,  a GREEN KEY at 5 and 8 cordination in room at column 0 and row 1,  a BLUE KEY at 16 and 2 cordination in room at column 2 and row 0,  a PURPLE KEY at 8 and 17 cordination in room at column 1 and row 2,  a PURPLE KEY at 16 and 22 cordination in room at column 2 and row 2,  a PURPLE KEY at 22 and 16 cordination in room at column 2 and row 2, '"]}, "execution_count": 73, "metadata": {}, "output_type": "execute_result"}], "source": ["objects"]}, {"cell_type": "code", "execution_count": null, "id": "73c599c7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2a6849a2", "metadata": {}, "outputs": [], "source": ["### for the new env?\n", "def describe(timestep, env_params):\n", "    shape_ = timestep.state.grid.shape\n", "    num_cols= shape_[0]//8\n", "    num_rows = shape_[1]//8\n", "    number_rooms= env_params.grid_type\n", "    room_w= 8\n", "    room_h= 8 #self.room_grid[0][0].size\n", "    player_room_x, player_room_y = timestep.state.agent.position\n", "    player_room_x, player_x = player_room_x //room_w , player_room_x % room_w\n", "    player_room_y, player_y = player_room_y //room_h , player_room_y % room_h\n", "\n", "\n", "    objects = 'Here is the objects of each room. '\n", "\n", "    for obj in tiles_type.keys():\n", "        for color in colors_type.keys():\n", "            items = ((timestep.state.grid[..., 0] == tiles_type[obj]) & (timestep.state.grid[..., 1] == colors_type[color])).nonzero()\n", "            # print(items)\n", "            for i in range(len(items[0])):\n", "                objects += f' a {color} {obj} at {items[0][i]} and {items[1][i]} coordination in room at column {items[0][i]//8} and row {items[1][i]//8}, '\n", "    \n", "    return (f\"You are in the {num_cols * num_rows} rooms flat\" +\n", "            f\" gridworld with {num_cols} column and {num_rows} rows of rooms.\" +\n", "            f\"You are located in the room with at column {player_room_x} and row {player_room_y}. \" +\n", "            f\"Your position within that room is ({player_x}, {player_y}).\"\n", "        \" Adjacent rooms are connected with doors. \" +\n", "        \"Each room is {} cells wide and {} cells high. \".format(room_w, room_h) +\n", "        objects)"]}, {"cell_type": "code", "execution_count": 10, "id": "a6141a39-e8b2-4e97-b62a-bd3eefb2349c", "metadata": {}, "outputs": [], "source": ["def randomly_lock_doors(rng, grid):\n", "    door_mask = grid[..., 0] == Tiles.DOOR_CLOSED\n", "    key_mask = grid[..., 0] == Tiles.KEY\n", "    for color in [Colors.RED,\n", "              Colors.GREEN,\n", "              Colors.BLUE,\n", "              Colors.PURPLE,\n", "              Colors.YELLOW,\n", "              Colors.GREY,\n", "              Colors.BLACK,\n", "              Colors.ORANGE,\n", "              Colors.WHITE,\n", "              Colors.BROWN,\n", "              Colors.PINK]:\n", "        color_mask = grid[..., 1] == color\n", "        has_key_for_door = (color_mask & key_mask).any() & (color_mask & door_mask)\n", "        # randomly close doors\n", "        rng, curr_rng = jax.random.split(rng, 2)\n", "        # uniformly choosing\n", "        random_mask = jax.random.randint(curr_rng, timestep.state.grid[..., 0].shape, 0, 2) == 1\n", "        random_locked_closed = jnp.where(random_mask, Tiles.DOOR_LOCKED, Tiles.DOOR_CLOSED)\n", "        # if we have a certain key of a certain color - we randomly decide to close the door or leave it open. \n", "        # otherwise we leave it open\n", "        new_grid = jnp.where(has_key_for_door, random_locked_closed, grid[..., 0])\n", "        grid = jnp.stack([new_grid, grid[..., 1]], axis=-1)\n", "    return rng, grid\n", "def add_more_keys(rng, tiles, max_num_keys=6):\n", "    key_tiles = tiles.at[:, 0].set(Tiles.KEY)\n", "    rng, curr_key = jax.random.split(rng, 2)\n", "    key_colors = key_tiles.at[:, 1].set(jax.random.randint(curr_key, (key_tiles.shape[0],), 1, 12)) # colors are ints\n", "    tiles_with_keys = jnp.where(tiles != 0, tiles, key_colors)\n", "    rng, curr_key = jax.random.split(rng, 2)\n", "    total = (tiles != 0).all(1).sum()\n", "    max_objs = jax.random.randint(curr_key, (1,), total, total + max_num_keys)\n", "    return rng, jnp.where((jnp.arange(tiles.shape[0]) <= max_objs)[:, None], tiles_with_keys, jnp.zeros_like(tiles))\n"]}, {"cell_type": "code", "execution_count": 61, "id": "ec640a8b-994c-426d-a3bb-8979e535dbf1", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/network/scratch/a/artem.zholus/conda_envs/lg/lib/python3.10/site-packages/jax/_src/ops/scatter.py:96: FutureWarning: scatter inputs have incompatible types: cannot safely cast value from dtype=int32 to dtype=uint8 with jax_numpy_dtype_promotion='standard'. In future JAX releases this will result in an error.\n", "  warnings.warn(\n"]}, {"data": {"text/plain": ["<matplotlib.image.AxesImage at 0x7f947027b5e0>"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ruleset = benchmark.get_ruleset(ruleset_id=14)\n", "rng = jax.random.key(8)\n", "rng, new_tiles = add_more_keys(rng, ruleset.init_tiles, max_num_keys=10)\n", "ruleset = ruleset.replace(init_tiles=new_tiles)\n", "env_params = env_params.replace(ruleset=ruleset)\n", "rng, subkey = jax.random.split(rng, 2)\n", "timestep = env.reset(env_params, subkey)\n", "rng, new_grid = randomly_lock_doors(rng, timestep.state.grid)\n", "\n", "ruleset = ruleset.replace(rules=ruleset.rules[-1:])\n", "objects = jnp.stack((timestep.state.grid[..., 0] > 4).nonzero()).T\n", "rng_, subkey = jax.random.split(rng, 2)\n", "new_goal = ruleset.goal.at[0].set(2).at[1:3].set(jax.random.choice(subkey, objects))\n", "ruleset = ruleset.replace(goal=new_goal)\n", "\n", "timestep = timestep.replace(state=timestep.state.replace(grid=new_grid))\n", "# timestep.state.grid = new_grid\n", "plt.imshow(env.render(env_params, timestep))"]}, {"cell_type": "code", "execution_count": 55, "id": "c3fd06fe-ef1d-483c-8c2f-fa58b87d0d96", "metadata": {}, "outputs": [{"data": {"text/plain": ["RuleSet(goal=Array([ 2, 15,  3,  0,  0], dtype=int32), rules=Array([[0, 0, 0, 0, 0, 0, 0]], dtype=uint8), init_tiles=Array([[11, 10],\n", "       [ 6, 10],\n", "       [ 4,  2],\n", "       [ 3,  4],\n", "       [ 7,  5],\n", "       [ 7,  5],\n", "       [12,  8],\n", "       [ 7,  7],\n", "       [ 7, 10],\n", "       [ 7,  9],\n", "       [ 7, 11],\n", "       [ 7,  5],\n", "       [ 7,  8],\n", "       [ 7,  9],\n", "       [ 7,  6],\n", "       [ 7,  1],\n", "       [ 7,  6],\n", "       [ 0,  0],\n", "       [ 0,  0],\n", "       [ 0,  0],\n", "       [ 0,  0],\n", "       [ 0,  0],\n", "       [ 0,  0]], dtype=uint8))"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["ruleset = ruleset.replace(rules=ruleset.rules[-1:])\n", "objects = jnp.stack((timestep.state.grid[..., 0] > 4).nonzero()).T\n", "rng_, subkey = jax.random.split(rng, 2)\n", "new_goal = ruleset.goal.at[0].set(2).at[1:3].set(jax.random.choice(subkey, objects))\n", "ruleset = ruleset.replace(goal=new_goal)\n", "ruleset"]}, {"cell_type": "code", "execution_count": null, "id": "4576b082-368c-46aa-adeb-2b2422c34e3c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 154, "id": "a794de55-7ce9-4844-8793-a4c57acc1635", "metadata": {}, "outputs": [], "source": ["rng_ = jax.random.key(2)"]}, {"cell_type": "code", "execution_count": 198, "id": "8db455b0-ae52-49a8-88d1-7dbd353d06ab", "metadata": {}, "outputs": [], "source": ["timestep.state.grid.shape\n", "rng = rng_\n", "door_mask = timestep.state.grid[..., 0] == Tiles.DOOR_CLOSED\n", "key_mask = timestep.state.grid[..., 0] == Tiles.KEY\n", "color = Colors.PURPLE\n", "# for color in range(12): # TODO: change this to enumeration\n", "if True:\n", "    color_mask = timestep.state.grid[..., 1] == color\n", "    has_key_for_door = (color_mask & key_mask).any() & (color_mask & door_mask)\n", "    # randomly close doors\n", "    rng, curr_rng = jax.random.split(rng, 2)\n", "    # uniformly choosing\n", "    random_mask = jax.random.randint(curr_rng, timestep.state.grid[..., 0].shape, 0, 2) == 1\n", "    random_open_closed = jnp.where(random_mask, Tiles.DOOR_LOCKED, Tiles.DOOR_CLOSED)\n", "    # if we have a certain key of a certain color - we randomly decide to close the door or leave it open. \n", "    # otherwise we leave it open\n", "    new_grid = jnp.where(has_key_for_door, random_open_closed, timestep.state.grid[..., 0])\n", "new_grid = jnp.stack([new_grid, timestep.state.grid[..., 1]], axis=-1)"]}, {"cell_type": "code", "execution_count": 19, "id": "18345960-9a4c-4287-9ab4-9861b6e74279", "metadata": {}, "outputs": [], "source": ["# utils for the demonstation\n", "from xminigrid.core.grid import room\n", "from xminigrid.types import AgentState\n", "from xminigrid.core.actions import take_action\n", "from xminigrid.core.constants import Tiles, Colors, TILES_REGISTRY\n", "from xminigrid.rendering.rgb_render import render\n", "\n", "# rules and goals\n", "from xminigrid.core.goals import check_goal, AgentNearGoal\n", "from xminigrid.core.rules import check_rule, AgentNearRule\n", "import timeit\n", "import imageio\n", "import matplotlib.pyplot as plt\n", "from tqdm.auto import trange, tqdm\n", "\n", "def show_img(img, dpi=32):\n", "    plt.figure(dpi=dpi)\n", "    plt.axis('off')\n", "    plt.imshow(img)"]}, {"cell_type": "code", "execution_count": 158, "id": "7d365b8f-6e51-41f0-99a6-ee2d0151b22a", "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.image.AxesImage at 0x7feecc6db190>"]}, "execution_count": 158, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.imshow(random_open_closed)"]}, {"cell_type": "code", "execution_count": 193, "id": "5a79bb7a-d8b8-4890-9e75-cc24459d3bdd", "metadata": {}, "outputs": [{"data": {"text/plain": ["(25, 25, 2)"]}, "execution_count": 193, "metadata": {}, "output_type": "execute_result"}], "source": ["timestep.state.grid.shape"]}, {"cell_type": "code", "execution_count": 20, "id": "d161a299-5f42-4b8a-9b4d-6ac1fed8db55", "metadata": {}, "outputs": [], "source": ["def randomly_lock_doors(rng, grid):\n", "    door_mask = grid[..., 0] == Tiles.DOOR_CLOSED\n", "    key_mask = grid[..., 0] == Tiles.KEY\n", "    for color in [Colors.RED,\n", "              Colors.GREEN,\n", "              Colors.BLUE,\n", "              Colors.PURPLE,\n", "              Colors.YELLOW,\n", "              Colors.GREY,\n", "              Colors.BLACK,\n", "              Colors.ORANGE,\n", "              Colors.WHITE,\n", "              Colors.BROWN,\n", "              Colors.PINK]:\n", "        color_mask = grid[..., 1] == color\n", "        has_key_for_door = (color_mask & key_mask).any() & (color_mask & door_mask)\n", "        # randomly close doors\n", "        rng, curr_rng = jax.random.split(rng, 2)\n", "        # uniformly choosing\n", "        random_mask = jax.random.randint(curr_rng, timestep.state.grid[..., 0].shape, 0, 2) == 1\n", "        random_locked_closed = jnp.where(random_mask, Tiles.DOOR_LOCKED, Tiles.DOOR_CLOSED)\n", "        # if we have a certain key of a certain color - we randomly decide to close the door or leave it open. \n", "        # otherwise we leave it open\n", "        new_grid = jnp.where(has_key_for_door, random_locked_closed, grid[..., 0])\n", "        grid = jnp.stack([new_grid, grid[..., 1]], axis=-1)\n", "    return rng, grid\n", "def add_more_keys(rng, tiles, max_num_keys=6):\n", "    key_tiles = tiles.at[:, 0].set(Tiles.KEY)\n", "    rng, curr_key = jax.random.split(rng, 2)\n", "    key_colors = key_tiles.at[:, 1].set(jax.random.randint(curr_key, (key_tiles.shape[0],), 1, 12)) # colors are ints\n", "    tiles_with_keys = jnp.where(tiles != 0, tiles, key_colors)\n", "    rng, curr_key = jax.random.split(rng, 2)\n", "    total = (tiles != 0).all(1).sum()\n", "    max_objs = jax.random.randint(curr_key, (1,), total, total + max_num_keys)\n", "    return rng, jnp.where((jnp.arange(tiles.shape[0]) <= max_objs)[:, None], tiles_with_keys, jnp.zeros_like(tiles))"]}, {"cell_type": "code", "execution_count": 21, "id": "5aa076f9-d8c7-442f-903c-c01961bb4116", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/network/scratch/a/artem.zholus/conda_envs/lg/lib/python3.10/site-packages/jax/_src/ops/scatter.py:96: FutureWarning: scatter inputs have incompatible types: cannot safely cast value from dtype=int32 to dtype=uint8 with jax_numpy_dtype_promotion='standard'. In future JAX releases this will result in an error.\n", "  warnings.warn(\n"]}, {"data": {"text/plain": ["<matplotlib.image.AxesImage at 0x7f1810519030>"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ruleset = benchmark.get_ruleset(ruleset_id=13)\n", "rng = jax.random.key(5)\n", "rng, new_tiles = add_more_keys(rng, ruleset.init_tiles, max_num_keys=10)\n", "ruleset = ruleset.replace(init_tiles=new_tiles)\n", "env_params = env_params.replace(ruleset=ruleset)\n", "timestep = env.reset(env_params, jax.random.key(0))\n", "rng, new_grid = randomly_lock_doors(rng, timestep.state.grid)\n", "timestep = timestep.replace(state=timestep.state.replace(grid=new_grid))\n", "# timestep.state.grid = new_grid\n", "plt.imshow(env.render(env_params, timestep))"]}], "metadata": {"kernelspec": {"display_name": "lg_2", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}