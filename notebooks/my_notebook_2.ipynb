{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[=== Module openmpi/4.0.4 loaded ===]\n", "[=== Module cudatoolkit/12.1.1 loaded ===]\n"]}], "source": ["!module load openmpi\n", "!module load cuda/12.1.1"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ['XLA_PYTHON_CLIENT_PREALLOCATE'] = 'false'"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["goal_map = {\n", "            5: 'ball',\n", "            6: 'square',\n", "            7: 'pyramid',\n", "            8: 'goal',\n", "            9: 'key',\n", "            10: 'locked door',\n", "            11: 'closed door',\n", "            12: 'open door',\n", "            13: 'hex',\n", "            14: 'star'\n", "        }\n", "color_map = {\n", "            3: 'red',\n", "            4: 'green',\n", "            5: 'blue',\n", "            6: 'purple',\n", "            7: 'yellow',\n", "            8: 'grey',\n", "            9: 'black',\n", "            10: 'orange',\n", "            11: 'white',\n", "            12: 'brown',\n", "            13: 'pink'\n", "        }"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ball, square, pyramid, goal, key, locked door, closed door, open door, hex, star\n", "red, green, blue, purple, yellow, grey, black, orange, white, brown, pink\n"]}], "source": ["obeject= ', '.join(f\"{value}\" for key, value in goal_map.items())\n", "print(obeject)\n", "colors = ', '.join(f\"{value}\" for key, value in color_map.items())\n", "print(colors)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["answer the users question as best as possible. \n", "You are in the 4 rooms flat gridworld with 2 column and 2 rows of rooms.\n", " Adjacent rooms are connected with doors. \n", "The objects in the environment are: ball, square, pyramid, goal, key, locked door, closed door, open door, hex, star\n", "The colors in the environment are: red, green, blue, purple, yellow, grey, black, orange, white, brown, pink\n", "Given the goal of the agent please provide what is the object and what is the color of the object that the agent should approach to? \n", "The output should be a markdown code snippet formatted in the following schema, including the leading and trailing \"```json\" and \"```\":\n", "```json\n", "{{\n", "    \"object\": string  // the target object type\n", "    \"color\": string  // target object color\n", "}}\n", "```\n", "Exmaple 1: Goal of the agent is: 'Go to the green square'. Answer: \n", "```json\n", "{{\n", "    \"object\": \"square\" \n", "    \"color\": \"green\" \n", "}}\n", "```\n", "Exmaple 2: Goal of the agent is: 'Pick up the blue key'. Answer: \n", "```json\n", "{{\n", "    \"object\": \"key\" \n", "    \"color\": \"blue\" \n", "}}\n", "```\n", "New: Goal of the agent is: 'Go to the yellow ball'. Answer: \n", "\n"]}], "source": ["clr = 'yellow'\n", "obj = 'ball'\n", "pattern = \\\n", "            \"answer the users question as best as possible. \\n\" \\\n", "            \"You are in the 4 rooms flat gridworld with 2 column and 2 rows of rooms.\\n \" \\\n", "            \"Adjacent rooms are connected with doors. \\n\" \\\n", "            f\"The objects in the environment are: {obeject}\\n\"\\\n", "            f\"The colors in the environment are: {colors}\\n\" \\\n", "            \"Given the goal of the agent please provide what is the object and what is the color of the object that the agent should approach to? \\n\" \\\n", "            'The output should be a markdown code snippet formatted in the following schema, ' \\\n", "            'including the leading and trailing \"```json\" and \"```\":\\n' \\\n", "            '```json\\n' \\\n", "            '{{\\n' \\\n", "            '    \"object\": string  // the target object type\\n' \\\n", "            '    \"color\": string  // target object color\\n' \\\n", "            '}}\\n' \\\n", "            '```\\n' \\\n", "            \"Exmaple 1: Goal of the agent is: 'Go to the green square'. Answer: \\n\" \\\n", "            '```json\\n' \\\n", "            '{{\\n' \\\n", "            '    \"object\": \"square\" \\n' \\\n", "            '    \"color\": \"green\" \\n' \\\n", "            '}}\\n' \\\n", "            '```\\n' \\\n", "            \"Exmaple 2: Goal of the agent is: 'Pick up the blue key'. Answer: \\n\" \\\n", "            '```json\\n' \\\n", "            '{{\\n' \\\n", "            '    \"object\": \"key\" \\n' \\\n", "            '    \"color\": \"blue\" \\n' \\\n", "            '}}\\n' \\\n", "            '```\\n' \\\n", "            f\"New: Goal of the agent is: 'Go to the {clr} {obj}'. Answer: \\n\" \\\n", "\n", "print(pattern)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/transformers/utils/hub.py:124: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.\n", "  warnings.warn(\n", "`config.hidden_act` is ignored, you should use `config.hidden_activation` instead.\n", "<PERSON>'s activation function will be set to `gelu_pytorch_tanh`. Please, use\n", "`config.hidden_activation` if you want to override this behaviour.\n", "See https://github.com/huggingface/transformers/pull/29402 for more details.\n", "Loading checkpoint shards: 100%|██████████| 2/2 [00:07<00:00,  4.00s/it]\n", "/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/transformers/generation/utils.py:1168: UserWarning: Using the model-agnostic default `max_length` (=20) to control the generation length. We recommend setting `max_new_tokens` to control the maximum length of the generation.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<bos>Write me a poem about Machine Learning.\n", "\n", "Machines, they weave and they learn,\n", "From\n"]}], "source": ["from transformers import AutoTokenizer, AutoModelForCausalLM\n", "import os\n", "import torch\n", "tokenizer = AutoTokenizer.from_pretrained(\"google/gemma-2b-it\",cache_dir=os.environ['SCRATCH'] + '/hf')\n", "model = AutoModelForCausalLM.from_pretrained(\"google/gemma-2b-it\", device_map=\"auto\", torch_dtype=torch.bfloat16, cache_dir=os.environ['SCRATCH'] + '/hf')\n", "\n", "input_text = \"Write me a poem about Machine Learning.\"\n", "input_ids = tokenizer(input_text, return_tensors=\"pt\").to(\"cuda\")\n", "\n", "outputs = model.generate(**input_ids)\n", "print(tokenizer.decode(outputs[0]))"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<bos>answer the users question as best as possible. \n", "You are in the 4 rooms flat gridworld with 2 column and 2 rows of rooms.\n", " Adjacent rooms are connected with doors. \n", "The objects in the environment are: ball, square, pyramid, goal, key, locked door, closed door, open door, hex, star\n", "The colors in the environment are: red, green, blue, purple, yellow, grey, black, orange, white, brown, pink\n", "Given the goal of the agent please provide what is the object and what is the color of the object that the agent should approach to? \n", "The output should be a markdown code snippet formatted in the following schema, including the leading and trailing \"```json\" and \"```\":\n", "```json\n", "{{\n", "    \"object\": string  // the target object type\n", "    \"color\": string  // target object color\n", "}}\n", "```\n", "Exmaple 1: Goal of the agent is: 'Go to the green square'. Answer: \n", "```json\n", "{{\n", "    \"object\": \"square\" \n", "    \"color\": \"green\" \n", "}}\n", "```\n", "Exmaple 2: Goal of the agent is: 'Pick up the blue key'. Answer: \n", "```json\n", "{{\n", "    \"object\": \"key\" \n", "    \"color\": \"blue\" \n", "}}\n", "```\n", "New: Goal of the agent is: 'Go to the yellow ball'. Answer: \n", "```json\n", "{{\n", "    \"object\": \"ball\" \n", "    \"color\": \"yellow\" \n", "}}\n", "```\n", "\n", "**Note:** The agent can move freely in the gridworld.<eos>\n"]}], "source": ["input_text = pattern\n", "input_ids = tokenizer(input_text, return_tensors=\"pt\").to(\"cuda\")\n", "\n", "outputs = model.generate(**input_ids,max_length= 1024)\n", "output_text = tokenizer.decode(outputs[0])\n", "print(tokenizer.decode(outputs[0]))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/transformers/utils/hub.py:124: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.\n", "  warnings.warn(\n", "`config.hidden_act` is ignored, you should use `config.hidden_activation` instead.\n", "<PERSON>'s activation function will be set to `gelu_pytorch_tanh`. Please, use\n", "`config.hidden_activation` if you want to override this behaviour.\n", "See https://github.com/huggingface/transformers/pull/29402 for more details.\n", "Loading checkpoint shards: 100%|██████████| 2/2 [00:04<00:00,  2.30s/it]\n"]}], "source": ["import transformers\n", "import torch\n", "\n", "model_id = \"google/gemma-2b-it\" #\"meta-llama/Meta-Llama-3-8B-Instruct\"\n", "\n", "pipeline = transformers.pipeline(\n", "  \"text-generation\",\n", "  model=model_id, #\"meta-llama/Meta-Llama-3-8B-Instruct\",\n", "  model_kwargs={\"torch_dtype\": torch.bfloat16},\n", "  device=\"cuda\",\n", ")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'TextGenerationPipeline' object has no attribute 'batch'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "File \u001b[0;32m<timed eval>:1\u001b[0m\n", "\u001b[0;31mAttributeError\u001b[0m: 'TextGenerationPipeline' object has no attribute 'batch'"]}], "source": ["%%time\n", "print(pipeline.batch([\"What is the capital of France ?\",] * 100))"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["from langchain.output_parsers import ResponseSchema, StructuredOutputParser\n", "from langchain.prompts import PromptTemplate\n", "# from langchain_openai import ChatOpenAI\n", "response_schemas = [\n", "    ResponseSchema(name=\"object\", description=\"the target object type\"),\n", "    ResponseSchema(\n", "        name=\"color\",\n", "        description=\"target object color\",\n", "    ),\n", "]\n", "output_parser = StructuredOutputParser.from_response_schemas(response_schemas)\n", "format_instructions = output_parser.get_format_instructions()\n", "prompt = PromptTemplate(\n", "    template=\"answer the users question as best as possible.\\n\" \n", "        \"You are in the 4 rooms flat gridworld with 2 column and 2 rows of rooms.\\n\"\n", "        \"Adjacent rooms are connected with doors.\\n\"\n", "        \"Goal of the agent: Go to the blue circle.\\n\"\n", "        \"Provide the instruction for the agent.\\n\"\n", "    \n", "        \"\\n {format_instructions}\\n{question}\",\n", "    input_variables=[\"question\"],\n", "    partial_variables={\"format_instructions\": format_instructions},\n", ")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["answer the users question as best as possible.\n", "You are in the 4 rooms flat gridworld with 2 column and 2 rows of rooms.\n", "Adjacent rooms are connected with doors.\n", "Goal of the agent: Go to the blue circle.\n", "Provide the instruction for the agent.\n", "\n", " The output should be a markdown code snippet formatted in the following schema, including the leading and trailing \"```json\" and \"```\":\n", "\n", "```json\n", "{\n", "\t\"object\": string  // the target object type\n", "\t\"color\": string  // target object color\n", "}\n", "```\n", "Instruction step 1:\n"]}], "source": ["print(prompt.invoke({'question': 'Instruction step 1:'}).text)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'model' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[10], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m chain \u001b[39m=\u001b[39m prompt \u001b[39m|\u001b[39m model \u001b[39m|\u001b[39m output_parser\n", "\u001b[0;31mNameError\u001b[0m: name 'model' is not defined"]}], "source": ["chain = prompt | model | output_parser"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["answer the users question as best as possible.\n", "You are in the 4 rooms flat gridworld with 2 column and 2 rows of rooms.\n", "Adjacent rooms are connected with doors.\n", "Goal of the agent: Go to the blue circle.\n", "Provide the instruction for the agent.\n", "\n", " The output should be a markdown code snippet formatted in the following schema, including the leading and trailing \"```json\" and \"```\":\n", "\n", "```json\n", "{\n", "\t\"object\": string  // the target object type\n", "\t\"color\": string  // target object color\n", "}\n", "```\n", "<bos>answer the users question as best as possible. \n", "You are in the 4 rooms flat gridworld with 2 column and 2 rows of rooms.\n", " Adjacent rooms are connected with doors. \n", "The objects in the environment are: ball, square, pyramid, goal, key, locked door, closed door, open door, hex, star\n", "The colors in the environment are: red, green, blue, purple, yellow, grey, black, orange, white, brown, pink\n", "Given the goal of the agent please provide what is the object and what is the color of the object that the agent should approach to? \n", "The output should be a markdown code snippet formatted in the following schema, including the leading and trailing \"```json\" and \"```\":\n", "```json\n", "{{\n", "    \"object\": string  // the target object type\n", "    \"color\": string  // target object color\n", "}}\n", "```\n", "Exmaple 1: Goal of the agent is: 'Go to the green square'. Answer: \n", "```json\n", "{{\n", "    \"object\": \"square\" \n", "    \"color\": \"green\" \n", "}}\n", "```\n", "Exmaple 2: Goal of the agent is: 'Pick up the blue key'. Answer: \n", "```json\n", "{{\n", "    \"object\": \"key\" \n", "    \"color\": \"blue\" \n", "}}\n", "```\n", "New: Goal of the agent is: 'Go to the yellow ball'. Answer: \n", "```json\n", "{{\n", "    \"object\": \"ball\" \n", "    \"color\": \"yellow\" \n", "}}\n", "```\n", "\n", "**Note:** The agent can move freely in the gridworld.<eos>\n"]}], "source": ["print(prompt.invoke({'question': output_text}).text)\n", "# print(chain.invoke({\"question\": output_text}))"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 4}