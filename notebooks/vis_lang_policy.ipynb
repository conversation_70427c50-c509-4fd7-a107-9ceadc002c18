{"cells": [{"cell_type": "code", "execution_count": 1, "id": "7a7def4c-5f1f-4798-8c82-702a6d516fe1", "metadata": {}, "outputs": [], "source": ["import os\n", "# os.environ['JAX_PLATFORM_NAME'] = 'cpu'"]}, {"cell_type": "code", "execution_count": 2, "id": "a270d535-9421-4eea-a8db-372fd6ab3dc8", "metadata": {}, "outputs": [], "source": ["import jax\n", "from jax import numpy as jnp"]}, {"cell_type": "code", "execution_count": 3, "id": "07824f3c-4bb5-47cf-a72c-fd4ed97f2166", "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.insert(0, 'src')"]}, {"cell_type": "code", "execution_count": 4, "id": "cbe504e0-b3af-464b-849f-e72aceb7e3ff", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/network/scratch/a/artem.zholus/conda_envs/rl_jax_torch/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "2024-03-27 13:48:07.849333: W external/xla/xla/service/gpu/nvptx_compiler.cc:742] The NVIDIA driver's CUDA version is 12.2 which is older than the ptxas CUDA version (12.4.99). Because the driver is older than the ptxas version, XLA is disabling parallel compilation, which may slow down compilation. You should update your NVIDIA driver or use the NVIDIA-provided CUDA forward compatibility packages.\n"]}], "source": ["import imageio\n", "import jax\n", "import jax.numpy as jnp\n", "import jmp\n", "import orbax.checkpoint\n", "import xminigrid\n", "from nn import ActorCriticRNN\n", "from xminigrid.rendering.text_render import print_ruleset\n", "from xminigrid.wrappers import GymAutoResetWrapper\n"]}, {"cell_type": "code", "execution_count": 5, "id": "190bc8c6-3f6c-40e6-a3d9-0f2890486494", "metadata": {}, "outputs": [], "source": ["orbax_checkpointer = orbax.checkpoint.PyTreeCheckpointer()\n", "checkpoint = orbax_checkpointer.restore(\"/network/scratch/a/artem.zholus/language_policies/1/checkpoint_0\")\n", "# config = checkpoint[\"config\"]\n", "params = checkpoint['params']\n", "\n", "env, env_params = xminigrid.make(\"XLand-MiniGrid-R4-9x9\")\n", "env = GymAutoResetWrapper(env)"]}, {"cell_type": "code", "execution_count": 6, "id": "be9eead0-21df-4d5e-b695-71ee8ef8b2a0", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:absl:Tensorflow library not found, tensorflow.io.gfile operations will use native shim calls. GCS paths (i.e. 'gs://...') cannot be accessed.\n"]}], "source": ["from train_meta_task import my_load_benchmark"]}, {"cell_type": "code", "execution_count": 7, "id": "6959db4a-31b5-444a-9bb0-6fb078ee2f52", "metadata": {}, "outputs": [], "source": ["benchmark = my_load_benchmark(\"my_trivial_1m_v2\")"]}, {"cell_type": "code", "execution_count": 8, "id": "2eab8a77-78f5-405e-b2ae-9f6ed0410884", "metadata": {}, "outputs": [], "source": ["action_emb_dim: int = 128\n", "rnn_hidden_dim: int = 512\n", "rnn_num_layers: int = 1\n", "head_hidden_dim: int = 512\n", "cnn: str = 'minigrid' # 'minigrid' | 'impala'\n", "half_precision: bool = False\n", "policy = jmp.get_policy('params=float32,compute=float32,output=float32')\n", "dtype = jnp.float32"]}, {"cell_type": "code", "execution_count": 9, "id": "b784782f-ed65-4e54-8635-b52ef2964d6f", "metadata": {}, "outputs": [], "source": ["model = ActorCriticRNN(\n", "    num_actions=env.num_actions(env_params),\n", "    action_emb_dim=action_emb_dim,\n", "    rnn_hidden_dim=rnn_hidden_dim,\n", "    rnn_num_layers=rnn_num_layers,\n", "    head_hidden_dim=head_hidden_dim,\n", "    cnn=cnn,\n", "    amp_policy=policy,\n", "    dtype=dtype\n", ")"]}, {"cell_type": "code", "execution_count": 10, "id": "119ac6d6-bae2-46c4-a635-9198067ac996", "metadata": {}, "outputs": [], "source": ["apply_fn, reset_fn, step_fn = jax.jit(model.apply), jax.jit(env.reset), jax.jit(env.step)"]}, {"cell_type": "code", "execution_count": 1, "id": "4f0958e3-6eeb-4ae3-895c-e4a40a074b2c", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/network/scratch/a/artem.zholus/conda_envs/rl_jax_torch/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import numpy as np\n", "import math\n", "from tqdm import tqdm\n", "from transformers import AutoTokenizer, BertForMaskedLM, BertForPreTraining, FlaxBertForPreTraining\n"]}, {"cell_type": "code", "execution_count": 2, "id": "1793751d-7503-464b-b14d-382d8080d622", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["CUDA backend failed to initialize: Found CUDA version 12010, but JAX was built against version 12030, which is newer. The copy of CUDA that is installed must be at least as new as the version against which JAX was built. (Set TF_CPP_MIN_LOG_LEVEL=0 and rerun for more info.)\n", "Some weights of FlaxBertForPreTraining were not initialized from the model checkpoint at google-bert/bert-base-uncased and are newly initialized: {('cls', 'seq_relationship', 'bias'), ('bert', 'pooler', 'dense', 'bias'), ('bert', 'pooler', 'dense', 'kernel'), ('cls', 'seq_relationship', 'kernel')}\n", "You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.\n"]}], "source": ["tokenizer = AutoTokenizer.from_pretrained(\"google-bert/bert-base-uncased\")\n", "instruction_backbone = FlaxBertForPreTraining.from_pretrained(\"google-bert/bert-base-uncased\")"]}, {"cell_type": "code", "execution_count": 3, "id": "d1ff1075-c46e-49ad-8b76-9611ea3b826f", "metadata": {}, "outputs": [], "source": ["# class Tiles(struct.PyTreeNode):\n", "#     EMPTY: int = struct.field(pytree_node=False, default=0)\n", "#     END_OF_MAP: int = struct.field(pytree_node=False, default=1)\n", "#     UNSEEN: int = struct.field(pytree_node=False, default=2)\n", "#     FLOOR: int = struct.field(pytree_node=False, default=3)\n", "#     WALL: int = struct.field(pytree_node=False, default=4)\n", "#     BALL: int = struct.field(pytree_node=False, default=5)\n", "#     SQUARE: int = struct.field(pytree_node=False, default=6)\n", "#     PYRAMID: int = struct.field(pytree_node=False, default=7)\n", "#     GOAL: int = struct.field(pytree_node=False, default=8)\n", "#     KEY: int = struct.field(pytree_node=False, default=9)\n", "#     DOOR_LOCKED: int = struct.field(pytree_node=False, default=10)\n", "#     DOOR_CLOSED: int = struct.field(pytree_node=False, default=11)\n", "#     DOOR_OPEN: int = struct.field(pytree_node=False, default=12)\n", "#     HEX: int = struct.field(pytree_node=False, default=13)\n", "#     STAR: int = struct.field(pytree_node=False, default=14)\n", "\n", "# class Colors(struct.PyTreeNode):\n", "#     EMPTY: int = struct.field(pytree_node=False, default=0)\n", "#     END_OF_MAP: int = struct.field(pytree_node=False, default=1)\n", "#     UNSEEN: int = struct.field(pytree_node=False, default=2)\n", "#     RED: int = struct.field(pytree_node=False, default=3)\n", "#     GREEN: int = struct.field(pytree_node=False, default=4)\n", "#     BLUE: int = struct.field(pytree_node=False, default=5)\n", "#     PURPLE: int = struct.field(pytree_node=False, default=6)\n", "#     YELLOW: int = struct.field(pytree_node=False, default=7)\n", "#     GREY: int = struct.field(pytree_node=False, default=8)\n", "#     BLACK: int = struct.field(pytree_node=False, default=9)\n", "#     ORANGE: int = struct.field(pytree_node=False, default=10)\n", "#     WHITE: int = struct.field(pytree_node=False, default=11)\n", "#     BROWN: int = struct.field(pytree_node=False, default=12)\n", "#     PINK: int = struct.field(pytree_node=False, default=13)"]}, {"cell_type": "code", "execution_count": 22, "id": "c5a7ba27-6288-4047-b69f-ecdce6504fa3", "metadata": {}, "outputs": [], "source": ["benchmark = benchmark[0]"]}, {"cell_type": "code", "execution_count": 24, "id": "75af4bb7-afe5-4492-a56d-4930a9d0ae9f", "metadata": {}, "outputs": [], "source": ["rs = benchmark.get_ruleset(1)\n", "# rs.goal, rs.goal_vec[:10]"]}, {"cell_type": "code", "execution_count": 19, "id": "fda9f246-7a43-436f-9367-a535ca8a9ba5", "metadata": {}, "outputs": [], "source": ["inputs = tokenizer([\"Find the yellow square1111\", \"Find the yellow square\"], return_tensors=\"np\",padding=True)\n", "obj_id = 6\n", "color_id = 7\n", "outputs = instruction_backbone(**inputs, output_hidden_states=True)"]}, {"cell_type": "code", "execution_count": null, "id": "9b03691f-28f4-4d01-89b7-29a9bd6c0bae", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 20, "id": "505581b0-99a5-4a30-8862-a3f52a0410b9", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'input_ids': array([[  101,  2424,  1996,  3756,  2675, 14526, 14526,   102],\n", "       [  101,  2424,  1996,  3756,  2675,   102,     0,     0]]), 'token_type_ids': array([[0, 0, 0, 0, 0, 0, 0, 0],\n", "       [0, 0, 0, 0, 0, 0, 0, 0]]), 'attention_mask': array([[1, 1, 1, 1, 1, 1, 1, 1],\n", "       [1, 1, 1, 1, 1, 1, 0, 0]])}"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["inputs"]}, {"cell_type": "code", "execution_count": 21, "id": "be62f207-f5c1-4ea6-8aea-4b5ff05287ef", "metadata": {}, "outputs": [{"data": {"text/plain": ["(2, 8, 768)"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["outputs.hidden_states[-1].shape"]}, {"cell_type": "code", "execution_count": 29, "id": "5eed9125-666b-43ec-aba5-ad1c54a0e440", "metadata": {}, "outputs": [], "source": ["my_goal_vec = outputs.hidden_states[-1][0,0]"]}, {"cell_type": "code", "execution_count": 30, "id": "3e301ec0-2c47-44c3-a05d-4dba1a9869af", "metadata": {}, "outputs": [{"data": {"text/plain": ["Array([-0.15973261,  0.2591344 , -0.1988888 ,  0.10605793, -0.2505801 ,\n", "       -0.27617115,  0.29470947,  0.39133298, -0.03246829, -0.4683873 ],      dtype=float32)"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["my_goal_vec[:10]"]}, {"cell_type": "code", "execution_count": 31, "id": "2ba71883-5377-4b96-a721-768ba105f9b4", "metadata": {}, "outputs": [{"data": {"text/plain": ["(768,)"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["my_goal_vec.shape"]}, {"cell_type": "code", "execution_count": 32, "id": "89a0f137-c61b-4eb0-920f-e76f7666a4cc", "metadata": {"scrolled": true}, "outputs": [], "source": ["from tqdm import tqdm"]}, {"cell_type": "code", "execution_count": 33, "id": "173eccff-4016-4722-80e1-32ce64eeef98", "metadata": {}, "outputs": [], "source": ["TOTAL_EPISODES = 100"]}, {"cell_type": "code", "execution_count": 34, "id": "86b6e730-4a5d-461f-af26-c7b09db68bb1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1 6 7 0 0]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|███████████████████████████████████████████████████████████████████████████| 100/100 [00:10<00:00,  9.68it/s]\n"]}], "source": ["prev_reward = jnp.asarray(0)\n", "prev_action = jnp.asarray(0)\n", "hidden = model.initialize_carry(1)\n", "\n", "# for logging\n", "total_reward, num_episodes = 0, 0\n", "rendered_imgs = []\n", "\n", "rng = jax.random.<PERSON><PERSON><PERSON><PERSON>(42)\n", "rng, _rng = jax.random.split(rng)\n", "\n", "ruleset = benchmark.get_ruleset(1)\n", "ruleset = ruleset.replace(goal=jnp.array([1, obj_id, color_id, 0, 0]))\n", "print(ruleset.goal)\n", "env_params = env_params.replace(ruleset=ruleset)\n", "rng, _rng = jax.random.split(rng)\n", "\n", "timestep = reset_fn(env_params, _rng)\n", "rendered_imgs.append([env.render(env_params, timestep)])\n", "dones = 0\n", "cnt = 0\n", "with tqdm(total=TOTAL_EPISODES) as tq:\n", "    while num_episodes < TOTAL_EPISODES:\n", "        rng, _rng = jax.random.split(rng)\n", "        dist, value, hidden = apply_fn(\n", "            params,\n", "            {\n", "                \"observation\": timestep.observation[None, None, ...],\n", "                \"prev_action\": prev_action[None, None, ...],\n", "                \"prev_reward\": prev_reward[None, None, ...],\n", "                # \"goal\": ruleset.goal_vec[None, None, ...]\n", "                'goal': my_goal_vec[None, None],\n", "            },\n", "            hidden,\n", "        )\n", "        action = dist.sample(seed=_rng).squeeze()\n", "        cnt += 1\n", "        if cnt == 50:\n", "            rng, _rng = jax.random.split(rng)\n", "            timestep = reset_fn(env_params, _rng)\n", "            cnt = 0\n", "            num_episodes += 1\n", "            tq.update(1)\n", "        else:\n", "            timestep = step_fn(env_params, timestep, action)\n", "        prev_action = action\n", "        prev_reward = timestep.reward\n", "        dones += timestep.reward.item() > 0\n", "        \n", "        total_reward += timestep.reward.item()\n", "        last = int(timestep.last().item())\n", "        num_episodes += last\n", "        if last > 0:\n", "            rendered_imgs.append([])\n", "            cnt = 0\n", "            tq.update(1)\n", "        rendered_imgs[-1].append(env.render(env_params, timestep))"]}, {"cell_type": "code", "execution_count": 26, "id": "5b2a0e34-9201-48a9-bef5-3aa6af942ca3", "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named '<PERSON><PERSON><PERSON><PERSON><PERSON>'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[26], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[38;5;28;01mi<PERSON>rt\u001b[39;00m \u001b[38;5;21;01mmatplotlib\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01<PERSON><PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mplt\u001b[39;00m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28;01mi<PERSON>rt\u001b[39;00m \u001b[38;5;21;01mi<PERSON><PERSON>\u001b[39;00m\n", "\u001b[0;31mModuleNotFoundError\u001b[0m: No module named 'matplotlib'"]}], "source": ["import matplotlib.pyplot as plt\n", "import imageio"]}, {"cell_type": "code", "execution_count": 35, "id": "2df9f0e5-0bef-4a85-9c15-d16913ef7dc8", "metadata": {}, "outputs": [{"data": {"text/plain": ["1.0"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["dones / TOTAL_EPISODES"]}, {"cell_type": "code", "execution_count": 68, "id": "a995b07e-bb43-4f7a-aca6-a34df97e4dcc", "metadata": {}, "outputs": [{"data": {"text/plain": ["101"]}, "execution_count": 68, "metadata": {}, "output_type": "execute_result"}], "source": ["len(rendered_imgs)"]}, {"cell_type": "code", "execution_count": 24, "id": "c2d398a4-70b8-458a-a859-30da20d5647a", "metadata": {}, "outputs": [{"data": {"text/plain": ["19"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["t =  max([len(i) for i in rendered_imgs])\n", "t"]}, {"cell_type": "code", "execution_count": 25, "id": "d26a3407-97ae-44e7-bce4-bb61dca7a980", "metadata": {}, "outputs": [], "source": ["frames = []\n", "for j in range(t):\n", "    frame = np.concatenate([rendered_imgs[k][j % len(rendered_imgs[k])] for k in range(TOTAL_EPISODES)], axis=1)\n", "    frames.append(frame)"]}, {"cell_type": "code", "execution_count": 26, "id": "da157b84-06d0-4900-a133-a2176e0343a3", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/network/scratch/a/artem.zholus/conda_envs/jax_rl/lib/python3.10/subprocess.py:1796: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = _posixsubprocess.fork_exec(\n", "huggingface/tokenizers: The current process just got forked, after parallelism has already been used. Disabling parallelism to avoid deadlocks...\n", "To disable this warning, you can either:\n", "\t- Avoid using `tokenizers` before the fork if possible\n", "\t- Explicitly set the environment variable TOKENIZERS_PARALLELISM=(true | false)\n"]}], "source": ["imageio.mimsave(\"example_rollout.mp4\", frames, fps=5, format=\"mp4\")"]}, {"cell_type": "code", "execution_count": 27, "id": "48183677-9ed4-4405-8b77-9256153fc329", "metadata": {}, "outputs": [{"data": {"text/html": ["<video controls  >\n", " <source src=\"data:video/mp4;base64,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\" type=\"video/mp4\">\n", " Your browser does not support the video tag.\n", " </video>"], "text/plain": ["<IPython.core.display.Video object>"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["from IPython.display import Video\n", "\n", "Video(\"example_rollout.mp4\", embed=True)"]}, {"cell_type": "code", "execution_count": 32, "id": "2dd66dca-0eb9-4f27-a6da-8ad35e31f20c", "metadata": {}, "outputs": [{"data": {"text/plain": ["96.07777798175812"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["total_reward"]}, {"cell_type": "code", "execution_count": 88, "id": "84b77167-4de8-4fba-aaa7-1153f6fefec4", "metadata": {}, "outputs": [{"data": {"text/plain": ["10"]}, "execution_count": 88, "metadata": {}, "output_type": "execute_result"}], "source": ["num_episodes"]}, {"cell_type": "code", "execution_count": 89, "id": "468badcb-ffde-4ec1-ae27-dae2ec51c275", "metadata": {}, "outputs": [{"data": {"text/plain": ["Array(0., dtype=float32, weak_type=True)"]}, "execution_count": 89, "metadata": {}, "output_type": "execute_result"}], "source": ["prev_reward"]}, {"cell_type": "code", "execution_count": null, "id": "ca95329f-25e4-4c13-ad85-018eb2c1c050", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}