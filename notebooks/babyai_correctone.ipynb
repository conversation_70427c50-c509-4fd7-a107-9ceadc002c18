{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["For BabyAI:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["### for the old env\n", "def describe(self):\n", "    room_w, room_h = self.room_grid[0][0].size\n", "    player_room_x, player_room_y = self.agent_pos\n", "    player_room_x, player_x = player_room_x //room_w , player_room_x % room_w\n", "    player_room_y, player_y = player_room_y //room_h , player_room_y % room_h\n", "    objects = 'Here is the desription of each room. '\n", "    for x in (range(self.num_cols)):\n", "        for y in (range(self.num_rows)):\n", "            room_row = self.room_grid[x]\n", "            room = room_row[y]\n", "            if len(room.objs) > 0:\n", "                objects += f'The room at column {x} and row {y} has '\n", "                objs = []\n", "                for obj in room.objs:\n", "                    type_name = type(obj).__name__\n", "                    at = str(tuple(obj.cur_pos.tolist()))\n", "                    objs.append(f' a {obj.color} {type_name} at {at}')\n", "                objects += ','.join(objs) + \".\"\n", "    return (f\"You are in the {self.num_cols * self.num_rows} rooms flat\" +\n", "            f\" gridworld with {self.num_cols} column and {self.num_rows} rows of rooms. \" +\n", "            f\"You are located in the room with at column {player_room_x} and row {player_room_y}. \" +\n", "            f\"Your position within that room is ({player_x}, {player_y}).\"\n", "        \" Adjacent rooms are connected with doors. \" +\n", "        \"Each room is {} cells wide and {} cells high. \".format(room_w, room_h) +\n", "        objects)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Describe function:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import sys\n", "sys.path.insert(0, 'src')\n", "import os\n", "from jax import numpy as jnp\n", "import imageio\n", "import jax\n", "import jax.numpy as jnp\n", "import jmp\n", "import orbax.checkpoint\n", "import xminigrid\n", "# from nn import ActorCriticRNN\n", "from xminigrid.rendering.text_render import print_ruleset\n", "from xminigrid.wrappers import GymAutoResetWrapper\n", "\n", "# utils for the demonstation\n", "from xminigrid.core.grid import room\n", "from xminigrid.types import AgentState\n", "from xminigrid.core.actions import take_action\n", "from xminigrid.core.constants import Tiles, Colors, TILES_REGISTRY\n", "from xminigrid.rendering.rgb_render import render\n", "\n", "# rules and goals\n", "from xminigrid.core.goals import check_goal, AgentNearGoal\n", "from xminigrid.core.rules import check_rule, AgentNearRule\n", "import timeit\n", "import imageio\n", "import matplotlib.pyplot as plt\n", "from tqdm.auto import trange, tqdm\n", "\n", "def show_img(img, dpi=32):\n", "    plt.figure(dpi=dpi)\n", "    plt.axis('off')\n", "    plt.imshow(img)\n", "\n", "\n", "import jax.random\n", "import xminigrid\n", "from xminigrid.benchmarks import Benchmark\n", "benchmark: Benchmark = xminigrid.load_benchmark(name=\"high-3m\")\n", "env, env_params = xminigrid.make(\"XLand-MiniGrid-R9-25x25\")\n", "env = GymAutoResetWrapper(env)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def build_rollout(env, env_params, num_steps):\n", "    def rollout(rng):\n", "        def _step_fn(carry, _):\n", "            rng, timestep = carry\n", "            rng, _rng = jax.random.split(rng)\n", "            action = jax.random.randint(_rng, shape=(), minval=0, maxval=env.num_actions(env_params))\n", "            \n", "            timestep = env.step(env_params, timestep, action)\n", "            return (rng, timestep), timestep\n", "    \n", "        rng, _rng = jax.random.split(rng)\n", "    \n", "        timestep = env.reset(env_params, _rng)\n", "        rng, transitions = jax.lax.scan(_step_fn, (rng, timestep), None, length=num_steps)\n", "        return transitions\n", "\n", "    return rollout"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.image.AxesImage at 0x7f03643a3370>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["benchmark.sample_ruleset(jax.random.key(0))\n", "ruleset = benchmark.get_ruleset(ruleset_id=11)\n", "env_params = env_params.replace(ruleset=ruleset)\n", "timestep = env.reset(env_params, jax.random.key(0))\n", "plt.imshow(env.render(env_params, timestep))"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["5\n", "10\n"]}], "source": ["print(timestep.state.grid[3, 19, 0])\n", "(timestep.state.grid[..., 0]==Tiles.PYRAMID ).nonzero()\n", "print(Colors.BROWN)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["tiles_type = {\n", "    # 'EMPTY': 0,\n", "    # 'END_OF_MAP': 1,\n", "    # 'UNSEEN': 2,\n", "    # 'FLOOR': 3,\n", "    # 'WALL': 4,\n", "    'BALL': 3,\n", "    'SQUARE': 4,\n", "    'PYRAMID': 5,\n", "    'GOAL': 6,\n", "    'KEY': 7,\n", "    'DOOR_LOCKED': 8,\n", "    'DOOR_CLOSED': 9,\n", "    'DOOR_OPEN': 10,\n", "    'HEX': 11,\n", "    'STAR': 12}\n", "\n", "colors_type = {\n", "    # 'EMPTY': 0,\n", "    # 'END_OF_MAP': 1,\n", "    # 'UNSEEN': 2,\n", "    'RED': 1,\n", "    'GREEN': 2,\n", "    'BLUE': 3,\n", "    'PURPLE': 4,\n", "    'YELLOW': 5,\n", "    'GREY': 6,\n", "    'BLACK': 7,\n", "    'ORANGE': 8,\n", "    'WHITE': 9,\n", "    'BROWN': 10,\n", "    'PINK': 11}"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["### for the new env?\n", "def describe(timestep):\n", "    shape_ = timestep.state.grid.shape\n", "    num_cols= shape_[0]//8\n", "    num_rows = shape_[1]//8\n", "    # number_rooms= env_params.grid_type\n", "    room_w= 8\n", "    room_h= 8 #self.room_grid[0][0].size\n", "    player_room_x, player_room_y = timestep.state.agent.position\n", "    player_room_x_room, player_x = player_room_x //room_w , player_room_x % room_w\n", "    player_room_y_room, player_y = player_room_y //room_h , player_room_y % room_h\n", "\n", "\n", "    objects = 'Here is the objects of each room. '\n", "\n", "    items_in_env = {'obj':[], 'color':[]}\n", "    for obj in tiles_type.keys():\n", "        for color in colors_type.keys():\n", "            items = ((timestep.state.grid[..., 0] == tiles_type[obj]) & (timestep.state.grid[..., 1] == colors_type[color])).nonzero()\n", "            # print(items)\n", "            for i in range(len(items[0])):\n", "                items_in_env['obj'].append(obj)\n", "                items_in_env['color'].append(color)\n", "                objects += f' a {color} {obj} at {items[1][i]} x-axis and {items[0][i]} y-axis coordination in room at column {items[1][i]//8} and row {items[0][i]//8}, \\n'\n", "    \n", "    return (f\"You are in the {num_cols * num_rows} rooms flat\" +\n", "            f\" gridworld with {num_cols} column and {num_rows} rows of rooms. \\n\" +\n", "            f\" This environment can have any of these object: \\n {tiles_type.keys()} \\n \"+\n", "            f\"with any of these colors: \\n\"+\n", "            f\" {colors_type.keys()}. \\n\"+\n", "            f\"/this is World Map: You are located in the room at column {player_room_x_room} and row {player_room_y_room}. \\n\" +\n", "            f\"Your position within that room is ({player_x}, {player_y}) and in the environment is {player_room_x}, {player_room_y} \\n\"\n", "        \" Adjacent rooms are connected with doors. \\n\" +\n", "        \"Each room is {} cells wide and {} cells high. \\n \".format(room_w, room_h) +\n", "        objects), items_in_env"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["desc, items_in_env= describe(timestep)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You are in the 9 rooms flat gridworld with 3 column and 3 rows of rooms. \n", " This environment can have any of these object: \n", " dict_keys(['BALL', 'SQUAR<PERSON>', 'PYRAMID', 'GOAL', 'KEY', 'DOOR_LOCKED', 'DOOR_CLOSED', 'DOOR_OPEN', 'HEX', 'STAR']) \n", " with any of these colors: \n", " dict_keys(['RED', 'G<PERSON><PERSON>', 'BL<PERSON>', 'PURPLE', 'YELL<PERSON>', 'GREY', 'BLACK', 'ORANGE', 'WHITE', 'BROWN', 'PINK']). \n", "/this is World Map: You are located in the room at column 1 and row 1. \n", "Your position within that room is (4, 4) and in the environment is 12, 12 \n", " Adjacent rooms are connected with doors. \n", "Each room is 8 cells wide and 8 cells high. \n", " Here is the objects of each room.  a YELLOW BALL at 18 x-axis and 1 y-axis coordination in room at column 2 and row 0, \n", " a BLUE SQUARE at 3 x-axis and 15 y-axis coordination in room at column 0 and row 1, \n", " a YELLOW SQUARE at 19 x-axis and 23 y-axis coordination in room at column 2 and row 2, \n", " a YELLOW PYRAMID at 19 x-axis and 3 y-axis coordination in room at column 2 and row 0, \n", " a BROWN GOAL at 14 x-axis and 11 y-axis coordination in room at column 1 and row 1, \n", " a PURPLE KEY at 22 x-axis and 14 y-axis coordination in room at column 2 and row 1, \n", " a RED DOOR_CLOSED at 7 x-axis and 8 y-axis coordination in room at column 0 and row 1, \n", " a RED DOOR_CLOSED at 11 x-axis and 16 y-axis coordination in room at column 1 and row 2, \n", " a GREEN DOOR_CLOSED at 16 x-axis and 2 y-axis coordination in room at column 2 and row 0, \n", " a GREEN DOOR_CLOSED at 9 x-axis and 8 y-axis coordination in room at column 1 and row 1, \n", " a GREEN DOOR_CLOSED at 8 x-axis and 21 y-axis coordination in room at column 1 and row 2, \n", " a BLUE DOOR_CLOSED at 8 x-axis and 9 y-axis coordination in room at column 1 and row 1, \n", " a BLUE DOOR_CLOSED at 16 x-axis and 12 y-axis coordination in room at column 2 and row 1, \n", " a PURPLE DOOR_CLOSED at 8 x-axis and 5 y-axis coordination in room at column 1 and row 0, \n", " a YELLOW DOOR_CLOSED at 2 x-axis and 16 y-axis coordination in room at column 0 and row 2, \n", " a GREY DOOR_CLOSED at 17 x-axis and 8 y-axis coordination in room at column 2 and row 1, \n", " a GREY DOOR_CLOSED at 22 x-axis and 16 y-axis coordination in room at column 2 and row 2, \n", " a GREY DOOR_CLOSED at 16 x-axis and 22 y-axis coordination in room at column 2 and row 2, \n", " a RED STAR at 22 x-axis and 20 y-axis coordination in room at column 2 and row 2, \n", "\n", "{'obj': ['BALL', '<PERSON>Q<PERSON><PERSON><PERSON>', 'SQU<PERSON><PERSON>', '<PERSON><PERSON><PERSON>MI<PERSON>', 'GO<PERSON>', 'KE<PERSON>', 'DOOR_CLOSED', 'DOOR_CLOSED', 'DO<PERSON>_CLOSED', '<PERSON><PERSON><PERSON>_CLOSED', '<PERSON><PERSON><PERSON>_CLOSED', '<PERSON><PERSON><PERSON>_CLOSED', 'DOOR_CLOSED', 'DOOR_CLOSED', 'DOOR_CLOSED', 'DOOR_CLOSED', 'DOOR_CLOSED', 'DOOR_CLOSED', 'STAR'], 'color': ['YELLOW', 'BLUE', 'YELLOW', 'YELLOW', 'BROWN', 'PURPLE', 'RED', 'RED', 'GRE<PERSON>', 'GRE<PERSON>', 'GREEN', 'BLUE', 'BLUE', 'PURPLE', 'YELLOW', 'GREY', 'GREY', 'GREY', 'RED']}\n"]}, {"data": {"text/plain": ["<matplotlib.image.AxesImage at 0x7f032c296440>"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(desc)\n", "print(items_in_env)\n", "plt.imshow(env.render(env_params, timestep))"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["9\n", "6\n", "9\n", "6\n"]}], "source": ["print(timestep.state.grid[8, 17, 0])\n", "print(timestep.state.grid[8, 17, 1])\n", "(timestep.state.grid[..., 0]==Tiles.PYRAMID ).nonzero()\n", "'a GREY DOOR_CLOSED at 8 and 17 coordination in room at column 1 and row 2, '\n", "print(Tiles.DOOR_CLOSED)\n", "print(Colors.GREY)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 13, "metadata": {"notebookRunGroups": {"groupValue": "2"}}, "outputs": [], "source": ["# desc= describe(env)\n", "\n", "high_act_list = []\n", "\n", "if len(high_act_list) == 0: end='No high level action is done yet!\\n What is the current high level action that the agent should complete? \\n'\n", "else:\n", "    end = 'These are the hight level actions which are done till now: \\n'\n", "    for i in len(high_act_list):\n", "        end += str(high_act_list[i]) + ', \\n'\n", "    end += 'What is the current high level action that the agent should complete?\\n'\n", "\n", "current_obs = ''\n", " "]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["from tqdm import tqdm\n", "from langchain.output_parsers import ResponseSchema, StructuredOutputParser\n", "from langchain.prompts import PromptTemplate\n", "\n", "# Define goal and color mappings\n", "# goal_map = {\n", "#     5: 'ball', 6: 'square', 7: 'pyramid', 8: 'goal', 9: 'key',\n", "#     10: 'locked door', 11: 'closed door', 12: 'open door', 13: 'hex', 14: 'star'\n", "# }\n", "# color_map = {\n", "#     3: 'red', 4: 'green', 5: 'blue', 6: 'purple', 7: 'yellow',\n", "#     8: 'grey', 9: 'black', 10: 'orange', 11: 'white', 12: 'brown', 13: 'pink'\n", "# }\n", "\n", "# Define the response schema\n", "response_schemas = [\n", "    ResponseSchema(name=\"action\", description=\"the target action type\"),\n", "    ResponseSchema(name=\"color\", description=\"target object color\"),\n", "    ResponseSchema(name=\"object\", description=\"the target object type\"),\n", "]\n", "output_parser = StructuredOutputParser.from_response_schemas(response_schemas)\n", "format_instructions = output_parser.get_format_instructions()\n", "\n", "\n", "# Define the prompt template\n", "prompt_template = desc + current_obs + end + \"Assume the agent can do either 'pick-up an object' or 'open a door'.\\n \"\\\n", "     \" The output should be a markdown code snippet formatted in the following schema, \\n\"\\\n", "     'including the leading and trailing \"```json\" and \"```\":\\n'\\\n", "    '```json\\n'\\\n", "    '```json\\n' \\\n", "    '{{\\n' \\\n", "    '    \"action\": string,  // the target action type\\n' \\\n", "    '    \"color\": string  // target object color\\n' \\\n", "    '    \"object\": string  // the target object type\\n' \\\n", "    '}}\\n' \\\n", "    '```\\n' \\\n", "    \"Exmaple 1: The high level action is pick-up a green key. Answer: \\n\" \\\n", "    '```json\\n' \\\n", "    '{{\\n' \\\n", "    '    \"action\": \"pick-up\", \\n' \\\n", "    '    \"color\": \"green\" \\n' \\\n", "    '    \"object\": \"key\" \\n' \\\n", "    '}}\\n' \\\n", "    '```\\n' \\\n", "    \"Exmaple 2: The high level action is open the red door. Answer \\n\" \\\n", "    '```json\\n' \\\n", "    '{{\\n' \\\n", "    '    \"action\": \"open,\" \\n' \\\n", "    '    \"color\": \"red\" \\n' \\\n", "    '    \"object\": \"door\" \\n' \\\n", "    '}}\\n' \\\n", "  \n", "\n", "\n", "\n", "prompt = PromptTemplate(\n", "template= desc + current_obs + end + prompt_template, \n", "input_variables=[\"desc\", \"current_obs\", \"end\"],\n", "partial_variables={\"format_instructions\": format_instructions},\n", ")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["Array([ 8,  7, 11,  5,  8], dtype=int32)"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["timestep.state.goal_encoding"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["4"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "\n", "pick_item = np.random.randint(0, len(items_in_env['obj'])-1)\n", "pick_item\n", "# len(items_in_env['obj'])-1"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["go to GREEN DOOR_CLOSED\n"]}], "source": ["## define goal here using timestep and items_in_env\n", "import numpy as np\n", "def make_goal(timestep, items_in_env):\n", "    pick_item = np.random.randint(0, len(items_in_env['obj'])-1)\n", "    goal = f\"go to {items_in_env['color'][pick_item]} {items_in_env['obj'][pick_item]}\"\n", "    return goal\n", "\n", "\n", "goal = make_goal(timestep, items_in_env)\n", "print(goal)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/pty.py:89: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  pid, fd = os.forkpty()\n", "huggingface/tokenizers: The current process just got forked, after parallelism has already been used. Disabling parallelism to avoid deadlocks...\n", "To disable this warning, you can either:\n", "\t- Avoid using `tokenizers` before the fork if possible\n", "\t- Explicitly set the environment variable TOKENIZERS_PARALLELISM=(true | false)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["transformers                  4.38.2\n", "transformers-stream-generator 0.0.5\n", "xformers                      0.0.23.post1\n"]}], "source": ["!pip list | grep formers"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["current_obs='' ## we can use it later if we think it is good \n", "\n", "def make_prompt( timestep, current_obs, high_act_list, goal):\n", "    ''' it build the input of the LLM'''\n", "    \n", "    desc, _= describe(timestep)\n", "    find_goal_prompt = 'What is the current high level action that the agent should do in order to achieve to gosl? You should specify the action using the World Map and the objects in the environment \\n'\n", "    if len(high_act_list) == 0: end='No high level action is done yet!\\n'\n", "    else:\n", "        end = 'These are the hight level actions which are done till now: \\n'\n", "        for i in len(high_act_list):\n", "            end += str(high_act_list[i]) + ', \\n'\n", "    end += find_goal_prompt\n", "\n", "    prompt_input_LLM = desc + f\"Your goal is {goal}. \\n {current_obs} Assume the agent can do either 'pick-up an object' or 'open a door'. {end} \\n \"\\\n", "     \" The output should be a markdown code snippet formatted in the following schema, \\n\"\\\n", "     'including the leading and trailing \"```json\" and \"```\":\\n'\\\n", "    '```json\\n'\\\n", "    '```json\\n' \\\n", "    '{{\\n' \\\n", "    '    \"action\": string,  // the target action type\\n' \\\n", "    '    \"color\": string  // target object color\\n' \\\n", "    '    \"object\": string  // the target object type\\n' \\\n", "    '}}\\n' \\\n", "    '```\\n' \\\n", "    \"Exmaple 1: The high level action is pick-up a green key. Answer: \\n\" \\\n", "    '```json\\n' \\\n", "    '{{\\n' \\\n", "    '    \"action\": \"pick-up\", \\n' \\\n", "    '    \"color\": \"green\" \\n' \\\n", "    '    \"object\": \"key\" \\n' \\\n", "    '}}\\n' \\\n", "    '```\\n' \\\n", "    \"Exmaple 2: The high level action is open the red door. Answer \\n\" \\\n", "    '```json\\n' \\\n", "    '{{\\n' \\\n", "    '    \"action\": \"open,\" \\n' \\\n", "    '    \"color\": \"red\" \\n' \\\n", "    '    \"object\": \"door\" \\n' \\\n", "    '}}\\n' \\\n", "    '. Just generate the action: '\n", "\n", "    return prompt_input_LLM"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["# desc + f\"Your goal is {goal}. \\n\"\n", "#     + current_obs + end\n", "prompt_input_LLM = make_prompt( timestep, current_obs, high_act_list, goal)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You are in the 9 rooms flat gridworld with 3 column and 3 rows of rooms. \n", " This environment can have any of these object: \n", " dict_keys(['BALL', 'SQUAR<PERSON>', 'PYRAMID', 'GOAL', 'KEY', 'DOOR_LOCKED', 'DOOR_CLOSED', 'DOOR_OPEN', 'HEX', 'STAR']) \n", " with any of these colors: \n", " dict_keys(['RED', 'G<PERSON><PERSON>', 'BL<PERSON>', 'PURPLE', 'YELL<PERSON>', 'GREY', 'BLACK', 'ORANGE', 'WHITE', 'BROWN', 'PINK']). \n", "/this is World Map: You are located in the room at column 1 and row 1. \n", "Your position within that room is (4, 4) and in the environment is 12, 12 \n", " Adjacent rooms are connected with doors. \n", "Each room is 8 cells wide and 8 cells high. \n", " Here is the objects of each room.  a YELLOW BALL at 18 x-axis and 1 y-axis coordination in room at column 2 and row 0, \n", " a BLUE SQUARE at 3 x-axis and 15 y-axis coordination in room at column 0 and row 1, \n", " a YELLOW SQUARE at 19 x-axis and 23 y-axis coordination in room at column 2 and row 2, \n", " a YELLOW PYRAMID at 19 x-axis and 3 y-axis coordination in room at column 2 and row 0, \n", " a BROWN GOAL at 14 x-axis and 11 y-axis coordination in room at column 1 and row 1, \n", " a PURPLE KEY at 22 x-axis and 14 y-axis coordination in room at column 2 and row 1, \n", " a RED DOOR_CLOSED at 7 x-axis and 8 y-axis coordination in room at column 0 and row 1, \n", " a RED DOOR_CLOSED at 11 x-axis and 16 y-axis coordination in room at column 1 and row 2, \n", " a GREEN DOOR_CLOSED at 16 x-axis and 2 y-axis coordination in room at column 2 and row 0, \n", " a GREEN DOOR_CLOSED at 9 x-axis and 8 y-axis coordination in room at column 1 and row 1, \n", " a GREEN DOOR_CLOSED at 8 x-axis and 21 y-axis coordination in room at column 1 and row 2, \n", " a BLUE DOOR_CLOSED at 8 x-axis and 9 y-axis coordination in room at column 1 and row 1, \n", " a BLUE DOOR_CLOSED at 16 x-axis and 12 y-axis coordination in room at column 2 and row 1, \n", " a PURPLE DOOR_CLOSED at 8 x-axis and 5 y-axis coordination in room at column 1 and row 0, \n", " a YELLOW DOOR_CLOSED at 2 x-axis and 16 y-axis coordination in room at column 0 and row 2, \n", " a GREY DOOR_CLOSED at 17 x-axis and 8 y-axis coordination in room at column 2 and row 1, \n", " a GREY DOOR_CLOSED at 22 x-axis and 16 y-axis coordination in room at column 2 and row 2, \n", " a GREY DOOR_CLOSED at 16 x-axis and 22 y-axis coordination in room at column 2 and row 2, \n", " a RED STAR at 22 x-axis and 20 y-axis coordination in room at column 2 and row 2, \n", "Your goal is go to GREEN DOOR_CLOSED. \n", "  Assume the agent can do either 'pick-up an object' or 'open a door'. No high level action is done yet!\n", "What is the current high level action that the agent should do in order to achieve to gosl? You should specify the action using the World Map and the objects in the environment \n", " \n", "  The output should be a markdown code snippet formatted in the following schema, \n", "including the leading and trailing \"```json\" and \"```\":\n", "```json\n", "```json\n", "{{\n", "    \"action\": string,  // the target action type\n", "    \"color\": string  // target object color\n", "    \"object\": string  // the target object type\n", "}}\n", "```\n", "Exmaple 1: The high level action is pick-up a green key. Answer: \n", "```json\n", "{{\n", "    \"action\": \"pick-up\", \n", "    \"color\": \"green\" \n", "    \"object\": \"key\" \n", "}}\n", "```\n", "Exmaple 2: The high level action is open the red door. Answer \n", "```json\n", "{{\n", "    \"action\": \"open,\" \n", "    \"color\": \"red\" \n", "    \"object\": \"door\" \n", "}}\n", ". Just generate the action: \n"]}], "source": ["print(prompt_input_LLM)"]}, {"cell_type": "code", "execution_count": 75, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/huggingface_hub/file_download.py:797: FutureWarning: `resume_download` is deprecated and will be removed in version 1.0.0. Downloads always resume when possible. If you want to force a new download, use `force_download=True`.\n", "  warnings.warn(\n", " ... (more hidden) ...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<bos>You are in the 9 rooms flat gridworld with 3 column and 3 rows of rooms. \n", " This environment can have any of these object: \n", " dict_keys(['BALL', 'SQUAR<PERSON>', 'PYRAMID', 'GOAL', 'KEY', 'DOOR_LOCKED', 'DOOR_CLOSED', 'DOOR_OPEN', 'HEX', 'STAR']) \n", " with any of these colors: \n", " dict_keys(['RED', 'G<PERSON><PERSON>', 'BL<PERSON>', 'PURPLE', 'YELL<PERSON>', 'GREY', 'BLACK', 'ORANGE', 'WHITE', 'BROWN', 'PINK']). \n", "/this is World Map: You are located in the room at column 1 and row 1. \n", "Your position within that room is (4, 4) and in the environment is 12, 12 \n", " Adjacent rooms are connected with doors. \n", "Each room is 8 cells wide and 8 cells high. \n", " Here is the objects of each room.  a YELLOW BALL at 18 x-axis and 1 y-axis coordination in room at column 2 and row 0, \n", " a BLUE SQUARE at 3 x-axis and 15 y-axis coordination in room at column 0 and row 1, \n", " a YELLOW SQUARE at 19 x-axis and 23 y-axis coordination in room at column 2 and row 2, \n", " a YELLOW PYRAMID at 19 x-axis and 3 y-axis coordination in room at column 2 and row 0, \n", " a BROWN GOAL at 14 x-axis and 11 y-axis coordination in room at column 1 and row 1, \n", " a PURPLE KEY at 22 x-axis and 14 y-axis coordination in room at column 2 and row 1, \n", " a RED DOOR_CLOSED at 7 x-axis and 8 y-axis coordination in room at column 0 and row 1, \n", " a RED DOOR_CLOSED at 11 x-axis and 16 y-axis coordination in room at column 1 and row 2, \n", " a GREEN DOOR_CLOSED at 16 x-axis and 2 y-axis coordination in room at column 2 and row 0, \n", " a GREEN DOOR_CLOSED at 9 x-axis and 8 y-axis coordination in room at column 1 and row 1, \n", " a GREEN DOOR_CLOSED at 8 x-axis and 21 y-axis coordination in room at column 1 and row 2, \n", " a BLUE DOOR_CLOSED at 8 x-axis and 9 y-axis coordination in room at column 1 and row 1, \n", " a BLUE DOOR_CLOSED at 16 x-axis and 12 y-axis coordination in room at column 2 and row 1, \n", " a PURPLE DOOR_CLOSED at 8 x-axis and 5 y-axis coordination in room at column 1 and row 0, \n", " a YELLOW DOOR_CLOSED at 2 x-axis and 16 y-axis coordination in room at column 0 and row 2, \n", " a GREY DOOR_CLOSED at 17 x-axis and 8 y-axis coordination in room at column 2 and row 1, \n", " a GREY DOOR_CLOSED at 22 x-axis and 16 y-axis coordination in room at column 2 and row 2, \n", " a GREY DOOR_CLOSED at 16 x-axis and 22 y-axis coordination in room at column 2 and row 2, \n", " a RED STAR at 22 x-axis and 20 y-axis coordination in room at column 2 and row 2, \n", "Your goal is go to YELLOW PYRAMID. \n", "  Assume the agent can do either 'pick-up an object' or 'open a door'. No high level action is done yet!\n", "What is the current high level action that the agent should do in order to achieve to gosl? You should specify the action using the World Map and the objects in the environment \n", " \n", "  The output should be a markdown code snippet formatted in the following schema, \n", "including the leading and trailing \"```json\" and \"```\":\n", "```json\n", "```json\n", "{{\n", "    \"action\": string,  // the target action type\n", "    \"color\": string  // target object color\n", "    \"object\": string  // the target object type\n", "}}\n", "```\n", "Exmaple 1: The high level action is pick-up a green key. Answer: \n", "```json\n", "{{\n", "    \"action\": \"pick-up\", \n", "    \"color\": \"green\" \n", "    \"object\": \"key\" \n", "}}\n", "```\n", "Exmaple 2: The high level action is open the red door. Answer \n", "```json\n", "{{\n", "    \"action\": \"open,\" \n", "    \"color\": \"red\" \n", "    \"object\": \"door\" \n", "}}\n", ". Just generate the action: \n", "```json\n", "{{\n", "    \"action\": \"pick-up\", \n", "    \"color\": \"yellow\" \n", "    \"object\": \"BALL\" \n", "}}\n", "```\n", "\n", "The current high level action that the agent should do in order to achieve to go to YELLOW PYRAMID is pick-up a yellow ball.<eos>\n"]}], "source": ["# pip install accelerate\n", "from transformers import AutoTokenizer, AutoModelForCausalLM\n", "import torch\n", "\n", "tokenizer = AutoTokenizer.from_pretrained(\"google/gemma-2b-it\")\n", "model = AutoModelForCausalLM.from_pretrained(\n", "    \"google/gemma-2b-it\",\n", "    device_map=\"auto\",\n", "    torch_dtype=torch.bfloat16,\n", ")\n", "\n", "input_text = prompt_input_LLM #\"Write me a poem about Machine Learning.\"\n", "input_ids = tokenizer(input_text, return_tensors=\"pt\").to(\"cuda\")\n", "\n", "outputs = model.generate(**input_ids, max_new_tokens=1024)\n", "print(tokenizer.decode(outputs[0]))\n", "\n", "# from transformers import AutoTokenizer, AutoModelForCausalLM\n", "# import os\n", "# import torch\n", "# tokenizer = AutoTokenizer.from_pretrained(\"google/gemma-2b-it\",cache_dir=os.environ['SCRATCH'] + '/hf')\n", "# model = AutoModelForCausalLM.from_pretrained(\"google/gemma-2b-it\", device_map=\"auto\", torch_dtype=torch.bfloat16, cache_dir=os.environ['SCRATCH'] + '/hf')\n", "\n", "# input_text = \"Write me a poem about Machine Learning.\"\n", "# input_ids = tokenizer(input_text, return_tensors=\"pt\").to(\"cuda\")\n", "\n", "# outputs = model.generate(**input_ids)\n", "# print(tokenizer.decode(outputs[0]))"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3282\n"]}, {"data": {"text/plain": ["'`'"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["answer = tokenizer.decode(outputs[0])\n", "loc = answer.find('Just generate the action:')\n", "print(loc)\n", "answer[loc+27]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "n_samples = 1000\n", "\n", "for i in range(n_samples): # env and goal is fixed\n", "    outputs = model.generate(**input_ids, max_new_tokens=1024)\n", "    print(tokenizer.decode(outputs[0]))\n", "    answer = tokenizer.decode(outputs[0])\n", "    loc = answer.find('Just generate the action:')\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from tqdm import tqdm\n", "from langchain.output_parsers import ResponseSchema, StructuredOutputParser\n", "from langchain.prompts import PromptTemplate\n", "\n", "# Define goal and color mappings\n", "goal_map = {\n", "    5: 'ball', 6: 'square', 7: 'pyramid', 8: 'goal', 9: 'key',\n", "    10: 'locked door', 11: 'closed door', 12: 'open door', 13: 'hex', 14: 'star'\n", "}\n", "color_map = {\n", "    3: 'red', 4: 'green', 5: 'blue', 6: 'purple', 7: 'yellow',\n", "    8: 'grey', 9: 'black', 10: 'orange', 11: 'white', 12: 'brown', 13: 'pink'\n", "}\n", "\n", "# Define the response schema\n", "response_schemas = [\n", "    ResponseSchema(name=\"object\", description=\"the target object type\"),\n", "    ResponseSchema(name=\"color\", description=\"target object color\"),\n", "]\n", "output_parser = StructuredOutputParser.from_response_schemas(response_schemas)\n", "format_instructions = output_parser.get_format_instructions()\n", "\n", "# Define the prompt template\n", "prompt_template = \"You are in the 4 rooms flat gridworld with 2 columns and 2 rows of rooms.\"\\\n", "\"Adjacent rooms are connected with doors. The objects in the environment are: ball, square, pyramid, goal, key, locked door, closed door, open door, hex, star. The colors in the environment are: red, green, blue, purple, yellow, grey, black, orange, white, brown, pink. \"\\\n", "\"Given the goal of the agent, please provide the object and the color of the object that the agent should approach.\"\\\n", "     \" The output should be a markdown code snippet formatted in the following schema,\"\\\n", "     'including the leading and trailing \"```json\" and \"```\":\\n'\\\n", "    '```json\\n'\\\n", "    '{{\\n' \\\n", "    '    \"object\": string  // the target object type\\n' \\\n", "    '    \"color\": string  // target object color\\n' \\\n", "    '}}\\n' \\\n", "    '```\\n'\\\n", "    \"Exmaple 1: What is the object and color in the goal if goal of the agent is: 'Go to the green square'. Answer: \\n\" \\\n", "    '{{\\n' \\\n", "    '    \"object\": \"square\" \\n' \\\n", "    '    \"color\": \"green\" \\n' \\\n", "    '}}\\n' \\\n", "    \"Exmaple 2: What is the object and color in the goal if goal of the agent is: 'Go to the blue key'. Answer: \\n\" \\\n", "    '{{\\n' \\\n", "    '    \"object\": \"key\" \\n' \\\n", "    '    \"color\": \"blue\" \\n' \\\n", "    '}}\\n' \\\n", "  \n", "\n", "\n", "\n", "prompt = PromptTemplate(\n", "template=prompt_template+\"\\n What is the object and color in the goal if goal of the agent is: 'Go to the {clr} {obj}'\",\n", "input_variables=[\"clr\", \"obj\"],\n", "partial_variables={\"format_instructions\": format_instructions},\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["questions = [{\"clr\": clr, \"obj\": obj} for clr in color_map.values() for obj in goal_map.values()]\n", "print(questions)\n", "res = (prompt | llm).batch(questions)\n", "print(res)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["parsed = []\n", "for r in tqdm(res):\n", "    try:\n", "        parsed.append(output_parser.invoke(r))\n", "    except:\n", "        continue"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["correct_count = 0\n", "parsed_responses = []\n", "\n", "for r, q in tqdm(zip(res, questions), total=len(questions)):\n", "    print(r, q)\n", "    parsed = output_parser.parse(r)\n", "    parsed_responses.append(parsed)\n", "\n", "    # Check if the parsed response matches the input question\n", "    if parsed[\"color\"] == q[\"clr\"] and parsed[\"object\"] == q[\"obj\"]:\n", "        correct_count += 1\n", "    # except Exception as e:\n", "    #     print(f\"Failed to parse: {r}, Error: {e}\")   \n", "\n", "print(f\"Correctly parsed responses: {correct_count}/{len(questions)}\")       "]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 4}