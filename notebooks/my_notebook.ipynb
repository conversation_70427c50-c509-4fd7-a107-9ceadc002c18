{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!module load openmpi\n", "!module load cuda/12.1.1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ['XLA_PYTHON_CLIENT_PREALLOCATE'] = 'false'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import math\n", "from tqdm import tqdm\n", "import jax\n", "from jax import numpy as jnp\n", "import torch\n", "from transformers import AutoTokenizer, BertForMaskedLM, BertForPreTraining\n", "\n", "from xminigrid.benchmarks import load_bz2_pickle\n", "import xminigrid\n", "from train_meta_task import my_load_benchmark\n", "from rich import print"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rng = jax.random.<PERSON><PERSON><PERSON><PERSON>(42)\n", "ruleset = benchmark.get_ruleset(1)\n", "env_params = env_params.replace(ruleset=ruleset)\n", "timestep = env.reset(env_params, rng)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["goal_map = {\n", "            5: 'ball',\n", "            6: 'square',\n", "            7: 'pyramid',\n", "            8: 'goal',\n", "            9: 'key',\n", "            10: 'locked door',\n", "            11: 'closed door',\n", "            12: 'open door',\n", "            13: 'hex',\n", "            14: 'star'\n", "        }\n", "color_map = {\n", "            3: 'red',\n", "            4: 'green',\n", "            5: 'blue',\n", "            6: 'purple',\n", "            7: 'yellow',\n", "            8: 'grey',\n", "            9: 'black',\n", "            10: 'orange',\n", "            11: 'white',\n", "            12: 'brown',\n", "            13: 'pink'\n", "        }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pattern = \\\n", "            \"answer the users question as best as possible. \" \\\n", "            \"You are in the 4 rooms flat gridworld with 2 column and 2 rows of rooms. \" \\\n", "            \"Adjacent rooms are connected with doors. \" \\\n", "            \"these are the object in the environment: \".join([f'{obj} , ' for obj in goal_map])\\\n", "            .join(f\"and there are the colors in the environment \").join([f'{clr} , ' for clr in color_map])\\\n", "            .join(\"Goal of the agent: Go to the {clr} {objt}.\\n\" \\\n", "            \"Provide the instruction for the agent. \" \\\n", "            'The output should be a markdown code snippet formatted in the following schema, ' \\\n", "            'including the leading and trailing \"```json\" and \"```\":\\n' \\\n", "            '```json\\n' \\\n", "            '{{\\n' \\\n", "            '    \"object\": string  // the target object type\\n' \\\n", "            '    \"color\": string  // target object color\\n' \\\n", "            '}}\\n' \\\n", "            '```\\n' \\\n", "            'Instruction step 1:')\n", "\n", "print(pattern)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["[a]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# pip install accelerate\n", "from transformers import AutoTokenizer, AutoModelForCausalLM\n", "import os\n", "import torch\n", "tokenizer = AutoTokenizer.from_pretrained(\"google/gemma-2b-it\",cache_dir=os.environ['SCRATCH'] + '/hf')\n", "model = AutoModelForCausalLM.from_pretrained(\"google/gemma-2b-it\", device_map=\"auto\", torch_dtype=torch.bfloat16, cache_dir=os.environ['SCRATCH'] + '/hf')\n", "\n", "input_text = \"Write me a poem about Machine Learning.\"\n", "input_ids = tokenizer(input_text, return_tensors=\"pt\").to(\"cuda\")\n", "\n", "outputs = model.generate(**input_ids)\n", "print(tokenizer.decode(outputs[0]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prompt = "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inputs = tokenizer([prompt], return_tensors=\"pt\")\n", "gen_ids = model.generate(inputs[\"input_ids\"].cuda(), do_sample=True, \n", "                      max_new_tokens=20, top_k=0, temperature=1.4, num_return_sequences=20)\n", "texts = tokenizer.batch_decode(gen_ids[:, inputs[\"input_ids\"].shape[1]:], \n", "                               skip_special_tokens=True, clean_up_tokenization_spaces=False)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 4}