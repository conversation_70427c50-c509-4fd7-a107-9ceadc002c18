{"cells": [{"cell_type": "code", "execution_count": 2, "id": "be1641a2-a169-46b7-b64e-cf47ff214729", "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ['XLA_PYTHON_CLIENT_PREALLOCATE'] = 'false'"]}, {"cell_type": "code", "execution_count": 1, "id": "b1198fdf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[=== Module openmpi/4.0.4 loaded ===]\n", "[=== Module cudatoolkit/12.1.1 loaded ===]\n"]}], "source": ["!module load openmpi\n", "!module load cuda/12.1.1"]}, {"cell_type": "code", "execution_count": 9, "id": "cae51990-9238-4c13-9973-f324b43a1287", "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'train_meta_task'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[9], line 11\u001b[0m\n\u001b[1;32m      9\u001b[0m \u001b[39mfrom\u001b[39;00m \u001b[39mxminigrid\u001b[39;00m\u001b[39m.\u001b[39;00m\u001b[39mbenchmarks\u001b[39;00m \u001b[39mimport\u001b[39;00m load_bz2_pickle\n\u001b[1;32m     10\u001b[0m \u001b[39mimport\u001b[39;00m \u001b[39mxminigrid\u001b[39;00m\n\u001b[0;32m---> 11\u001b[0m \u001b[39mfrom\u001b[39;00m \u001b[39mtrain_meta_task\u001b[39;00m \u001b[39mimport\u001b[39;00m my_load_benchmark\n\u001b[1;32m     12\u001b[0m \u001b[39mfrom\u001b[39;00m \u001b[39mrich\u001b[39;00m \u001b[39mimport\u001b[39;00m \u001b[39mprint\u001b[39m\n", "\u001b[0;31mModuleNotFoundError\u001b[0m: No module named 'train_meta_task'"]}], "source": ["import numpy as np\n", "import math\n", "from tqdm import tqdm\n", "import jax\n", "from jax import numpy as jnp\n", "import torch\n", "from transformers import AutoTokenizer, BertForMaskedLM, BertForPreTraining\n", "\n", "from xminigrid.benchmarks import load_bz2_pickle\n", "import xminigrid\n", "from train_meta_task import my_load_benchmark\n", "from rich import print"]}, {"cell_type": "code", "execution_count": 4, "id": "79c0ed99-3854-424d-b6ff-48dc1dd12bba", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'my_load_benchmark' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[4], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m env, env_params \u001b[39m=\u001b[39m xminigrid\u001b[39m.\u001b[39mmake(\u001b[39m\"\u001b[39m\u001b[39mXLand-MiniGrid-R4-9x9\u001b[39m\u001b[39m\"\u001b[39m)\n\u001b[0;32m----> 2\u001b[0m benchmark \u001b[39m=\u001b[39m my_load_benchmark(\u001b[39m\"\u001b[39m\u001b[39mmy_trivial_1m_v2\u001b[39m\u001b[39m\"\u001b[39m)\n", "\u001b[0;31mNameError\u001b[0m: name 'my_load_benchmark' is not defined"]}], "source": ["env, env_params = xminigrid.make(\"XLand-MiniGrid-R4-9x9\")\n", "benchmark = my_load_benchmark(\"my_trivial_1m_v2\")"]}, {"cell_type": "code", "execution_count": 53, "id": "9f4dd7a8-de4c-40f0-974d-395431ab3d5a", "metadata": {}, "outputs": [], "source": ["rng = jax.random.<PERSON><PERSON><PERSON><PERSON>(42)\n", "ruleset = benchmark.get_ruleset(1)\n", "env_params = env_params.replace(ruleset=ruleset)\n", "timestep = env.reset(env_params, rng)"]}, {"cell_type": "code", "execution_count": 54, "id": "bf3c64db-5ae7-48de-a615-d3c2db5a97df", "metadata": {}, "outputs": [], "source": ["goal_map = {\n", "    5: 'ball',\n", "    6: 'square',\n", "    7: 'pyramid',\n", "    8: 'goal',\n", "    9: 'key',\n", "    10: 'locked door',\n", "    11: 'closed door',\n", "    12: 'open door',\n", "    13: 'hex',\n", "    14: 'star'\n", "}\n", "\n", "color_map = {\n", "    3: 'red',\n", "    4: 'green',\n", "    5: 'blue',\n", "    6: 'purple',\n", "    7: 'yellow',\n", "    8: 'grey',\n", "    9: 'black',\n", "    10: 'orange',\n", "    11: 'white',\n", "    12: 'brown',\n", "    13: 'pink'\n", "}"]}, {"cell_type": "code", "execution_count": 55, "id": "ec005965-0d02-4530-a66f-c936ed37d629", "metadata": {}, "outputs": [], "source": ["# print(env.render(env_params.replace(render_mode='rich_text'), timestep))"]}, {"cell_type": "code", "execution_count": 56, "id": "8b72f979-5779-402c-b959-008fd283e5d2", "metadata": {}, "outputs": [], "source": ["c = ((timestep.state.grid[..., 0] != 4) & (timestep.state.grid[..., 0] != 3) &\n", "     (timestep.state.grid[..., 0] != 11) & (timestep.state.grid[..., 0] != 10) &\n", "     (timestep.state.grid[..., 1] != 8) & (timestep.state.grid[..., 1] != 9)).nonzero()"]}, {"cell_type": "code", "execution_count": null, "id": "fe9483dc-b6d0-4d97-aa41-707db14dcc5b", "metadata": {"scrolled": true}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 57, "id": "4f8c4579-ce2c-4aad-a8dc-24020a2cadbb", "metadata": {}, "outputs": [], "source": ["info = []\n", "agent_pos = np.array(timestep.state.agent.position)\n", "for o, x, y in zip(np.array(timestep.state.grid[c]), *c):\n", "    info.append((goal_map[o[0]], color_map[o[1]], x.item(), y.item()))"]}, {"cell_type": "code", "execution_count": 58, "id": "0cc4bee9-c292-4c74-97b0-8c23529ad761", "metadata": {}, "outputs": [{"data": {"text/plain": ["[('square', 'yellow', 2, 5), ('hex', 'blue', 2, 7), ('star', 'purple', 6, 2)]"]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["info"]}, {"cell_type": "code", "execution_count": 59, "id": "1a33ca8d-e10b-48ee-925f-373a052ec52f", "metadata": {}, "outputs": [{"data": {"text/plain": ["Array([6, 7], dtype=uint8)"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["ruleset.goal[1:3]"]}, {"cell_type": "code", "execution_count": 100, "id": "947983b4-1d89-4ddb-94eb-11dd77eaf88c", "metadata": {"scrolled": true}, "outputs": [], "source": ["prompt = (f\"You are in the 4 rooms flat\" +\n", "          f\" gridworld with 2 column and 2 rows of rooms. \" +\n", "          # f\"Your position within the grid is ({agent_pos[0]}, {agent_pos[1]}).\" +\n", "          \" Adjacent rooms are connected with doors. \" +\n", "          \"Each room is 3 cells wide and 3 cells high. \") + \\\n", "          ''.join([f'The {col} {obj} is in the grid. ' for obj, col, x, y in info])"]}, {"cell_type": "code", "execution_count": 101, "id": "3370e16b-857b-4700-a04f-9818d8a43190", "metadata": {}, "outputs": [], "source": ["gobj, gcol = ruleset.goal[1:3]\n", "prompt = f'The gridworld environment desciprion: \\n{prompt}\\n' \\\n", "         f'Goal of the agent: Go to the {color_map[gcol.item()]} {goal_map[gobj.item()]}.\\n' \\\n", "         f'Break down the overall goal into short high-level step-by-step ' \\\n", "         f'instruction for the agent in the gridworld environment.\\n' \\\n", "         f'Instruction step 1: '"]}, {"cell_type": "code", "execution_count": 102, "id": "0acb715e-1659-43c3-a073-b5cd8d5b1fd9", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">The gridworld environment desciprion: \n", "You are in the <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4</span> rooms flat gridworld with <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span> column and <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span> rows of rooms.  Adjacent rooms are connected with doors. \n", "Each room is <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span> cells wide and <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span> cells high. The yellow square is in the grid. The blue hex is in the grid. The \n", "purple star is in the grid. \n", "Goal of the agent: Go to the yellow square.\n", "Break down the overall goal into short high-level step-by-step instruction for the agent in the gridworld \n", "environment.\n", "Instruction step <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>: \n", "</pre>\n"], "text/plain": ["The gridworld environment desciprion: \n", "You are in the \u001b[1;36m4\u001b[0m rooms flat gridworld with \u001b[1;36m2\u001b[0m column and \u001b[1;36m2\u001b[0m rows of rooms.  Adjacent rooms are connected with doors. \n", "Each room is \u001b[1;36m3\u001b[0m cells wide and \u001b[1;36m3\u001b[0m cells high. The yellow square is in the grid. The blue hex is in the grid. The \n", "purple star is in the grid. \n", "Goal of the agent: Go to the yellow square.\n", "Break down the overall goal into short high-level step-by-step instruction for the agent in the gridworld \n", "environment.\n", "Instruction step \u001b[1;36m1\u001b[0m: \n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(prompt)"]}, {"cell_type": "code", "execution_count": 21, "id": "2fa7e9da-4573-44d6-ba09-2006be2d9d86", "metadata": {}, "outputs": [], "source": ["from transformers import AutoTokenizer, FlaxT5ForConditionalGeneration, T5ForConditionalGeneration, \\\n", "    FlaxGemmaForCausalLM, GemmaForCausalLM \n", "import os"]}, {"cell_type": "code", "execution_count": 22, "id": "34b4868c-29c1-47e6-bfe6-d1a06e08318f", "metadata": {}, "outputs": [{"data": {"text/plain": ["'/network/scratch/m/maryam.hashemzadeh'"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["os.environ['SCRATCH']"]}, {"cell_type": "code", "execution_count": 23, "id": "f24fa2f2-590b-4685-ad9d-723da3b8c08e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Loading checkpoint shards: 100%|██████████| 2/2 [00:04<00:00,  2.24s/it]\n"]}, {"ename": "DeferredCudaCallError", "evalue": "CUDA call failed lazily at initialization with error: device >= 0 && device < num_gpus INTERNAL ASSERT FAILED at \"../aten/src/ATen/cuda/CUDAContext.cpp\":50, please report a bug to PyTorch. device=1, num_gpus=\u0001\n\nCUDA call was originally invoked at:\n\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/runpy.py\", line 196, in _run_module_as_main\n    return _run_code(code, main_globals, None,\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/runpy.py\", line 86, in _run_code\n    exec(code, run_globals)\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel_launcher.py\", line 18, in <module>\n    app.launch_new_instance()\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/traitlets/config/application.py\", line 1075, in launch_instance\n    app.start()\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel/kernelapp.py\", line 739, in start\n    self.io_loop.start()\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/tornado/platform/asyncio.py\", line 205, in start\n    self.asyncio_loop.run_forever()\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/asyncio/base_events.py\", line 603, in run_forever\n    self._run_once()\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/asyncio/base_events.py\", line 1909, in _run_once\n    handle._run()\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/asyncio/events.py\", line 80, in _run\n    self._context.run(self._callback, *self._args)\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 545, in dispatch_queue\n    await self.process_one()\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 534, in process_one\n    await dispatch(*args)\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 437, in dispatch_shell\n    await result\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 362, in execute_request\n    await super().execute_request(stream, ident, parent)\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 778, in execute_request\n    reply_content = await reply_content\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 449, in do_execute\n    res = shell.run_cell(\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel/zmqshell.py\", line 549, in run_cell\n    return super().run_cell(*args, **kwargs)\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3075, in run_cell\n    result = self._run_cell(\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3130, in _run_cell\n    result = runner(coro)\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/IPython/core/async_helpers.py\", line 128, in _pseudo_sync_runner\n    coro.send(None)\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3334, in run_cell_async\n    has_raised = await self.run_ast_nodes(code_ast.body, cell_name,\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3517, in run_ast_nodes\n    if await self.run_code(code, result, async_=asy):\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3577, in run_code\n    exec(code_obj, self.user_global_ns, self.user_ns)\n  File \"/tmp/ipykernel_2303906/1412134564.py\", line 6, in <module>\n    import torch\n  File \"<frozen importlib._bootstrap>\", line 1027, in _find_and_load\n  File \"<frozen importlib._bootstrap>\", line 1006, in _find_and_load_unlocked\n  File \"<frozen importlib._bootstrap>\", line 688, in _load_unlocked\n  File \"<frozen importlib._bootstrap_external>\", line 883, in exec_module\n  File \"<frozen importlib._bootstrap>\", line 241, in _call_with_frames_removed\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/torch/__init__.py\", line 1332, in <module>\n    _C._initExtension(manager_path())\n  File \"<frozen importlib._bootstrap>\", line 1027, in _find_and_load\n  File \"<frozen importlib._bootstrap>\", line 1006, in _find_and_load_unlocked\n  File \"<frozen importlib._bootstrap>\", line 688, in _load_unlocked\n  File \"<frozen importlib._bootstrap_external>\", line 883, in exec_module\n  File \"<frozen importlib._bootstrap>\", line 241, in _call_with_frames_removed\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/torch/cuda/__init__.py\", line 244, in <module>\n    _lazy_call(_check_capability)\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/torch/cuda/__init__.py\", line 241, in _lazy_call\n    _queued_calls.append((callable, traceback.format_stack()))\n", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mRuntimeError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "File \u001b[0;32m/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/torch/cuda/__init__.py:311\u001b[0m, in \u001b[0;36m_lazy_init\u001b[0;34m()\u001b[0m\n\u001b[1;32m    310\u001b[0m \u001b[39mtry\u001b[39;00m:\n\u001b[0;32m--> 311\u001b[0m     queued_call()\n\u001b[1;32m    312\u001b[0m \u001b[39mexcept\u001b[39;00m \u001b[39mException\u001b[39;00m \u001b[39mas\u001b[39;00m e:\n", "File \u001b[0;32m/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/torch/cuda/__init__.py:180\u001b[0m, in \u001b[0;36m_check_capability\u001b[0;34m()\u001b[0m\n\u001b[1;32m    179\u001b[0m \u001b[39mfor\u001b[39;00m d \u001b[39min\u001b[39;00m \u001b[39mrange\u001b[39m(device_count()):\n\u001b[0;32m--> 180\u001b[0m     capability \u001b[39m=\u001b[39m get_device_capability(d)\n\u001b[1;32m    181\u001b[0m     major \u001b[39m=\u001b[39m capability[\u001b[39m0\u001b[39m]\n", "File \u001b[0;32m/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/torch/cuda/__init__.py:435\u001b[0m, in \u001b[0;36mget_device_capability\u001b[0;34m(device)\u001b[0m\n\u001b[1;32m    423\u001b[0m \u001b[39m\u001b[39m\u001b[39mr\u001b[39m\u001b[39m\"\"\"Gets the cuda capability of a device.\u001b[39;00m\n\u001b[1;32m    424\u001b[0m \n\u001b[1;32m    425\u001b[0m \u001b[39mArgs:\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    433\u001b[0m \u001b[39m    tuple(int, int): the major and minor cuda capability of the device\u001b[39;00m\n\u001b[1;32m    434\u001b[0m \u001b[39m\"\"\"\u001b[39;00m\n\u001b[0;32m--> 435\u001b[0m prop \u001b[39m=\u001b[39m get_device_properties(device)\n\u001b[1;32m    436\u001b[0m \u001b[39mreturn\u001b[39;00m prop\u001b[39m.\u001b[39mmajor, prop\u001b[39m.\u001b[39mminor\n", "File \u001b[0;32m/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/torch/cuda/__init__.py:453\u001b[0m, in \u001b[0;36mget_device_properties\u001b[0;34m(device)\u001b[0m\n\u001b[1;32m    452\u001b[0m     \u001b[39mraise\u001b[39;00m \u001b[39mAssertionError\u001b[39;00m(\u001b[39m\"\u001b[39m\u001b[39mInvalid device id\u001b[39m\u001b[39m\"\u001b[39m)\n\u001b[0;32m--> 453\u001b[0m \u001b[39mreturn\u001b[39;00m _get_device_properties(device)\n", "\u001b[0;31mRuntimeError\u001b[0m: device >= 0 && device < num_gpus INTERNAL ASSERT FAILED at \"../aten/src/ATen/cuda/CUDAContext.cpp\":50, please report a bug to PyTorch. device=1, num_gpus=\u0001", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[0;31m<PERSON>eferredCudaCallError\u001b[0m                     <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[23], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m tokenizer \u001b[39m=\u001b[39m AutoTokenizer\u001b[39m.\u001b[39mfrom_pretrained(\u001b[39m\"\u001b[39m\u001b[39mgoogle/gemma-2b-it\u001b[39m\u001b[39m\"\u001b[39m, cache_dir\u001b[39m=\u001b[39mos\u001b[39m.\u001b[39menviron[\u001b[39m'\u001b[39m\u001b[39mSCRATCH\u001b[39m\u001b[39m'\u001b[39m] \u001b[39m+\u001b[39m \u001b[39m'\u001b[39m\u001b[39m/hf\u001b[39m\u001b[39m'\u001b[39m)\n\u001b[0;32m----> 2\u001b[0m model \u001b[39m=\u001b[39m GemmaForCausalLM\u001b[39m.\u001b[39;49mfrom_pretrained(\u001b[39m\"\u001b[39;49m\u001b[39mgoogle/gemma-2b-it\u001b[39;49m\u001b[39m\"\u001b[39;49m, cache_dir\u001b[39m=\u001b[39;49mos\u001b[39m.\u001b[39;49menviron[\u001b[39m'\u001b[39;49m\u001b[39mSCRATCH\u001b[39;49m\u001b[39m'\u001b[39;49m] \u001b[39m+\u001b[39;49m \u001b[39m'\u001b[39;49m\u001b[39m/hf\u001b[39;49m\u001b[39m'\u001b[39;49m)\u001b[39m.\u001b[39;49mcuda()\n", "File \u001b[0;32m/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/transformers/modeling_utils.py:2694\u001b[0m, in \u001b[0;36mPreTrainedModel.cuda\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   2689\u001b[0m     \u001b[39mraise\u001b[39;00m \u001b[39mValueError\u001b[39;00m(\n\u001b[1;32m   2690\u001b[0m         \u001b[39m\"\u001b[39m\u001b[39mCalling `cuda()` is not supported for `4-bit` or `8-bit` quantized models. Please use the model as it is, since the\u001b[39m\u001b[39m\"\u001b[39m\n\u001b[1;32m   2691\u001b[0m         \u001b[39m\"\u001b[39m\u001b[39m model has already been set to the correct devices and casted to the correct `dtype`.\u001b[39m\u001b[39m\"\u001b[39m\n\u001b[1;32m   2692\u001b[0m     )\n\u001b[1;32m   2693\u001b[0m \u001b[39melse\u001b[39;00m:\n\u001b[0;32m-> 2694\u001b[0m     \u001b[39mreturn\u001b[39;00m \u001b[39msuper\u001b[39;49m()\u001b[39m.\u001b[39;49mcuda(\u001b[39m*\u001b[39;49margs, \u001b[39m*\u001b[39;49m\u001b[39m*\u001b[39;49mkwargs)\n", "File \u001b[0;32m/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/torch/nn/modules/module.py:918\u001b[0m, in \u001b[0;36mModule.cuda\u001b[0;34m(self, device)\u001b[0m\n\u001b[1;32m    901\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mcuda\u001b[39m(\u001b[39mself\u001b[39m: T, device: Optional[Union[\u001b[39mint\u001b[39m, device]] \u001b[39m=\u001b[39m \u001b[39mNone\u001b[39;00m) \u001b[39m-\u001b[39m\u001b[39m>\u001b[39m T:\n\u001b[1;32m    902\u001b[0m \u001b[39m    \u001b[39m\u001b[39mr\u001b[39m\u001b[39m\"\"\"Moves all model parameters and buffers to the GPU.\u001b[39;00m\n\u001b[1;32m    903\u001b[0m \n\u001b[1;32m    904\u001b[0m \u001b[39m    This also makes associated parameters and buffers different objects. So\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    916\u001b[0m \u001b[39m        Module: self\u001b[39;00m\n\u001b[1;32m    917\u001b[0m \u001b[39m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 918\u001b[0m     \u001b[39mreturn\u001b[39;00m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_apply(\u001b[39mlambda\u001b[39;49;00m t: t\u001b[39m.\u001b[39;49mcuda(device))\n", "File \u001b[0;32m/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/torch/nn/modules/module.py:810\u001b[0m, in \u001b[0;36mModule._apply\u001b[0;34m(self, fn, recurse)\u001b[0m\n\u001b[1;32m    808\u001b[0m \u001b[39mif\u001b[39;00m recurse:\n\u001b[1;32m    809\u001b[0m     \u001b[39mfor\u001b[39;00m module \u001b[39min\u001b[39;00m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mchildren():\n\u001b[0;32m--> 810\u001b[0m         module\u001b[39m.\u001b[39;49m_apply(fn)\n\u001b[1;32m    812\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mcompute_should_use_set_data\u001b[39m(tensor, tensor_applied):\n\u001b[1;32m    813\u001b[0m     \u001b[39mif\u001b[39;00m torch\u001b[39m.\u001b[39m_has_compatible_shallow_copy_type(tensor, tensor_applied):\n\u001b[1;32m    814\u001b[0m         \u001b[39m# If the new tensor has compatible tensor type as the existing tensor,\u001b[39;00m\n\u001b[1;32m    815\u001b[0m         \u001b[39m# the current behavior is to change the tensor in-place using `.data =`,\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    820\u001b[0m         \u001b[39m# global flag to let the user control whether they want the future\u001b[39;00m\n\u001b[1;32m    821\u001b[0m         \u001b[39m# behavior of overwriting the existing tensor or not.\u001b[39;00m\n", "File \u001b[0;32m/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/torch/nn/modules/module.py:810\u001b[0m, in \u001b[0;36mModule._apply\u001b[0;34m(self, fn, recurse)\u001b[0m\n\u001b[1;32m    808\u001b[0m \u001b[39mif\u001b[39;00m recurse:\n\u001b[1;32m    809\u001b[0m     \u001b[39mfor\u001b[39;00m module \u001b[39min\u001b[39;00m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mchildren():\n\u001b[0;32m--> 810\u001b[0m         module\u001b[39m.\u001b[39;49m_apply(fn)\n\u001b[1;32m    812\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mcompute_should_use_set_data\u001b[39m(tensor, tensor_applied):\n\u001b[1;32m    813\u001b[0m     \u001b[39mif\u001b[39;00m torch\u001b[39m.\u001b[39m_has_compatible_shallow_copy_type(tensor, tensor_applied):\n\u001b[1;32m    814\u001b[0m         \u001b[39m# If the new tensor has compatible tensor type as the existing tensor,\u001b[39;00m\n\u001b[1;32m    815\u001b[0m         \u001b[39m# the current behavior is to change the tensor in-place using `.data =`,\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    820\u001b[0m         \u001b[39m# global flag to let the user control whether they want the future\u001b[39;00m\n\u001b[1;32m    821\u001b[0m         \u001b[39m# behavior of overwriting the existing tensor or not.\u001b[39;00m\n", "File \u001b[0;32m/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/torch/nn/modules/module.py:833\u001b[0m, in \u001b[0;36mModule._apply\u001b[0;34m(self, fn, recurse)\u001b[0m\n\u001b[1;32m    829\u001b[0m \u001b[39m# Tensors stored in modules are graph leaves, and we don't want to\u001b[39;00m\n\u001b[1;32m    830\u001b[0m \u001b[39m# track autograd history of `param_applied`, so we have to use\u001b[39;00m\n\u001b[1;32m    831\u001b[0m \u001b[39m# `with torch.no_grad():`\u001b[39;00m\n\u001b[1;32m    832\u001b[0m \u001b[39mwith\u001b[39;00m torch\u001b[39m.\u001b[39mno_grad():\n\u001b[0;32m--> 833\u001b[0m     param_applied \u001b[39m=\u001b[39m fn(param)\n\u001b[1;32m    834\u001b[0m should_use_set_data \u001b[39m=\u001b[39m compute_should_use_set_data(param, param_applied)\n\u001b[1;32m    835\u001b[0m \u001b[39mif\u001b[39;00m should_use_set_data:\n", "File \u001b[0;32m/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/torch/nn/modules/module.py:918\u001b[0m, in \u001b[0;36mModule.cuda.<locals>.<lambda>\u001b[0;34m(t)\u001b[0m\n\u001b[1;32m    901\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mcuda\u001b[39m(\u001b[39mself\u001b[39m: T, device: Optional[Union[\u001b[39mint\u001b[39m, device]] \u001b[39m=\u001b[39m \u001b[39mNone\u001b[39;00m) \u001b[39m-\u001b[39m\u001b[39m>\u001b[39m T:\n\u001b[1;32m    902\u001b[0m \u001b[39m    \u001b[39m\u001b[39mr\u001b[39m\u001b[39m\"\"\"Moves all model parameters and buffers to the GPU.\u001b[39;00m\n\u001b[1;32m    903\u001b[0m \n\u001b[1;32m    904\u001b[0m \u001b[39m    This also makes associated parameters and buffers different objects. So\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    916\u001b[0m \u001b[39m        Module: self\u001b[39;00m\n\u001b[1;32m    917\u001b[0m \u001b[39m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 918\u001b[0m     \u001b[39mreturn\u001b[39;00m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_apply(\u001b[39mlambda\u001b[39;00m t: t\u001b[39m.\u001b[39;49mcuda(device))\n", "File \u001b[0;32m/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/torch/cuda/__init__.py:317\u001b[0m, in \u001b[0;36m_lazy_init\u001b[0;34m()\u001b[0m\n\u001b[1;32m    312\u001b[0m         \u001b[39mexcept\u001b[39;00m \u001b[39mException\u001b[39;00m \u001b[39mas\u001b[39;00m e:\n\u001b[1;32m    313\u001b[0m             msg \u001b[39m=\u001b[39m (\n\u001b[1;32m    314\u001b[0m                 \u001b[39mf\u001b[39m\u001b[39m\"\u001b[39m\u001b[39mCUDA call failed lazily at initialization with error: \u001b[39m\u001b[39m{\u001b[39;00m\u001b[39mstr\u001b[39m(e)\u001b[39m}\u001b[39;00m\u001b[39m\\n\u001b[39;00m\u001b[39m\\n\u001b[39;00m\u001b[39m\"\u001b[39m\n\u001b[1;32m    315\u001b[0m                 \u001b[39mf\u001b[39m\u001b[39m\"\u001b[39m\u001b[39mCUDA call was originally invoked at:\u001b[39m\u001b[39m\\n\u001b[39;00m\u001b[39m\\n\u001b[39;00m\u001b[39m{\u001b[39;00m\u001b[39m'\u001b[39m\u001b[39m'\u001b[39m\u001b[39m.\u001b[39mjoin(orig_traceback)\u001b[39m}\u001b[39;00m\u001b[39m\"\u001b[39m\n\u001b[1;32m    316\u001b[0m             )\n\u001b[0;32m--> 317\u001b[0m             \u001b[39mraise\u001b[39;00m DeferredCudaCallError(msg) \u001b[39mfrom\u001b[39;00m \u001b[39me\u001b[39;00m\n\u001b[1;32m    318\u001b[0m \u001b[39mfinally\u001b[39;00m:\n\u001b[1;32m    319\u001b[0m     \u001b[39mdelattr\u001b[39m(_tls, \u001b[39m\"\u001b[39m\u001b[39mis_initializing\u001b[39m\u001b[39m\"\u001b[39m)\n", "\u001b[0;31mDeferredCudaCallError\u001b[0m: CUDA call failed lazily at initialization with error: device >= 0 && device < num_gpus INTERNAL ASSERT FAILED at \"../aten/src/ATen/cuda/CUDAContext.cpp\":50, please report a bug to PyTorch. device=1, num_gpus=\u0001\n\nCUDA call was originally invoked at:\n\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/runpy.py\", line 196, in _run_module_as_main\n    return _run_code(code, main_globals, None,\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/runpy.py\", line 86, in _run_code\n    exec(code, run_globals)\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel_launcher.py\", line 18, in <module>\n    app.launch_new_instance()\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/traitlets/config/application.py\", line 1075, in launch_instance\n    app.start()\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel/kernelapp.py\", line 739, in start\n    self.io_loop.start()\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/tornado/platform/asyncio.py\", line 205, in start\n    self.asyncio_loop.run_forever()\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/asyncio/base_events.py\", line 603, in run_forever\n    self._run_once()\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/asyncio/base_events.py\", line 1909, in _run_once\n    handle._run()\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/asyncio/events.py\", line 80, in _run\n    self._context.run(self._callback, *self._args)\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 545, in dispatch_queue\n    await self.process_one()\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 534, in process_one\n    await dispatch(*args)\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 437, in dispatch_shell\n    await result\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 362, in execute_request\n    await super().execute_request(stream, ident, parent)\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 778, in execute_request\n    reply_content = await reply_content\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 449, in do_execute\n    res = shell.run_cell(\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel/zmqshell.py\", line 549, in run_cell\n    return super().run_cell(*args, **kwargs)\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3075, in run_cell\n    result = self._run_cell(\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3130, in _run_cell\n    result = runner(coro)\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/IPython/core/async_helpers.py\", line 128, in _pseudo_sync_runner\n    coro.send(None)\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3334, in run_cell_async\n    has_raised = await self.run_ast_nodes(code_ast.body, cell_name,\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3517, in run_ast_nodes\n    if await self.run_code(code, result, async_=asy):\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3577, in run_code\n    exec(code_obj, self.user_global_ns, self.user_ns)\n  File \"/tmp/ipykernel_2303906/1412134564.py\", line 6, in <module>\n    import torch\n  File \"<frozen importlib._bootstrap>\", line 1027, in _find_and_load\n  File \"<frozen importlib._bootstrap>\", line 1006, in _find_and_load_unlocked\n  File \"<frozen importlib._bootstrap>\", line 688, in _load_unlocked\n  File \"<frozen importlib._bootstrap_external>\", line 883, in exec_module\n  File \"<frozen importlib._bootstrap>\", line 241, in _call_with_frames_removed\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/torch/__init__.py\", line 1332, in <module>\n    _C._initExtension(manager_path())\n  File \"<frozen importlib._bootstrap>\", line 1027, in _find_and_load\n  File \"<frozen importlib._bootstrap>\", line 1006, in _find_and_load_unlocked\n  File \"<frozen importlib._bootstrap>\", line 688, in _load_unlocked\n  File \"<frozen importlib._bootstrap_external>\", line 883, in exec_module\n  File \"<frozen importlib._bootstrap>\", line 241, in _call_with_frames_removed\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/torch/cuda/__init__.py\", line 244, in <module>\n    _lazy_call(_check_capability)\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/torch/cuda/__init__.py\", line 241, in _lazy_call\n    _queued_calls.append((callable, traceback.format_stack()))\n"]}], "source": ["tokenizer = AutoTokenizer.from_pretrained(\"google/gemma-2b-it\", cache_dir=os.environ['SCRATCH'] + '/hf')\n", "model = GemmaForCausalLM.from_pretrained(\"google/gemma-2b-it\", cache_dir=os.environ['SCRATCH'] + '/hf').cuda()"]}, {"cell_type": "code", "execution_count": 17, "id": "82f72d99-7d13-4e48-b31f-5665212d0cfd", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Downloading shards: 100%|██████████| 2/2 [04:29<00:00, 134.90s/it]\n", "Loading checkpoint shards: 100%|██████████| 2/2 [00:11<00:00,  5.72s/it]\n"]}, {"ename": "DeferredCudaCallError", "evalue": "CUDA call failed lazily at initialization with error: device >= 0 && device < num_gpus INTERNAL ASSERT FAILED at \"../aten/src/ATen/cuda/CUDAContext.cpp\":50, please report a bug to PyTorch. device=1, num_gpus=\u0001\n\nCUDA call was originally invoked at:\n\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/runpy.py\", line 196, in _run_module_as_main\n    return _run_code(code, main_globals, None,\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/runpy.py\", line 86, in _run_code\n    exec(code, run_globals)\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel_launcher.py\", line 18, in <module>\n    app.launch_new_instance()\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/traitlets/config/application.py\", line 1075, in launch_instance\n    app.start()\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel/kernelapp.py\", line 739, in start\n    self.io_loop.start()\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/tornado/platform/asyncio.py\", line 205, in start\n    self.asyncio_loop.run_forever()\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/asyncio/base_events.py\", line 603, in run_forever\n    self._run_once()\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/asyncio/base_events.py\", line 1909, in _run_once\n    handle._run()\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/asyncio/events.py\", line 80, in _run\n    self._context.run(self._callback, *self._args)\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 545, in dispatch_queue\n    await self.process_one()\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 534, in process_one\n    await dispatch(*args)\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 437, in dispatch_shell\n    await result\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 362, in execute_request\n    await super().execute_request(stream, ident, parent)\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 778, in execute_request\n    reply_content = await reply_content\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 449, in do_execute\n    res = shell.run_cell(\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel/zmqshell.py\", line 549, in run_cell\n    return super().run_cell(*args, **kwargs)\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3075, in run_cell\n    result = self._run_cell(\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3130, in _run_cell\n    result = runner(coro)\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/IPython/core/async_helpers.py\", line 128, in _pseudo_sync_runner\n    coro.send(None)\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3334, in run_cell_async\n    has_raised = await self.run_ast_nodes(code_ast.body, cell_name,\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3517, in run_ast_nodes\n    if await self.run_code(code, result, async_=asy):\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3577, in run_code\n    exec(code_obj, self.user_global_ns, self.user_ns)\n  File \"/tmp/ipykernel_2303906/1412134564.py\", line 6, in <module>\n    import torch\n  File \"<frozen importlib._bootstrap>\", line 1027, in _find_and_load\n  File \"<frozen importlib._bootstrap>\", line 1006, in _find_and_load_unlocked\n  File \"<frozen importlib._bootstrap>\", line 688, in _load_unlocked\n  File \"<frozen importlib._bootstrap_external>\", line 883, in exec_module\n  File \"<frozen importlib._bootstrap>\", line 241, in _call_with_frames_removed\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/torch/__init__.py\", line 1332, in <module>\n    _C._initExtension(manager_path())\n  File \"<frozen importlib._bootstrap>\", line 1027, in _find_and_load\n  File \"<frozen importlib._bootstrap>\", line 1006, in _find_and_load_unlocked\n  File \"<frozen importlib._bootstrap>\", line 688, in _load_unlocked\n  File \"<frozen importlib._bootstrap_external>\", line 883, in exec_module\n  File \"<frozen importlib._bootstrap>\", line 241, in _call_with_frames_removed\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/torch/cuda/__init__.py\", line 244, in <module>\n    _lazy_call(_check_capability)\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/torch/cuda/__init__.py\", line 241, in _lazy_call\n    _queued_calls.append((callable, traceback.format_stack()))\n", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mRuntimeError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "File \u001b[0;32m/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/torch/cuda/__init__.py:311\u001b[0m, in \u001b[0;36m_lazy_init\u001b[0;34m()\u001b[0m\n\u001b[1;32m    310\u001b[0m \u001b[39mtry\u001b[39;00m:\n\u001b[0;32m--> 311\u001b[0m     queued_call()\n\u001b[1;32m    312\u001b[0m \u001b[39mexcept\u001b[39;00m \u001b[39mException\u001b[39;00m \u001b[39mas\u001b[39;00m e:\n", "File \u001b[0;32m/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/torch/cuda/__init__.py:180\u001b[0m, in \u001b[0;36m_check_capability\u001b[0;34m()\u001b[0m\n\u001b[1;32m    179\u001b[0m \u001b[39mfor\u001b[39;00m d \u001b[39min\u001b[39;00m \u001b[39mrange\u001b[39m(device_count()):\n\u001b[0;32m--> 180\u001b[0m     capability \u001b[39m=\u001b[39m get_device_capability(d)\n\u001b[1;32m    181\u001b[0m     major \u001b[39m=\u001b[39m capability[\u001b[39m0\u001b[39m]\n", "File \u001b[0;32m/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/torch/cuda/__init__.py:435\u001b[0m, in \u001b[0;36mget_device_capability\u001b[0;34m(device)\u001b[0m\n\u001b[1;32m    423\u001b[0m \u001b[39m\u001b[39m\u001b[39mr\u001b[39m\u001b[39m\"\"\"Gets the cuda capability of a device.\u001b[39;00m\n\u001b[1;32m    424\u001b[0m \n\u001b[1;32m    425\u001b[0m \u001b[39mArgs:\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    433\u001b[0m \u001b[39m    tuple(int, int): the major and minor cuda capability of the device\u001b[39;00m\n\u001b[1;32m    434\u001b[0m \u001b[39m\"\"\"\u001b[39;00m\n\u001b[0;32m--> 435\u001b[0m prop \u001b[39m=\u001b[39m get_device_properties(device)\n\u001b[1;32m    436\u001b[0m \u001b[39mreturn\u001b[39;00m prop\u001b[39m.\u001b[39mmajor, prop\u001b[39m.\u001b[39mminor\n", "File \u001b[0;32m/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/torch/cuda/__init__.py:453\u001b[0m, in \u001b[0;36mget_device_properties\u001b[0;34m(device)\u001b[0m\n\u001b[1;32m    452\u001b[0m     \u001b[39mraise\u001b[39;00m \u001b[39mAssertionError\u001b[39;00m(\u001b[39m\"\u001b[39m\u001b[39mInvalid device id\u001b[39m\u001b[39m\"\u001b[39m)\n\u001b[0;32m--> 453\u001b[0m \u001b[39mreturn\u001b[39;00m _get_device_properties(device)\n", "\u001b[0;31mRuntimeError\u001b[0m: device >= 0 && device < num_gpus INTERNAL ASSERT FAILED at \"../aten/src/ATen/cuda/CUDAContext.cpp\":50, please report a bug to PyTorch. device=1, num_gpus=\u0001", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[0;31m<PERSON>eferredCudaCallError\u001b[0m                     <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[17], line 3\u001b[0m\n\u001b[1;32m      1\u001b[0m tokenizer \u001b[39m=\u001b[39m AutoTokenizer\u001b[39m.\u001b[39mfrom_pretrained(\u001b[39m\"\u001b[39m\u001b[39mgoogle/flan-t5-xl\u001b[39m\u001b[39m\"\u001b[39m, cache_dir\u001b[39m=\u001b[39mos\u001b[39m.\u001b[39menviron[\u001b[39m'\u001b[39m\u001b[39mSCRATCH\u001b[39m\u001b[39m'\u001b[39m] \u001b[39m+\u001b[39m \u001b[39m'\u001b[39m\u001b[39m/hf\u001b[39m\u001b[39m'\u001b[39m)\n\u001b[1;32m      2\u001b[0m model \u001b[39m=\u001b[39m T5ForConditionalGeneration\u001b[39m.\u001b[39;49mfrom_pretrained(\u001b[39m\"\u001b[39;49m\u001b[39mgoogle/flan-t5-xl\u001b[39;49m\u001b[39m\"\u001b[39;49m, \n\u001b[0;32m----> 3\u001b[0m                                                    cache_dir\u001b[39m=\u001b[39;49mos\u001b[39m.\u001b[39;49menviron[\u001b[39m'\u001b[39;49m\u001b[39mSCRATCH\u001b[39;49m\u001b[39m'\u001b[39;49m] \u001b[39m+\u001b[39;49m \u001b[39m'\u001b[39;49m\u001b[39m/hf\u001b[39;49m\u001b[39m'\u001b[39;49m)\u001b[39m.\u001b[39;49mcuda()\n", "File \u001b[0;32m/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/transformers/modeling_utils.py:2694\u001b[0m, in \u001b[0;36mPreTrainedModel.cuda\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   2689\u001b[0m     \u001b[39mraise\u001b[39;00m \u001b[39mValueError\u001b[39;00m(\n\u001b[1;32m   2690\u001b[0m         \u001b[39m\"\u001b[39m\u001b[39mCalling `cuda()` is not supported for `4-bit` or `8-bit` quantized models. Please use the model as it is, since the\u001b[39m\u001b[39m\"\u001b[39m\n\u001b[1;32m   2691\u001b[0m         \u001b[39m\"\u001b[39m\u001b[39m model has already been set to the correct devices and casted to the correct `dtype`.\u001b[39m\u001b[39m\"\u001b[39m\n\u001b[1;32m   2692\u001b[0m     )\n\u001b[1;32m   2693\u001b[0m \u001b[39melse\u001b[39;00m:\n\u001b[0;32m-> 2694\u001b[0m     \u001b[39mreturn\u001b[39;00m \u001b[39msuper\u001b[39;49m()\u001b[39m.\u001b[39;49mcuda(\u001b[39m*\u001b[39;49margs, \u001b[39m*\u001b[39;49m\u001b[39m*\u001b[39;49mkwargs)\n", "File \u001b[0;32m/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/torch/nn/modules/module.py:918\u001b[0m, in \u001b[0;36mModule.cuda\u001b[0;34m(self, device)\u001b[0m\n\u001b[1;32m    901\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mcuda\u001b[39m(\u001b[39mself\u001b[39m: T, device: Optional[Union[\u001b[39mint\u001b[39m, device]] \u001b[39m=\u001b[39m \u001b[39mNone\u001b[39;00m) \u001b[39m-\u001b[39m\u001b[39m>\u001b[39m T:\n\u001b[1;32m    902\u001b[0m \u001b[39m    \u001b[39m\u001b[39mr\u001b[39m\u001b[39m\"\"\"Moves all model parameters and buffers to the GPU.\u001b[39;00m\n\u001b[1;32m    903\u001b[0m \n\u001b[1;32m    904\u001b[0m \u001b[39m    This also makes associated parameters and buffers different objects. So\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    916\u001b[0m \u001b[39m        Module: self\u001b[39;00m\n\u001b[1;32m    917\u001b[0m \u001b[39m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 918\u001b[0m     \u001b[39mreturn\u001b[39;00m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_apply(\u001b[39mlambda\u001b[39;49;00m t: t\u001b[39m.\u001b[39;49mcuda(device))\n", "File \u001b[0;32m/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/torch/nn/modules/module.py:810\u001b[0m, in \u001b[0;36mModule._apply\u001b[0;34m(self, fn, recurse)\u001b[0m\n\u001b[1;32m    808\u001b[0m \u001b[39mif\u001b[39;00m recurse:\n\u001b[1;32m    809\u001b[0m     \u001b[39mfor\u001b[39;00m module \u001b[39min\u001b[39;00m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mchildren():\n\u001b[0;32m--> 810\u001b[0m         module\u001b[39m.\u001b[39;49m_apply(fn)\n\u001b[1;32m    812\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mcompute_should_use_set_data\u001b[39m(tensor, tensor_applied):\n\u001b[1;32m    813\u001b[0m     \u001b[39mif\u001b[39;00m torch\u001b[39m.\u001b[39m_has_compatible_shallow_copy_type(tensor, tensor_applied):\n\u001b[1;32m    814\u001b[0m         \u001b[39m# If the new tensor has compatible tensor type as the existing tensor,\u001b[39;00m\n\u001b[1;32m    815\u001b[0m         \u001b[39m# the current behavior is to change the tensor in-place using `.data =`,\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    820\u001b[0m         \u001b[39m# global flag to let the user control whether they want the future\u001b[39;00m\n\u001b[1;32m    821\u001b[0m         \u001b[39m# behavior of overwriting the existing tensor or not.\u001b[39;00m\n", "File \u001b[0;32m/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/torch/nn/modules/module.py:833\u001b[0m, in \u001b[0;36mModule._apply\u001b[0;34m(self, fn, recurse)\u001b[0m\n\u001b[1;32m    829\u001b[0m \u001b[39m# Tensors stored in modules are graph leaves, and we don't want to\u001b[39;00m\n\u001b[1;32m    830\u001b[0m \u001b[39m# track autograd history of `param_applied`, so we have to use\u001b[39;00m\n\u001b[1;32m    831\u001b[0m \u001b[39m# `with torch.no_grad():`\u001b[39;00m\n\u001b[1;32m    832\u001b[0m \u001b[39mwith\u001b[39;00m torch\u001b[39m.\u001b[39mno_grad():\n\u001b[0;32m--> 833\u001b[0m     param_applied \u001b[39m=\u001b[39m fn(param)\n\u001b[1;32m    834\u001b[0m should_use_set_data \u001b[39m=\u001b[39m compute_should_use_set_data(param, param_applied)\n\u001b[1;32m    835\u001b[0m \u001b[39mif\u001b[39;00m should_use_set_data:\n", "File \u001b[0;32m/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/torch/nn/modules/module.py:918\u001b[0m, in \u001b[0;36mModule.cuda.<locals>.<lambda>\u001b[0;34m(t)\u001b[0m\n\u001b[1;32m    901\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mcuda\u001b[39m(\u001b[39mself\u001b[39m: T, device: Optional[Union[\u001b[39mint\u001b[39m, device]] \u001b[39m=\u001b[39m \u001b[39mNone\u001b[39;00m) \u001b[39m-\u001b[39m\u001b[39m>\u001b[39m T:\n\u001b[1;32m    902\u001b[0m \u001b[39m    \u001b[39m\u001b[39mr\u001b[39m\u001b[39m\"\"\"Moves all model parameters and buffers to the GPU.\u001b[39;00m\n\u001b[1;32m    903\u001b[0m \n\u001b[1;32m    904\u001b[0m \u001b[39m    This also makes associated parameters and buffers different objects. So\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    916\u001b[0m \u001b[39m        Module: self\u001b[39;00m\n\u001b[1;32m    917\u001b[0m \u001b[39m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 918\u001b[0m     \u001b[39mreturn\u001b[39;00m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_apply(\u001b[39mlambda\u001b[39;00m t: t\u001b[39m.\u001b[39;49mcuda(device))\n", "File \u001b[0;32m/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/torch/cuda/__init__.py:317\u001b[0m, in \u001b[0;36m_lazy_init\u001b[0;34m()\u001b[0m\n\u001b[1;32m    312\u001b[0m         \u001b[39mexcept\u001b[39;00m \u001b[39mException\u001b[39;00m \u001b[39mas\u001b[39;00m e:\n\u001b[1;32m    313\u001b[0m             msg \u001b[39m=\u001b[39m (\n\u001b[1;32m    314\u001b[0m                 \u001b[39mf\u001b[39m\u001b[39m\"\u001b[39m\u001b[39mCUDA call failed lazily at initialization with error: \u001b[39m\u001b[39m{\u001b[39;00m\u001b[39mstr\u001b[39m(e)\u001b[39m}\u001b[39;00m\u001b[39m\\n\u001b[39;00m\u001b[39m\\n\u001b[39;00m\u001b[39m\"\u001b[39m\n\u001b[1;32m    315\u001b[0m                 \u001b[39mf\u001b[39m\u001b[39m\"\u001b[39m\u001b[39mCUDA call was originally invoked at:\u001b[39m\u001b[39m\\n\u001b[39;00m\u001b[39m\\n\u001b[39;00m\u001b[39m{\u001b[39;00m\u001b[39m'\u001b[39m\u001b[39m'\u001b[39m\u001b[39m.\u001b[39mjoin(orig_traceback)\u001b[39m}\u001b[39;00m\u001b[39m\"\u001b[39m\n\u001b[1;32m    316\u001b[0m             )\n\u001b[0;32m--> 317\u001b[0m             \u001b[39mraise\u001b[39;00m DeferredCudaCallError(msg) \u001b[39mfrom\u001b[39;00m \u001b[39me\u001b[39;00m\n\u001b[1;32m    318\u001b[0m \u001b[39mfinally\u001b[39;00m:\n\u001b[1;32m    319\u001b[0m     \u001b[39mdelattr\u001b[39m(_tls, \u001b[39m\"\u001b[39m\u001b[39mis_initializing\u001b[39m\u001b[39m\"\u001b[39m)\n", "\u001b[0;31mDeferredCudaCallError\u001b[0m: CUDA call failed lazily at initialization with error: device >= 0 && device < num_gpus INTERNAL ASSERT FAILED at \"../aten/src/ATen/cuda/CUDAContext.cpp\":50, please report a bug to PyTorch. device=1, num_gpus=\u0001\n\nCUDA call was originally invoked at:\n\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/runpy.py\", line 196, in _run_module_as_main\n    return _run_code(code, main_globals, None,\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/runpy.py\", line 86, in _run_code\n    exec(code, run_globals)\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel_launcher.py\", line 18, in <module>\n    app.launch_new_instance()\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/traitlets/config/application.py\", line 1075, in launch_instance\n    app.start()\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel/kernelapp.py\", line 739, in start\n    self.io_loop.start()\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/tornado/platform/asyncio.py\", line 205, in start\n    self.asyncio_loop.run_forever()\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/asyncio/base_events.py\", line 603, in run_forever\n    self._run_once()\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/asyncio/base_events.py\", line 1909, in _run_once\n    handle._run()\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/asyncio/events.py\", line 80, in _run\n    self._context.run(self._callback, *self._args)\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 545, in dispatch_queue\n    await self.process_one()\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 534, in process_one\n    await dispatch(*args)\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 437, in dispatch_shell\n    await result\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 362, in execute_request\n    await super().execute_request(stream, ident, parent)\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 778, in execute_request\n    reply_content = await reply_content\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 449, in do_execute\n    res = shell.run_cell(\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/ipykernel/zmqshell.py\", line 549, in run_cell\n    return super().run_cell(*args, **kwargs)\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3075, in run_cell\n    result = self._run_cell(\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3130, in _run_cell\n    result = runner(coro)\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/IPython/core/async_helpers.py\", line 128, in _pseudo_sync_runner\n    coro.send(None)\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3334, in run_cell_async\n    has_raised = await self.run_ast_nodes(code_ast.body, cell_name,\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3517, in run_ast_nodes\n    if await self.run_code(code, result, async_=asy):\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3577, in run_code\n    exec(code_obj, self.user_global_ns, self.user_ns)\n  File \"/tmp/ipykernel_2303906/1412134564.py\", line 6, in <module>\n    import torch\n  File \"<frozen importlib._bootstrap>\", line 1027, in _find_and_load\n  File \"<frozen importlib._bootstrap>\", line 1006, in _find_and_load_unlocked\n  File \"<frozen importlib._bootstrap>\", line 688, in _load_unlocked\n  File \"<frozen importlib._bootstrap_external>\", line 883, in exec_module\n  File \"<frozen importlib._bootstrap>\", line 241, in _call_with_frames_removed\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/torch/__init__.py\", line 1332, in <module>\n    _C._initExtension(manager_path())\n  File \"<frozen importlib._bootstrap>\", line 1027, in _find_and_load\n  File \"<frozen importlib._bootstrap>\", line 1006, in _find_and_load_unlocked\n  File \"<frozen importlib._bootstrap>\", line 688, in _load_unlocked\n  File \"<frozen importlib._bootstrap_external>\", line 883, in exec_module\n  File \"<frozen importlib._bootstrap>\", line 241, in _call_with_frames_removed\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/torch/cuda/__init__.py\", line 244, in <module>\n    _lazy_call(_check_capability)\n  File \"/network/scratch/m/maryam.hashemzadeh/conda_envs/lg/lib/python3.10/site-packages/torch/cuda/__init__.py\", line 241, in _lazy_call\n    _queued_calls.append((callable, traceback.format_stack()))\n"]}], "source": ["tokenizer = AutoTokenizer.from_pretrained(\"google/flan-t5-xl\", cache_dir=os.environ['SCRATCH'] + '/hf')\n", "model = T5ForConditionalGeneration.from_pretrained(\"google/flan-t5-xl\", \n", "                                                   cache_dir=os.environ['SCRATCH'] + '/hf').cuda()"]}, {"cell_type": "code", "execution_count": 14, "id": "a9d4bed4-3ee5-4203-8c87-2d86ed14eb7c", "metadata": {}, "outputs": [], "source": ["import torch"]}, {"cell_type": "code", "execution_count": 15, "id": "126501ab-c3dd-46dd-b435-53d554a48d03", "metadata": {}, "outputs": [], "source": ["torch.cuda.empty_cache()"]}, {"cell_type": "code", "execution_count": 16, "id": "a656d7c8-97c6-4b4d-ab75-7ed5754379ae", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'prompt' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[16], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[39mprint\u001b[39m(prompt)\n", "\u001b[0;31mNameError\u001b[0m: name 'prompt' is not defined"]}], "source": ["print(prompt)"]}, {"cell_type": "code", "execution_count": 124, "id": "93cae55d-2b06-4f65-bc97-98b3745641fb", "metadata": {}, "outputs": [], "source": ["# with torch.amp.autocast('cuda', dtype=torch.float16):\n", "inputs = tokenizer([prompt], return_tensors=\"pt\")\n", "gen_ids = model.generate(inputs[\"input_ids\"].cuda(), do_sample=True, \n", "                      max_new_tokens=20, top_k=0, temperature=1.4, num_return_sequences=20)\n", "texts = tokenizer.batch_decode(gen_ids[:, inputs[\"input_ids\"].shape[1]:], \n", "                               skip_special_tokens=True, clean_up_tokenization_spaces=False)"]}, {"cell_type": "code", "execution_count": 125, "id": "d41ce34f-2d96-47da-8dff-43b3ef43d5ee", "metadata": {}, "outputs": [{"data": {"text/plain": ["['\\nGo to cell B3.\\nHow do you go from one cell to another and what will',\n", " '\\nMove to cell (2, 1) \\nCreate a set of possible next movements the',\n", " '\\n- Locate the yellow square from the initial position. \\n\\nHow can the agent approach the right',\n", " '\\nHead east. \\n**Understood steps: Head right, where will you look for the door',\n", " '\\n- Start in any arbitrary room in the grid.\\n- Examine the current room and find the',\n", " '\\nKnow where your starting location is\\n\\n\\nAs in the environment, you are currently in room 1',\n", " '\\n* Move one cell downward.\\n* Get obstacle information in the current room by checking the bottom',\n", " '\\n**Cell Selection and Entry:**\\n- Determine the best cell in row 1 and column ',\n", " ' imagine what isрисо .... (outer level - once the grid becomes more familiar).\\n\\n\\n**Step ',\n", " '\\n- reach 2-3 rooms down from the grid initial position.\\n\\n\\nInstruction step 2',\n", " '\\nWhat is your current position?\\nAnswer: Your current position is in the bottom left cell (',\n", " '\\n* Start at the position [4, 1].\\n* Move right 2 steps.',\n", " '\\n- Go from room 1 to room 2.\\n\\nInvestigate nearby rooms, and reach',\n", " '\\n<PERSON><PERSON><PERSON> (ActionListener) Mysterді\\n\\nRecall: \\n1. Move to the right when',\n", " '\\n\\n\\nFollow Green Door.\\n\\n\\nThe agent moves along the vertical white space between Horizontal and Vertical corridors till',\n", " \"\\nIdentify the north, west, and south's room numbers using the A, B, and\",\n", " '\\nThe agent should move from the grid.\\n\\n\\nInstruction step 2:\\nOnce in the grid',\n", " '\\nMove down from the current room.\\n\\n\\nInstruction step 2:\\nGo left through a neighbor',\n", " '\\nFrom starting position move to the south-west neighbor of #R1C1.\\n\\nInstruction',\n", " \"\\nNavigate from the current cell to the yellow square.\\n\\n\\nRemember, it's not necessary to\"]"]}, "execution_count": 125, "metadata": {}, "output_type": "execute_result"}], "source": ["texts"]}, {"cell_type": "code", "execution_count": null, "id": "50b61284-e516-4ef1-b24d-1a4d4f68db02", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 5}