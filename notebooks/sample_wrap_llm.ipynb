{"cells": [{"cell_type": "code", "execution_count": 1, "id": "be1641a2-a169-46b7-b64e-cf47ff214729", "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ['XLA_PYTHON_CLIENT_PREALLOCATE'] = 'false'"]}, {"cell_type": "code", "execution_count": 2, "id": "cae51990-9238-4c13-9973-f324b43a1287", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/network/scratch/a/artem.zholus/conda_envs/rl_jax_torch/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "CUDA backend failed to initialize: Found CUDA version 12010, but JAX was built against version 12030, which is newer. The copy of CUDA that is installed must be at least as new as the version against which JAX was built. (Set TF_CPP_MIN_LOG_LEVEL=0 and rerun for more info.)\n", "WARNING:absl:Tensorflow library not found, tensorflow.io.gfile operations will use native shim calls. GCS paths (i.e. 'gs://...') cannot be accessed.\n"]}], "source": ["import numpy as np\n", "import math\n", "from tqdm import tqdm\n", "import jax\n", "from jax import numpy as jnp\n", "import torch\n", "from transformers import AutoTokenizer, BertForMaskedLM, BertForPreTraining\n", "\n", "from xminigrid.benchmarks import load_bz2_pickle\n", "import xminigrid\n", "from train_meta_task import my_load_benchmark\n", "from rich import print"]}, {"cell_type": "code", "execution_count": 17, "id": "7b9d8897-d29d-487e-8491-b3d1cb823291", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Loading checkpoint shards: 100%|███████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.13s/it]\n"]}], "source": ["from langchain_community.llms.huggingface_pipeline import HuggingFacePipeline\n", "\n", "gpu_llm = HuggingFacePipeline.from_model_id(\n", "    model_id=\"google/gemma-2b-it\",\n", "    task=\"text-generation\",\n", "    device=0,  # replace with device_map=\"auto\" to use the accelerate library.\n", "    pipeline_kwargs={\"max_new_tokens\": 100},\n", "    model_kwargs={'cache_dir': os.environ['SCRATCH'] + '/hf'},\n", ")\n", "from langchain_core.prompts import PromptTemplate\n", "\n", "template = \"\"\"Question: {question}\"\"\"\n", "prompt = PromptTemplate.from_template(template)\n", "\n", "chain = prompt | gpu_llm"]}, {"cell_type": "code", "execution_count": 18, "id": "56098f3f-b2b1-4794-8ca7-e89b091e0595", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Question: What is electroencephalography?\n", "</pre>\n"], "text/plain": ["Question: What is electroencephalography?\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["question = \"What is electroencephalography?\"\n", "\n", "print(chain.invoke({\"question\": question}))"]}, {"cell_type": "code", "execution_count": 3, "id": "16ed5f4e-14f5-4239-8a2c-d0e014aa2ebc", "metadata": {}, "outputs": [{"ename": "RuntimeError", "evalue": "Failed to import transformers.models.gemma.modeling_gemma because of the following error (look up to see its traceback):\n/network/scratch/a/artem.zholus/conda_envs/rl_jax_torch/lib/python3.10/site-packages/flash_attn_2_cuda.cpython-310-x86_64-linux-gnu.so: undefined symbol: _ZN2at4_ops15sum_IntList_out4callERKNS_6TensorEN3c1016OptionalArrayRefIlEEbSt8optionalINS5_10ScalarTypeEERS2_", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mImportError\u001b[0m                               Traceback (most recent call last)", "File \u001b[0;32m/network/scratch/a/artem.zholus/conda_envs/rl_jax_torch/lib/python3.10/site-packages/transformers/utils/import_utils.py:1472\u001b[0m, in \u001b[0;36m_LazyModule._get_module\u001b[0;34m(self, module_name)\u001b[0m\n\u001b[1;32m   1471\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1472\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mimportlib\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mimport_module\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m.\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m+\u001b[39;49m\u001b[43m \u001b[49m\u001b[43mmodule_name\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;18;43m__name__\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1473\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n", "File \u001b[0;32m/network/scratch/a/artem.zholus/conda_envs/rl_jax_torch/lib/python3.10/importlib/__init__.py:126\u001b[0m, in \u001b[0;36mimport_module\u001b[0;34m(name, package)\u001b[0m\n\u001b[1;32m    125\u001b[0m         level \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m\n\u001b[0;32m--> 126\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_bootstrap\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_gcd_import\u001b[49m\u001b[43m(\u001b[49m\u001b[43mname\u001b[49m\u001b[43m[\u001b[49m\u001b[43mlevel\u001b[49m\u001b[43m:\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpackage\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlevel\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m<frozen importlib._bootstrap>:1050\u001b[0m, in \u001b[0;36m_gcd_import\u001b[0;34m(name, package, level)\u001b[0m\n", "File \u001b[0;32m<frozen importlib._bootstrap>:1027\u001b[0m, in \u001b[0;36m_find_and_load\u001b[0;34m(name, import_)\u001b[0m\n", "File \u001b[0;32m<frozen importlib._bootstrap>:1006\u001b[0m, in \u001b[0;36m_find_and_load_unlocked\u001b[0;34m(name, import_)\u001b[0m\n", "File \u001b[0;32m<frozen importlib._bootstrap>:688\u001b[0m, in \u001b[0;36m_load_unlocked\u001b[0;34m(spec)\u001b[0m\n", "File \u001b[0;32m<frozen importlib._bootstrap_external>:883\u001b[0m, in \u001b[0;36mexec_module\u001b[0;34m(self, module)\u001b[0m\n", "File \u001b[0;32m<frozen importlib._bootstrap>:241\u001b[0m, in \u001b[0;36m_call_with_frames_removed\u001b[0;34m(f, *args, **kwds)\u001b[0m\n", "File \u001b[0;32m/network/scratch/a/artem.zholus/conda_envs/rl_jax_torch/lib/python3.10/site-packages/transformers/models/gemma/modeling_gemma.py:50\u001b[0m\n\u001b[1;32m     49\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_flash_attn_2_available():\n\u001b[0;32m---> 50\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mflash_attn\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m flash_attn_func, flash_attn_varlen_func\n\u001b[1;32m     51\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mflash_attn\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbert_padding\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m index_first_axis, pad_input, unpad_input  \u001b[38;5;66;03m# noqa\u001b[39;00m\n", "File \u001b[0;32m/network/scratch/a/artem.zholus/conda_envs/rl_jax_torch/lib/python3.10/site-packages/flash_attn/__init__.py:3\u001b[0m\n\u001b[1;32m      1\u001b[0m __version__ \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m2.5.6\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m----> 3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mflash_attn\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mflash_attn_interface\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[1;32m      4\u001b[0m     flash_attn_func,\n\u001b[1;32m      5\u001b[0m     flash_attn_kvpacked_func,\n\u001b[1;32m      6\u001b[0m     flash_attn_qkvpacked_func,\n\u001b[1;32m      7\u001b[0m     flash_attn_varlen_func,\n\u001b[1;32m      8\u001b[0m     flash_attn_varlen_kvpacked_func,\n\u001b[1;32m      9\u001b[0m     flash_attn_varlen_qkvpacked_func,\n\u001b[1;32m     10\u001b[0m     flash_attn_with_kvcache,\n\u001b[1;32m     11\u001b[0m )\n", "File \u001b[0;32m/network/scratch/a/artem.zholus/conda_envs/rl_jax_torch/lib/python3.10/site-packages/flash_attn/flash_attn_interface.py:10\u001b[0m\n\u001b[1;32m      8\u001b[0m \u001b[38;5;66;03m# isort: off\u001b[39;00m\n\u001b[1;32m      9\u001b[0m \u001b[38;5;66;03m# We need to import the CUDA kernels after importing torch\u001b[39;00m\n\u001b[0;32m---> 10\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mflash_attn_2_cuda\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mflash_attn_cuda\u001b[39;00m\n\u001b[1;32m     12\u001b[0m \u001b[38;5;66;03m# isort: on\u001b[39;00m\n", "\u001b[0;31mImportError\u001b[0m: /network/scratch/a/artem.zholus/conda_envs/rl_jax_torch/lib/python3.10/site-packages/flash_attn_2_cuda.cpython-310-x86_64-linux-gnu.so: undefined symbol: _ZN2at4_ops15sum_IntList_out4callERKNS_6TensorEN3c1016OptionalArrayRefIlEEbSt8optionalINS5_10ScalarTypeEERS2_", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[0;31mRuntimeError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[3], line 4\u001b[0m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01m<PERSON>ch\u001b[39;00m\n\u001b[1;32m      3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mtransformers\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m AutoModelForCausalLM, AutoTokenizer, pipeline\n\u001b[0;32m----> 4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mtransformers\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m AutoTokenizer, FlaxT5ForConditionalGeneration, T5ForConditionalGeneration, \\\n\u001b[1;32m      5\u001b[0m     FlaxGemmaForCausalLM, GemmaForCausalLM \n\u001b[1;32m      6\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mos\u001b[39;00m\n\u001b[1;32m      7\u001b[0m \u001b[38;5;66;03m# tokenizer = AutoTokenizer.from_pretrained(\"google/gemma-2b-it\", cache_dir=os.environ['SCRATCH'] + '/hf')\u001b[39;00m\n\u001b[1;32m      8\u001b[0m \u001b[38;5;66;03m# model = GemmaForCausalLM.from_pretrained(\u001b[39;00m\n\u001b[1;32m      9\u001b[0m \u001b[38;5;66;03m#     \"google/gemma-2b-it\",attn_implementation=\"flash_attention_2\",\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     12\u001b[0m \u001b[38;5;66;03m# pipe = pipeline(\"text-generation\", device=0, model=model, tokenizer=tokenizer, max_new_tokens=100)\u001b[39;00m\n\u001b[1;32m     13\u001b[0m \u001b[38;5;66;03m# pipe.enable_xformers_memory_efficient_attention()\u001b[39;00m\n", "File \u001b[0;32m<frozen importlib._bootstrap>:1075\u001b[0m, in \u001b[0;36m_handle_fromlist\u001b[0;34m(module, fromlist, import_, recursive)\u001b[0m\n", "File \u001b[0;32m/network/scratch/a/artem.zholus/conda_envs/rl_jax_torch/lib/python3.10/site-packages/transformers/utils/import_utils.py:1463\u001b[0m, in \u001b[0;36m_LazyModule.__getattr__\u001b[0;34m(self, name)\u001b[0m\n\u001b[1;32m   1461\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m name \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_class_to_module\u001b[38;5;241m.\u001b[39mkeys():\n\u001b[1;32m   1462\u001b[0m     module \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_module(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_class_to_module[name])\n\u001b[0;32m-> 1463\u001b[0m     value \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mgetattr\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mmodule\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mname\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1464\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1465\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mAttributeError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodule \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m has no attribute \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mname\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m/network/scratch/a/artem.zholus/conda_envs/rl_jax_torch/lib/python3.10/site-packages/transformers/utils/import_utils.py:1462\u001b[0m, in \u001b[0;36m_LazyModule.__getattr__\u001b[0;34m(self, name)\u001b[0m\n\u001b[1;32m   1460\u001b[0m     value \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_module(name)\n\u001b[1;32m   1461\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m name \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_class_to_module\u001b[38;5;241m.\u001b[39mkeys():\n\u001b[0;32m-> 1462\u001b[0m     module \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_get_module\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_class_to_module\u001b[49m\u001b[43m[\u001b[49m\u001b[43mname\u001b[49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1463\u001b[0m     value \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mgetattr\u001b[39m(module, name)\n\u001b[1;32m   1464\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n", "File \u001b[0;32m/network/scratch/a/artem.zholus/conda_envs/rl_jax_torch/lib/python3.10/site-packages/transformers/utils/import_utils.py:1474\u001b[0m, in \u001b[0;36m_LazyModule._get_module\u001b[0;34m(self, module_name)\u001b[0m\n\u001b[1;32m   1472\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m importlib\u001b[38;5;241m.\u001b[39mimport_module(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m.\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m+\u001b[39m module_name, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m)\n\u001b[1;32m   1473\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m-> 1474\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\n\u001b[1;32m   1475\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFailed to import \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mmodule_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m because of the following error (look up to see its\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   1476\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m traceback):\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;132;01m{\u001b[39;00me\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   1477\u001b[0m     ) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01me\u001b[39;00m\n", "\u001b[0;31mRuntimeError\u001b[0m: Failed to import transformers.models.gemma.modeling_gemma because of the following error (look up to see its traceback):\n/network/scratch/a/artem.zholus/conda_envs/rl_jax_torch/lib/python3.10/site-packages/flash_attn_2_cuda.cpython-310-x86_64-linux-gnu.so: undefined symbol: _ZN2at4_ops15sum_IntList_out4callERKNS_6TensorEN3c1016OptionalArrayRefIlEEbSt8optionalINS5_10ScalarTypeEERS2_"]}], "source": ["from langchain_community.llms.huggingface_pipeline import HuggingFacePipeline\n", "import torch\n", "from transformers import AutoModelForCausalLM, AutoTokenizer, pipeline\n", "from transformers import AutoTokenizer, FlaxT5ForConditionalGeneration, T5ForConditionalGeneration, \\\n", "    FlaxGemmaForCausalLM, GemmaForCausalLM \n", "import os\n", "# tokenizer = AutoTokenizer.from_pretrained(\"google/gemma-2b-it\", cache_dir=os.environ['SCRATCH'] + '/hf')\n", "# model = GemmaForCausalLM.from_pretrained(\n", "#     \"google/gemma-2b-it\",attn_implementation=\"flash_attention_2\",\n", "#     torch_dtype=torch.bfloat16, \n", "#     cache_dir=os.environ['SCRATCH'] + '/hf').cuda()\n", "# pipe = pipeline(\"text-generation\", device=0, model=model, tokenizer=tokenizer, max_new_tokens=100)\n", "# pipe.enable_xformers_memory_efficient_attention()"]}, {"cell_type": "code", "execution_count": 13, "id": "c82ec023-e941-4e94-b5d7-08058d10738b", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'generated_text': 'Write me a poem about Machine Learning.'}]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["pipe(\"Write me a poem about Machine Learning.\", \n", "     do_sample=True, \n", "     # num_beams=4,\n", "     # early_stopping=False,\n", "     # penalty_alpha=0.6,\n", "     eos_token_id=-1,\n", "     top_k=50, \n", "     temperature=1.)"]}, {"cell_type": "code", "execution_count": 4, "id": "7bd8de8a-4237-42ee-b1e7-aedbc10d1263", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-04-04 23:42:04,667\tINFO util.py:154 -- Missing packages: ['ipywidgets']. Run `pip install -U ipywidgets`, then restart the notebook server for rich notebook output.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO 04-04 23:42:06 llm_engine.py:75] Initializing an LLM engine (v0.4.0) with config: model='google/gemma-2b-it', tokenizer='google/gemma-2b-it', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=True, dtype=torch.bfloat16, max_seq_len=8192, download_dir=None, load_format=auto, tensor_parallel_size=1, disable_custom_all_reduce=True, quantization=None, enforce_eager=False, kv_cache_dtype=auto, device_config=cuda, seed=0)\n", "INFO 04-04 23:42:07 selector.py:45] Cannot use FlashAttention because the package is not found. Please install it for better performance.\n", "INFO 04-04 23:42:07 selector.py:21] Using XFormers backend.\n", "WARNING 04-04 23:42:09 gemma.py:55] <PERSON>'s activation function was incorrectly set to exact GeLU in the config JSON file when it was initially released. Changing the activation function to approximate GeLU (`gelu_pytorch_tanh`). If you want to use the legacy `gelu`, edit the config JSON to set `hidden_activation=gelu` instead of `hidden_act`. See https://github.com/huggingface/transformers/pull/29402 for more details.\n", "INFO 04-04 23:42:09 weight_utils.py:177] Using model weights format ['*.safetensors']\n", "INFO 04-04 23:42:23 model_runner.py:104] Loading model weights took 4.6720 GB\n", "INFO 04-04 23:42:24 gpu_executor.py:94] # GPU blocks: 103713, # CPU blocks: 14563\n", "INFO 04-04 23:42:26 model_runner.py:791] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.\n", "INFO 04-04 23:42:26 model_runner.py:795] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.\n", "INFO 04-04 23:42:33 model_runner.py:867] Graph capturing finished in 7 secs.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processed prompts: 100%|████████████████████████████████████████████████████████████| 1/1 [00:00<00:00,  3.91it/s]\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "\n", "Paris is the capital of France. It is the seat of the French government and is the political, economic, and \n", "cultural center of the country.\n", "</pre>\n"], "text/plain": ["\n", "\n", "Paris is the capital of France. It is the seat of the French government and is the political, economic, and \n", "cultural center of the country.\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from langchain_community.llms import VLLM\n", "\n", "llm = VLLM(\n", "    model=\"google/gemma-2b-it\",\n", "    trust_remote_code=True,  # mandatory for hf models\n", "    max_new_tokens=128,\n", "    top_k=10,\n", "    top_p=0.95,\n", "    temperature=1.2,\n", "    # download_dir=,\n", ")\n", "\n", "print(llm.invoke(\"What is the capital of France ?\"))"]}, {"cell_type": "code", "execution_count": 5, "id": "eb8348ce-bc68-4b1d-979d-a1401d3f9ac4", "metadata": {}, "outputs": [], "source": ["from langchain.chains import LLMChain\n", "from langchain_core.prompts import PromptTemplate\n", "\n", "template = \"\"\"Question: {question}\n", "\n", "Answer: Let's think step by step.\"\"\"\n", "prompt = PromptTemplate.from_template(template)\n", "\n", "# llm_chain = LLMChain(prompt=prompt, llm=llm)\n", "\n", "# question = \"Who was the US president in the year the first Pokemon game was released?\"\n", "\n", "# print(llm_chain.invoke(question))"]}, {"cell_type": "code", "execution_count": 6, "id": "6f2be772-a526-4469-9909-e08f1dd7b9ec", "metadata": {}, "outputs": [], "source": ["from langchain.output_parsers import ResponseSchema, StructuredOutputParser\n", "from langchain.prompts import PromptTemplate\n", "# from langchain_openai import ChatOpenAI\n", "response_schemas = [\n", "    ResponseSchema(name=\"answer\", description=\"answer to the user's question\"),\n", "    ResponseSchema(\n", "        name=\"source\",\n", "        description=\"source used to answer the user's question, should be a website.\",\n", "    ),\n", "]\n", "output_parser = StructuredOutputParser.from_response_schemas(response_schemas)\n", "format_instructions = output_parser.get_format_instructions()\n", "prompt = PromptTemplate(\n", "    template=\"answer the users question as best as possible.\\n{format_instructions}\\n{question}\",\n", "    input_variables=[\"question\"],\n", "    partial_variables={\"format_instructions\": format_instructions},\n", ")"]}, {"cell_type": "code", "execution_count": 7, "id": "c83397a6-b4bd-4c0b-bc4a-824a0536bce9", "metadata": {}, "outputs": [], "source": ["chain = prompt | llm | output_parser"]}, {"cell_type": "code", "execution_count": 8, "id": "49586bdb-089b-4549-9fd8-fa124460a41c", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">answer the users question as best as possible.\n", "The output should be a markdown code snippet formatted in the following schema, including the leading and trailing \n", "<span style=\"color: #008000; text-decoration-color: #008000\">\"```json\"</span> and <span style=\"color: #008000; text-decoration-color: #008000\">\"```\"</span>:\n", "\n", "```json\n", "<span style=\"font-weight: bold\">{</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">\"answer\"</span>: string  <span style=\"color: #800080; text-decoration-color: #800080\">//</span> answer to the user's question\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">\"source\"</span>: string  <span style=\"color: #800080; text-decoration-color: #800080\">//</span> source used to answer the user's question, should be a website.\n", "<span style=\"font-weight: bold\">}</span>\n", "```\n", "what's the capital of france?\n", "</pre>\n"], "text/plain": ["answer the users question as best as possible.\n", "The output should be a markdown code snippet formatted in the following schema, including the leading and trailing \n", "\u001b[32m\"```json\"\u001b[0m and \u001b[32m\"```\"\u001b[0m:\n", "\n", "```json\n", "\u001b[1m{\u001b[0m\n", "        \u001b[32m\"answer\"\u001b[0m: string  \u001b[35m/\u001b[0m\u001b[35m/\u001b[0m answer to the user's question\n", "        \u001b[32m\"source\"\u001b[0m: string  \u001b[35m/\u001b[0m\u001b[35m/\u001b[0m source used to answer the user's question, should be a website.\n", "\u001b[1m}\u001b[0m\n", "```\n", "what's the capital of france?\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(prompt.invoke({\"question\": \"what's the capital of france?\"}).text)"]}, {"cell_type": "code", "execution_count": 9, "id": "b89cfc22-6015-4519-90b2-a134bb9e54bb", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Processed prompts: 100%|████████████████████████████████████████████████████████████| 1/1 [00:00<00:00,  1.58it/s]\n"]}, {"data": {"text/plain": ["'\\n\\n```json\\n{\\n\\t\"answer\": \"Paris\",\\n\\t\"source\": \"Wikipedia\"\\n}\\n```\\n\\nThe question is about the capital of France. The source provided is Wikipedia, which is a reputable website that provides reliable information about France and its capital city.\\n\\n```json\\n{\\n\\t\"answer\": \"Paris\",\\n\\t\"source\": \"Wikipedia\"\\n}\\n```'"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["(prompt | llm).invoke({\"question\": \"what's the capital of france?\"}, )a"]}, {"cell_type": "code", "execution_count": 10, "id": "8214c284-ced3-4676-9ef1-4943853f36bb", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'answer': 'Paris', 'source': 'Wikipedia'}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["output_parser.invoke('\\n\\n```json\\n{\\n\\t\"answer\": \"Paris\",\\n\\t\"source\": \"Wikipedia\"\\n}\\n```\\n\\nThe question is about the capital of France. The source provided is Wikipedia, which is a reputable website that provides reliable information about France and its capital city.\\n\\n```json\\n{\\n\\t\"answer\": \"Paris\",\\n\\t\"source\": \"Wikipedia\"\\n}\\n```')"]}, {"cell_type": "code", "execution_count": 6, "id": "c2db5011-1b62-4212-971a-500539edc06b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Processed prompts: 100%|████████████████████████████████████████████████████████████| 1/1 [00:00<00:00,  1.59it/s]\n"]}, {"data": {"text/plain": ["{'answer': 'Paris', 'source': 'Wikipedia'}"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["chain.invoke({\"question\": \"what's the capital of france?\"}, )"]}, {"cell_type": "code", "execution_count": 3, "id": "df46071e-00ec-4f41-ba71-288b7c8793ff", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Loading checkpoint shards: 100%|████████████████████████████████████████████████████| 2/2 [00:01<00:00,  1.97it/s]\n", "/network/scratch/a/artem.zholus/conda_envs/rl_jax_torch/lib/python3.10/site-packages/transformers/generation/utils.py:1178: UserWarning: Using the model-agnostic default `max_length` (=20) to control the generation length. We recommend setting `max_new_tokens` to control the maximum length of the generation.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<bos>Write me a poem about Machine Learning.\n", "\n", "Machines, they weave and they learn,\n", "From\n"]}], "source": ["# pip install accelerate\n", "from transformers import AutoTokenizer, AutoModelForCausalLM\n", "import os\n", "import torch\n", "tokenizer = AutoTokenizer.from_pretrained(\"google/gemma-2b-it\",cache_dir=os.environ['SCRATCH'] + '/hf')\n", "model = AutoModelForCausalLM.from_pretrained(\"google/gemma-2b-it\", device_map=\"auto\", torch_dtype=torch.bfloat16, cache_dir=os.environ['SCRATCH'] + '/hf')\n", "\n", "input_text = \"Write me a poem about Machine Learning.\"\n", "input_ids = tokenizer(input_text, return_tensors=\"pt\").to(\"cuda\")\n", "\n", "outputs = model.generate(**input_ids)\n", "print(tokenizer.decode(outputs[0]))\n"]}, {"cell_type": "code", "execution_count": 9, "id": "78c2cb22-0311-48c4-9dbe-d240e70ac061", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'input_ids': tensor([[     2,   5559,    682,    476,  19592,   1105,  13403,  14715, 235265]],\n", "       device='cuda:0'), 'attention_mask': tensor([[1, 1, 1, 1, 1, 1, 1, 1, 1]], device='cuda:0')}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["input_ids"]}, {"cell_type": "code", "execution_count": 8, "id": "42d458d0-a333-443f-8708-cf26d1fe0a4c", "metadata": {}, "outputs": [{"data": {"text/plain": ["GemmaConfig {\n", "  \"_name_or_path\": \"google/gemma-2b-it\",\n", "  \"architectures\": [\n", "    \"GemmaForCausalLM\"\n", "  ],\n", "  \"attention_bias\": false,\n", "  \"attention_dropout\": 0.0,\n", "  \"bos_token_id\": 2,\n", "  \"eos_token_id\": 1,\n", "  \"head_dim\": 256,\n", "  \"hidden_act\": \"gelu\",\n", "  \"hidden_size\": 2048,\n", "  \"initializer_range\": 0.02,\n", "  \"intermediate_size\": 16384,\n", "  \"max_position_embeddings\": 8192,\n", "  \"model_type\": \"gemma\",\n", "  \"num_attention_heads\": 8,\n", "  \"num_hidden_layers\": 18,\n", "  \"num_key_value_heads\": 1,\n", "  \"pad_token_id\": 0,\n", "  \"rms_norm_eps\": 1e-06,\n", "  \"rope_scaling\": null,\n", "  \"rope_theta\": 10000.0,\n", "  \"torch_dtype\": \"bfloat16\",\n", "  \"transformers_version\": \"4.38.2\",\n", "  \"use_cache\": true,\n", "  \"vocab_size\": 256000\n", "}"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["model.config"]}, {"cell_type": "code", "execution_count": 6, "id": "3cc8f8bc-b814-4357-b906-bacb79d3280b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<bos>Write me a poem about Machine Learning.\n", "\n", "A machine so wise, a mind so bright,\n", "With algorithms that shine and light.\n", "It learns from data, a never-ending quest,\n", "Algorithmic insights, a guiding test.\n", "\n", "From images and text, it extracts and gains,\n", "A symphony of rules and patterns.\n", "With every step it takes, a new path is found,\n", "A machine's intelligence, profound.\n", "\n", "From predictive analysis to chatbots that ignite,\n", "ML is shaping our world with every bite.\n", "The future unfolds, a tapestry of grace,\n", "With machines as partners, we find our place.\n", "\n", "So let us embrace this technology's might,\n", "For progress and possibilities take flight.\n", "Machine learning, a beacon in the night,\n", "Guiding us toward an era bright.<eos>\n"]}], "source": ["outputs = model.generate(**input_ids, do_sample=True, max_new_tokens=500)\n", "print(tokenizer.decode(outputs[0]))"]}, {"cell_type": "code", "execution_count": 27, "id": "79c0ed99-3854-424d-b6ff-48dc1dd12bba", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'xminigrid' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[27], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m env, env_params \u001b[38;5;241m=\u001b[39m \u001b[43mxminigrid\u001b[49m\u001b[38;5;241m.\u001b[39mmake(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mXLand-MiniGrid-R4-9x9\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m      2\u001b[0m benchmark \u001b[38;5;241m=\u001b[39m my_load_benchmark(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmy_trivial_1m_v2\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[0;31mNameError\u001b[0m: name 'xminigrid' is not defined"]}], "source": ["env, env_params = xminigrid.make(\"XLand-MiniGrid-R4-9x9\")\n", "benchmark = my_load_benchmark(\"my_trivial_1m_v2\")"]}, {"cell_type": "code", "execution_count": 4, "id": "9f4dd7a8-de4c-40f0-974d-395431ab3d5a", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'tuple' object has no attribute 'get_ruleset'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[4], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m rng \u001b[38;5;241m=\u001b[39m jax\u001b[38;5;241m.\u001b[39mrandom\u001b[38;5;241m.\u001b[39mPRNGKey(\u001b[38;5;241m42\u001b[39m)\n\u001b[0;32m----> 2\u001b[0m ruleset \u001b[38;5;241m=\u001b[39m \u001b[43mbenchmark\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_ruleset\u001b[49m(\u001b[38;5;241m1\u001b[39m)\n\u001b[1;32m      3\u001b[0m env_params \u001b[38;5;241m=\u001b[39m env_params\u001b[38;5;241m.\u001b[39mreplace(ruleset\u001b[38;5;241m=\u001b[39mruleset)\n\u001b[1;32m      4\u001b[0m timestep \u001b[38;5;241m=\u001b[39m env\u001b[38;5;241m.\u001b[39mreset(env_params, rng)\n", "\u001b[0;31mAttributeError\u001b[0m: 'tuple' object has no attribute 'get_ruleset'"]}], "source": ["rng = jax.random.<PERSON><PERSON><PERSON><PERSON>(42)\n", "ruleset = benchmark.get_ruleset(1)\n", "env_params = env_params.replace(ruleset=ruleset)\n", "timestep = env.reset(env_params, rng)"]}, {"cell_type": "code", "execution_count": null, "id": "bf3c64db-5ae7-48de-a615-d3c2db5a97df", "metadata": {}, "outputs": [], "source": ["goal_map = {\n", "    5: 'ball',\n", "    6: 'square',\n", "    7: 'pyramid',\n", "    8: 'goal',\n", "    9: 'key',\n", "    10: 'locked door',\n", "    11: 'closed door',\n", "    12: 'open door',\n", "    13: 'hex',\n", "    14: 'star'\n", "}\n", "\n", "color_map = {\n", "    3: 'red',\n", "    4: 'green',\n", "    5: 'blue',\n", "    6: 'purple',\n", "    7: 'yellow',\n", "    8: 'grey',\n", "    9: 'black',\n", "    10: 'orange',\n", "    11: 'white',\n", "    12: 'brown',\n", "    13: 'pink'\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "ec005965-0d02-4530-a66f-c936ed37d629", "metadata": {}, "outputs": [], "source": ["# print(env.render(env_params.replace(render_mode='rich_text'), timestep))"]}, {"cell_type": "code", "execution_count": null, "id": "8b72f979-5779-402c-b959-008fd283e5d2", "metadata": {}, "outputs": [], "source": ["c = ((timestep.state.grid[..., 0] != 4) & (timestep.state.grid[..., 0] != 3) &\n", "     (timestep.state.grid[..., 0] != 11) & (timestep.state.grid[..., 0] != 10) &\n", "     (timestep.state.grid[..., 1] != 8) & (timestep.state.grid[..., 1] != 9)).nonzero()"]}, {"cell_type": "code", "execution_count": null, "id": "fe9483dc-b6d0-4d97-aa41-707db14dcc5b", "metadata": {"scrolled": true}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 5, "id": "4f8c4579-ce2c-4aad-a8dc-24020a2cadbb", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'timestep' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[5], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m info \u001b[38;5;241m=\u001b[39m []\n\u001b[0;32m----> 2\u001b[0m agent_pos \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39marray(\u001b[43mtimestep\u001b[49m\u001b[38;5;241m.\u001b[39mstate\u001b[38;5;241m.\u001b[39magent\u001b[38;5;241m.\u001b[39mposition)\n\u001b[1;32m      3\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m o, x, y \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mzip\u001b[39m(np\u001b[38;5;241m.\u001b[39marray(timestep\u001b[38;5;241m.\u001b[39mstate\u001b[38;5;241m.\u001b[39mgrid[c]), \u001b[38;5;241m*\u001b[39mc):\n\u001b[1;32m      4\u001b[0m     info\u001b[38;5;241m.\u001b[39mappend((goal_map[o[\u001b[38;5;241m0\u001b[39m]], color_map[o[\u001b[38;5;241m1\u001b[39m]], x\u001b[38;5;241m.\u001b[39mitem(), y\u001b[38;5;241m.\u001b[39mitem()))\n", "\u001b[0;31mNameError\u001b[0m: name 'timestep' is not defined"]}], "source": ["info = []\n", "agent_pos = np.array(timestep.state.agent.position)\n", "for o, x, y in zip(np.array(timestep.state.grid[c]), *c):\n", "    info.append((goal_map[o[0]], color_map[o[1]], x.item(), y.item()))"]}, {"cell_type": "code", "execution_count": 6, "id": "0cc4bee9-c292-4c74-97b0-8c23529ad761", "metadata": {}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["info"]}, {"cell_type": "code", "execution_count": 7, "id": "1a33ca8d-e10b-48ee-925f-373a052ec52f", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'ruleset' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[7], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43mruleset\u001b[49m\u001b[38;5;241m.\u001b[39mgoal[\u001b[38;5;241m1\u001b[39m:\u001b[38;5;241m3\u001b[39m]\n", "\u001b[0;31mNameError\u001b[0m: name 'ruleset' is not defined"]}], "source": ["ruleset.goal[1:3]"]}, {"cell_type": "code", "execution_count": 100, "id": "947983b4-1d89-4ddb-94eb-11dd77eaf88c", "metadata": {"scrolled": true}, "outputs": [], "source": ["prompt = (f\"You are in the 4 rooms flat\" +\n", "          f\" gridworld with 2 column and 2 rows of rooms. \" +\n", "          # f\"Your position within the grid is ({agent_pos[0]}, {agent_pos[1]}).\" +\n", "          \" Adjacent rooms are connected with doors. \" +\n", "          \"Each room is 3 cells wide and 3 cells high. \") + \\\n", "          ''.join([f'The {col} {obj} is in the grid. ' for obj, col, x, y in info])"]}, {"cell_type": "code", "execution_count": 101, "id": "3370e16b-857b-4700-a04f-9818d8a43190", "metadata": {}, "outputs": [], "source": ["gobj, gcol = ruleset.goal[1:3]\n", "prompt = f'The gridworld environment desciprion: \\n{prompt}\\n' \\\n", "         f'Goal of the agent: Go to the {color_map[gcol.item()]} {goal_map[gobj.item()]}.\\n' \\\n", "         f'Break down the overall goal into short high-level step-by-step ' \\\n", "         f'instruction for the agent in the gridworld environment.\\n' \\\n", "         f'Instruction step 1: '"]}, {"cell_type": "code", "execution_count": 102, "id": "0acb715e-1659-43c3-a073-b5cd8d5b1fd9", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">The gridworld environment desciprion: \n", "You are in the <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4</span> rooms flat gridworld with <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span> column and <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span> rows of rooms.  Adjacent rooms are connected with doors. \n", "Each room is <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span> cells wide and <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span> cells high. The yellow square is in the grid. The blue hex is in the grid. The \n", "purple star is in the grid. \n", "Goal of the agent: Go to the yellow square.\n", "Break down the overall goal into short high-level step-by-step instruction for the agent in the gridworld \n", "environment.\n", "Instruction step <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>: \n", "</pre>\n"], "text/plain": ["The gridworld environment desciprion: \n", "You are in the \u001b[1;36m4\u001b[0m rooms flat gridworld with \u001b[1;36m2\u001b[0m column and \u001b[1;36m2\u001b[0m rows of rooms.  Adjacent rooms are connected with doors. \n", "Each room is \u001b[1;36m3\u001b[0m cells wide and \u001b[1;36m3\u001b[0m cells high. The yellow square is in the grid. The blue hex is in the grid. The \n", "purple star is in the grid. \n", "Goal of the agent: Go to the yellow square.\n", "Break down the overall goal into short high-level step-by-step instruction for the agent in the gridworld \n", "environment.\n", "Instruction step \u001b[1;36m1\u001b[0m: \n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(prompt)"]}, {"cell_type": "code", "execution_count": 63, "id": "2fa7e9da-4573-44d6-ba09-2006be2d9d86", "metadata": {}, "outputs": [], "source": ["from transformers import AutoTokenizer, FlaxT5ForConditionalGeneration, T5ForConditionalGeneration, \\\n", "    FlaxGemmaForCausalLM, GemmaForCausalLM \n", "import os"]}, {"cell_type": "code", "execution_count": 64, "id": "34b4868c-29c1-47e6-bfe6-d1a06e08318f", "metadata": {}, "outputs": [{"data": {"text/plain": ["'/network/scratch/a/artem.zholus'"]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["os.environ['SCRATCH']"]}, {"cell_type": "code", "execution_count": 92, "id": "f24fa2f2-590b-4685-ad9d-723da3b8c08e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Loading checkpoint shards: 100%|████████████████████████████████████████████████████████████████████| 2/2 [00:03<00:00,  1.94s/it]\n"]}], "source": ["tokenizer = AutoTokenizer.from_pretrained(\"google/gemma-2b-it\", cache_dir=os.environ['SCRATCH'] + '/hf')\n", "model = GemmaForCausalLM.from_pretrained(\"google/gemma-2b-it\", cache_dir=os.environ['SCRATCH'] + '/hf').cuda()"]}, {"cell_type": "code", "execution_count": 72, "id": "82f72d99-7d13-4e48-b31f-5665212d0cfd", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Downloading shards: 100%|███████████████████████████████████████████████████████████████████████████| 2/2 [00:55<00:00, 27.86s/it]\n", "Loading checkpoint shards: 100%|████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.66s/it]\n"]}], "source": ["# tokenizer = AutoTokenizer.from_pretrained(\"google/flan-t5-xl\", cache_dir=os.environ['SCRATCH'] + '/hf')\n", "# model = T5ForConditionalGeneration.from_pretrained(\"google/flan-t5-xl\", \n", "#                                                    cache_dir=os.environ['SCRATCH'] + '/hf').cuda()"]}, {"cell_type": "code", "execution_count": 73, "id": "a9d4bed4-3ee5-4203-8c87-2d86ed14eb7c", "metadata": {}, "outputs": [], "source": ["import torch"]}, {"cell_type": "code", "execution_count": 74, "id": "126501ab-c3dd-46dd-b435-53d554a48d03", "metadata": {}, "outputs": [], "source": ["torch.cuda.empty_cache()"]}, {"cell_type": "code", "execution_count": 103, "id": "a656d7c8-97c6-4b4d-ab75-7ed5754379ae", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">The gridworld environment desciprion: \n", "You are in the <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4</span> rooms flat gridworld with <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span> column and <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span> rows of rooms.  Adjacent rooms are connected with doors. \n", "Each room is <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span> cells wide and <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span> cells high. The yellow square is in the grid. The blue hex is in the grid. The \n", "purple star is in the grid. \n", "Goal of the agent: Go to the yellow square.\n", "Break down the overall goal into short high-level step-by-step instruction for the agent in the gridworld \n", "environment.\n", "Instruction step <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>: \n", "</pre>\n"], "text/plain": ["The gridworld environment desciprion: \n", "You are in the \u001b[1;36m4\u001b[0m rooms flat gridworld with \u001b[1;36m2\u001b[0m column and \u001b[1;36m2\u001b[0m rows of rooms.  Adjacent rooms are connected with doors. \n", "Each room is \u001b[1;36m3\u001b[0m cells wide and \u001b[1;36m3\u001b[0m cells high. The yellow square is in the grid. The blue hex is in the grid. The \n", "purple star is in the grid. \n", "Goal of the agent: Go to the yellow square.\n", "Break down the overall goal into short high-level step-by-step instruction for the agent in the gridworld \n", "environment.\n", "Instruction step \u001b[1;36m1\u001b[0m: \n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(prompt)"]}, {"cell_type": "code", "execution_count": 124, "id": "93cae55d-2b06-4f65-bc97-98b3745641fb", "metadata": {}, "outputs": [], "source": ["# with torch.amp.autocast('cuda', dtype=torch.float16):\n", "inputs = tokenizer([prompt], return_tensors=\"pt\")\n", "gen_ids = model.generate(inputs[\"input_ids\"].cuda(), do_sample=True, \n", "                      max_new_tokens=20, top_k=0, temperature=1.4, num_return_sequences=20)\n", "texts = tokenizer.batch_decode(gen_ids[:, inputs[\"input_ids\"].shape[1]:], \n", "                               skip_special_tokens=True, clean_up_tokenization_spaces=False)"]}, {"cell_type": "code", "execution_count": 125, "id": "d41ce34f-2d96-47da-8dff-43b3ef43d5ee", "metadata": {}, "outputs": [{"data": {"text/plain": ["['\\nGo to cell B3.\\nHow do you go from one cell to another and what will',\n", " '\\nMove to cell (2, 1) \\nCreate a set of possible next movements the',\n", " '\\n- Locate the yellow square from the initial position. \\n\\nHow can the agent approach the right',\n", " '\\nHead east. \\n**Understood steps: Head right, where will you look for the door',\n", " '\\n- Start in any arbitrary room in the grid.\\n- Examine the current room and find the',\n", " '\\nKnow where your starting location is\\n\\n\\nAs in the environment, you are currently in room 1',\n", " '\\n* Move one cell downward.\\n* Get obstacle information in the current room by checking the bottom',\n", " '\\n**Cell Selection and Entry:**\\n- Determine the best cell in row 1 and column ',\n", " ' imagine what isрисо .... (outer level - once the grid becomes more familiar).\\n\\n\\n**Step ',\n", " '\\n- reach 2-3 rooms down from the grid initial position.\\n\\n\\nInstruction step 2',\n", " '\\nWhat is your current position?\\nAnswer: Your current position is in the bottom left cell (',\n", " '\\n* Start at the position [4, 1].\\n* Move right 2 steps.',\n", " '\\n- Go from room 1 to room 2.\\n\\nInvestigate nearby rooms, and reach',\n", " '\\n<PERSON><PERSON><PERSON> (ActionListener) Mysterді\\n\\nRecall: \\n1. Move to the right when',\n", " '\\n\\n\\nFollow Green Door.\\n\\n\\nThe agent moves along the vertical white space between Horizontal and Vertical corridors till',\n", " \"\\nIdentify the north, west, and south's room numbers using the A, B, and\",\n", " '\\nThe agent should move from the grid.\\n\\n\\nInstruction step 2:\\nOnce in the grid',\n", " '\\nMove down from the current room.\\n\\n\\nInstruction step 2:\\nGo left through a neighbor',\n", " '\\nFrom starting position move to the south-west neighbor of #R1C1.\\n\\nInstruction',\n", " \"\\nNavigate from the current cell to the yellow square.\\n\\n\\nRemember, it's not necessary to\"]"]}, "execution_count": 125, "metadata": {}, "output_type": "execute_result"}], "source": ["texts"]}, {"cell_type": "code", "execution_count": null, "id": "50b61284-e516-4ef1-b24d-1a4d4f68db02", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}