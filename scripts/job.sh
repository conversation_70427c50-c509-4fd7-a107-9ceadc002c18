#!/bin/bash
set -e
export IMAGE_PATH=$SCRATCH/lg_latest.sif
export CODE_PATH=$SLURM_TMPDIR/code
srun --ntasks=$SLURM_JOB_NUM_NODES --ntasks-per-node=1 mkdir -p $CODE_PATH
if [[ ! -z $WANDB_PREV_RUN ]]; then
    if test -f $WANDB_PREV_RUN; then
        mkdir -p $RUN_DIR/"$SLURM_ARRAY_JOB_ID"_"$SLURM_ARRAY_TASK_ID"
        mkdir -p $RUN_DIR/"$SLURM_ARRAY_JOB_ID"_"$SLURM_ARRAY_TASK_ID"/replay
        mkdir -p $WANDB_DIR/wandb
        export WANDB_RUN_ID=$(cat $WANDB_PREV_RUN)
        export WANDB_RESUME=allow
    fi
else
    mkdir -p $RUN_DIR/"$SLURM_ARRAY_JOB_ID"_"$SLURM_ARRAY_TASK_ID"
    mkdir -p $RUN_DIR/"$SLURM_ARRAY_JOB_ID"_"$SLURM_ARRAY_TASK_ID"/replay
    mkdir -p $WANDB_DIR/wandb
fi
echo "copying code into: $CODE_PATH"
export RUNNABLE="$@"
echo "Running: $RUNNABLE"
srun --ntasks=$SLURM_JOB_NUM_NODES --ntasks-per-node=1 cp -r OpenRLHF .netrc scripts $CODE_PATH
export POLICIES=$SCRATCH/language_policies
if [[ ! -z $HF_HOME ]]; then
    export MY_HF_HOME=$HF_HOME
else
    export MY_HF_HOME=$HOME/.cache/huggingface
fi
srun --ntasks=$SLURM_JOB_NUM_NODES --ntasks-per-node=1 ./scripts/container.sh
#while wait; test $? -gt 128; do :; done

#    --env TORCH_DISTRIBUTED_DEBUG=DETAIL \
#    --env NCCL_DEBUG=INFO \
#    --env TORCH_CPP_LOG_LEVEL=INFO \
