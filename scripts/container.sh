#!/bin/bash
exec singularity exec --nv \
    -H $CODE_PATH:/home \
    -B $SCRATCH/hf:/home/<USER>
    -B $POLICIES:/home/<USER>/language_policies \
    -B $RUN_DIR/"$SLURM_ARRAY_JOB_ID"_"$SLURM_ARRAY_TASK_ID":/home/<USER>
    -B $WANDB_DIR/wandb:/home/<USER>
    -B $MY_HF_HOME:/home/<USER>
    --cleanenv \
    --env WANDB_DIR=/home \
    --env MUJOCO_GL=osmesa \
    --env SCRATCH=/home/<USER>/hf \
    --env POLICIES=/home/<USER>/language_policies \
    --env XLA_PYTHON_CLIENT_PREALLOCATE=false \
    --env HF_HOME=/home/<USER>
    --env WANDB_ENTITY=$WANDB_ENTITY \
    --env WANDB_PROJECT=$WANDB_PROJECT \
    --env WANDB_MODE=$WANDB_MODE \
    --env HF_HUB_OFFLINE=YES \
    --env OMP_NUM_THREADS=20 \
    --env MASTER_PORT=$MASTER_PORT \
    --env MASTER_ADDR=$MASTER_ADDR \
    --env N_GPUS=$N_GPUS \
    --env N_NODES=$N_NODES \
    --env SLURM_PROCID=$SLURM_PROCID \
    --env SLURM_ARRAY_JOB_ID=$SLURM_ARRAY_JOB_ID \
    --env SLURM_ARRAY_TASK_ID=$SLURM_ARRAY_TASK_ID \
    --env USER_ARGS="$USER_ARGS" \
    --pwd /home/<USER>/examples \
    $IMAGE_PATH \
    torchrun \
    --nproc_per_node $N_GPUS \
    --nnodes $N_NODES \
    --node_rank $SLURM_PROCID \
    --master_addr $MASTER_ADDR --master_port $MASTER_PORT \
    $RUNNABLE \
    --slurm_job "$SLURM_ARRAY_JOB_ID"_"$SLURM_ARRAY_TASK_ID"

#    --env TORCH_DISTRIBUTED_DEBUG=DETAIL \
#    --env NCCL_DEBUG=INFO \
#    --env TORCH_CPP_LOG_LEVEL=INFO \
