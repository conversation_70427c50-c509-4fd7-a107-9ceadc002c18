# This viminfo file was generated by Vim 8.2.
# You may edit it if you're careful!

# Viminfo version
|1,4

# Value of 'encoding' when this file was written
*encoding=utf-8


# hlsearch on (H) or off (h):
~h
# Last Substitute String:
$

# Command Line History (newest to oldest):
:q
|2,0,1714510957,,"q"

# Search String History (newest to oldest):
? @$
|2,1,1714510954,,"@$"

# Expression History (newest to oldest):

# Input Line History (newest to oldest):

# Debug Line History (newest to oldest):

# Registers:

# File marks:
'0  1  0  ~/.cache/huggingface/token
|4,48,1,0,1714510957,"~/.cache/huggingface/token"

# Jumplist (newest first):
-'  1  0  ~/.cache/huggingface/token
|4,39,1,0,1714510957,"~/.cache/huggingface/token"
-'  10  0  ~/.cache
|4,39,10,0,1714510954,"~/.cache"

# History of marks within files (newest to oldest):

> ~/.cache/huggingface/token
	*	1714510956	0
	"	1	0
