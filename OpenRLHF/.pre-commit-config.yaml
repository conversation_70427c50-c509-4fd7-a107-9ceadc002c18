default_language_version:
  python: python3

ci:
  autofix_prs: true
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit suggestions'
  autoupdate_schedule: quarterly

repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: check-yaml
      - id: check-case-conflict
      - id: detect-private-key
      - id: check-added-large-files
        args: ['--maxkb=1000']
      - id: requirements-txt-fixer

  - repo: https://github.com/PyCQA/isort
    rev: 5.13.2
    hooks:
      - id: isort
        name: Format imports
        exclude: docs/

  - repo: https://github.com/psf/black
    rev: 24.3.0
    hooks:
      - id: black
        name: Format code
        additional_dependencies: ['click==8.0.2']
