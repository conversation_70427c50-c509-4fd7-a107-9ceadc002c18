name: OPENRLHF PyPi Nightly

on:
  schedule:
    - cron: '0 0 * * *'
  # can manually trigger the workflow
  workflow_dispatch:

jobs:
  build-and-publish:
    # do not run in forks
    if: ${{ github.repository_owner == 'OpenLLMAI' }}
    name: build wheel and upload
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2

    - name: Set up Python 3.10
      uses: actions/setup-python@v1
      with:
        python-version: 3.10.13

    - name: days since the commit date
      run: |
          :
          timestamp=$(git log --no-walk --date=unix --format=%cd $GITHUB_SHA)
          days=$(( ( $(date --utc +%s) - $timestamp ) / 86400 ))
          if [ $days -eq 0 ]; then
              echo COMMIT_TODAY=true >> $GITHUB_ENV
          fi
    - name: Build wheel
      if: env.COMMIT_TODAY == 'true'
      env:
        OPENRLHF_BUILD_MODE: nightly
      run: |
          pip install pytest torch cloudpickle cryptography deepspeed # TODO(qwang): installing dev requirements.
          pip install ray[default]==2.7.0
          python setup.py bdist_wheel
    - name: Upload
      if: env.COMMIT_TODAY == 'true'
      uses: pypa/gh-action-pypi-publish@release/v1
      with:
        password: ${{ secrets.PYPI_API_TOKEN }}
