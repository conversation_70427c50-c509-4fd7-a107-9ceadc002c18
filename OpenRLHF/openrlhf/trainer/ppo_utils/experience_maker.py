import logging
import time
import os
from abc import ABC
from copy import deepcopy
from dataclasses import dataclass
import numpy as np
from typing import List, Optional, Tuple, Union

import ray
import torch
from torch import distributed as dist
import torch.nn as nn
from tqdm import tqdm

from browsergym.experiments.loop import StepInfo
from agentlab.agents.generic_agent.agent_configs import FLAGS_8B
from agentlab.agents import dynamic_prompting as dp
from openrlhf.models.actor import Actor
from openrlhf.models.utils import compute_reward, masked_mean
from openrlhf.utils.logging import init_logger

logger = init_logger(__name__)


@dataclass
class Experience:
    """Experience is a batch of data.
    These data should have the the sequence length and number of actions.
    Left padding for sequences is applied.

    Shapes of each tensor:
    sequences: (B, S)
    action_log_probs: (B, A)
    values: (B, A)
    returns: (B, A)
    advatanges: (B, A)
    attention_mask: (B, S)
    action_mask: (B, A)

    "A" is the number of actions.
    """

    sequences: torch.Tensor
    action_log_probs: torch.Tensor
    values: torch.Tensor
    returns: torch.Tensor
    advantages: torch.Tensor
    attention_mask: Optional[torch.LongTensor]
    action_mask: Optional[torch.BoolTensor]
    info: Optional[dict]

    @torch.no_grad()
    def to_device(self, device: torch.device) -> None:
        self.sequences = self.sequences.to(device)
        self.action_log_probs = self.action_log_probs.to(device)
        self.values = self.values.to(device)
        self.returns = self.returns.to(device)
        self.advantages = self.advantages.to(device)
        if self.attention_mask is not None:
            self.attention_mask = self.attention_mask.to(device)
        if self.action_mask is not None:
            self.action_mask = self.action_mask.to(device)

    def pin_memory(self):
        self.sequences = self.sequences.pin_memory()
        self.action_log_probs = self.action_log_probs.pin_memory()
        self.values = self.values.pin_memory()
        self.returns = self.returns.pin_memory()
        self.advantages = self.advantages.pin_memory()
        if self.attention_mask is not None:
            self.attention_mask = self.attention_mask.pin_memory()
        if self.action_mask is not None:
            self.action_mask = self.action_mask.pin_memory()
        return self


class NaiveExperienceMaker(ABC):
    """
    Naive experience maker.
    """

    def __init__(
        self,
        actor: Actor,
        actor_low: Actor,
        critic: nn.Module,
        reward_model: nn.Module,
        initial_model: Actor,
        tokenizer,
        critic_tokenizer,
        prompt_max_len: int,
        kl_controller,
        strategy=None,
        reward_fn=None,
    ) -> None:
        super().__init__()
        self.actor = actor
        self.actor_low = actor_low
        self.critic = critic
        self.critic_tokenizer = critic_tokenizer
        self.reward_model = reward_model
        self.initial_model = initial_model
        self.tokenizer = tokenizer
        self.prompt_max_len = prompt_max_len
        self.kl_ctl = kl_controller
        self.strategy = strategy
        self.reward_fn = reward_fn
        self.last_obs = {}
        self.last_subgoal = {} ## I added
        self.obs_preprocessor = dp.make_obs_preprocessor(FLAGS_8B.obs)

    # tokenizer
    def tokenize_fn(self, texts, max_length, device):
        batch = self.tokenizer(
            texts,
            return_tensors="pt",
            max_length=max_length,
            padding=True,
            truncation=True,
        )
        return {k: v.to(device) for k, v in batch.items()}

    @torch.no_grad()
    def make_experience(
            self, prompts: Union[str, List[str]]=None, prompters=None, envs=None, reset_env=False, **generate_kwargs
        ) -> Experience:
        self.actor.eval()
        self.critic.eval()
        self.initial_model.eval()
        self.actor_low.eval() ## this is fixed
        # self.reward_model.eval()
        torch.cuda.set_device(int(os.environ.get('LOCAL_RANK', 0)))
        # TODO: vectorize
        batch_inputs = [] 
        t = time.time()
        prompt_texts_high = []
        prompt_texts_low = []
        ## this loop adds info to the prompt
        for k in range(len(envs)):
            if reset_env:
                prompters[k].reset()
                step_info = StepInfo(step=0)
                episode_info = [step_info]

                step_info.from_reset(
                    envs[k], seed=10 + k, obs_preprocessor=self.obs_preprocessor
                )
                self.last_obs[k] = step_info.obs
                self.last_subgoal[k] = '' ## I added
                subgoal = 'start playing the game.' ## we can chnage it to a better prompt later
                # print(step_info.obs)
                prompt_high = prompters[k].next_prompt(step_info.obs, subgoal, Low=False, High=True) ## I changed
                prompt_low = prompters[k].next_prompt(step_info.obs, subgoal, Low=True, High=False) ## I changed

                prompt_texts_high.append(prompt_high)
                prompt_texts_low.append(prompt_low)
                
            elif prompts is None:
                prompt_high = prompters[k].next_prompt(self.last_obs[k], self.last_subgoal[k], Low=False, High=True)
                prompt_texts_high.append(prompt_high)
                prompt_low = prompters[k].next_prompt(self.last_obs[k], self.last_subgoal[k], Low=True, High=False)
                prompt_texts_low.append(prompt_low)
                # inputs = self.tokenize_fn(prompt, self.prompt_max_len, device="cuda")
            else:
                # generate seq
                prompt_texts_high.append(prompt_high)
                prompt_texts_low.append(prompt_low)
                # inputs = self.tokenize_fn(prompts, self.prompt_max_len, device="cuda")
            # batch_inputs.append(inputs)
        print(f'reset time: {time.time() - t:.3f}sec')
        t = time.time()

        inputs = self.tokenize_fn(prompt_texts_high, self.prompt_max_len, device="cuda")
        inputs_low = self.tokenize_fn(prompt_texts_low, self.prompt_max_len, device="cuda")

        sequences, attention_mask, action_mask = self.actor.generate(**inputs, **generate_kwargs) ## this is pi_high so action is subgoal
        num_actions = action_mask.size(1)
        print(f'sample time: {time.time() - t:.3f}sec')

        
        sequences_low, attention_mask_low, action_mask_low = self.actor_low.generate(**inputs_low, **generate_kwargs) ## this is pi_low so action is primary action
        num_actions_low = action_mask_low.size(1)
        print(f'sample time: {time.time() - t:.3f}sec')



        dist.barrier()
        # log probs
        t = time.time()
        action_log_probs = self.actor(sequences, num_actions, attention_mask)

        # init log probs
        base_action_log_probs = self.initial_model(sequences, num_actions, attention_mask)

        # values
        value = self.critic(sequences, action_mask, attention_mask)

        # rewards
        r = []
        for k_low, (seq_low, act_mask_low) in enumerate(zip(sequences_low, action_mask_low)):
            curr_actions_low = seq_low[-num_actions_low:][act_mask_low][2:]
            curr_actions_low = self.tokenizer.decode(curr_actions_low, skip_special_tokens=True)
            step_info = StepInfo(step=0)
            step_info.from_step(envs[k], curr_actions_low, obs_preprocessor=self.obs_preprocessor)
            self.last_obs[k] = step_info.obs
            r.append(step_info.reward)
            prompters[k].actions.append(curr_actions_low)
            prompters[k].memories.append(None)
            prompters[k].thoughts.append(None)


        for k, (seq, act_mask) in enumerate(zip(sequences, action_mask)):
            curr_actions = seq[-num_actions:][act_mask][2:]
            curr_actions = self.tokenizer.decode(curr_actions, skip_special_tokens=True)
            # step_info = StepInfo(step=0)
            # step_info.from_step(envs[k], curr_actions, obs_preprocessor=self.obs_preprocessor)
            self.last_subgoal[k] = curr_actions
            subgoal = curr_actions
            # r.append(step_info.reward)
            # prompters[k].actions.append(curr_actions)
            # prompters[k].memories.append(None)
            # prompters[k].thoughts.append(None)
            
        r = torch.from_numpy(np.array(r)).to(sequences.device)
        # text_sequences = self.tokenizer.batch_decode(sequences, skip_special_tokens=True)
    
        # r = self.reward_model(text_sequences, attention_mask)

        reward, kl = compute_reward(
            r,
            self.kl_ctl.value,
            action_log_probs,
            base_action_log_probs,
            action_mask=action_mask,
        )
        advantage, returns = self.get_advantages_and_returns(
            value,
            reward,
            action_mask,
            generate_kwargs["gamma"],
            generate_kwargs["lambd"],
        )  
        print(f'postproc time: {time.time() - t:.3f}sec')

        info = {
            "kl": masked_mean(kl, action_mask, dim=-1),
            "reward": r,
            "return": reward.sum(dim=-1),
            "response_length": action_mask.float().sum(dim=-1),
            "total_length": attention_mask.float().sum(dim=-1),
        }
        # reset model state
        self.actor.train()
        self.critic.train()

        return Experience(
            sequences,
            action_log_probs,
            value,
            returns,
            advantage,
            attention_mask,
            action_mask,
            info,
        )

    @torch.no_grad()
    def get_advantages_and_returns(
        self,
        values: torch.Tensor,
        rewards: torch.Tensor,
        action_mask: torch.Tensor,
        gamma: float,
        lambd: float,
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """Function that computes advantages and returns from rewards and values.
        Calculated as in the original PPO paper: https://arxiv.org/abs/1707.06347
        Note that rewards may include a KL divergence loss term.

        Advantages looks like this:
        Adv1 =  R1 + γ * λ * R2     + γ^2 * λ^2 * R3       + ...
              - V1 + γ * (1 - λ) V2 + γ^2 * λ * (1 - λ) V3 + ...

        Returns looks like this:
        Ret1 =  R1 + γ * λ * R2     + γ^2 * λ^2 * R3       + ...
                   + γ * (1 - λ) V2 + γ^2 * λ * (1 - λ) V3 + ...

        Input:
        - values: Tensor of shape (batch_size, response_size)
        - rewards: Tensor of shape (batch_size, response_size)

        Output:
        - advantages: Tensor of shape (batch_size, response_size)
        - returns: Tensor of shape (batch_size, response_size)
        """
        lastgaelam = 0
        advantages_reversed = []
        response_length = rewards.size(1)

        # Mask invalid responses
        values = action_mask * values
        rewards = action_mask * rewards

        for t in reversed(range(response_length)):
            nextvalues = values[:, t + 1] if t < response_length - 1 else 0.0
            delta = rewards[:, t] + gamma * nextvalues - values[:, t]
            lastgaelam = delta + gamma * lambd * lastgaelam
            advantages_reversed.append(lastgaelam)
        advantages = torch.stack(advantages_reversed[::-1], dim=1)
        returns = advantages + values
        return advantages.detach(), returns


class RemoteExperienceMaker(NaiveExperienceMaker):
    def __init__(self, *args, vllm_engines: List = None, **kwargs):
        super().__init__(*args, **kwargs)
        self.vllm_engines = vllm_engines

    @torch.no_grad()
    def make_experience(self, prompts: Union[str, List[str]], **generate_kwargs) -> Experience:
        self.actor.eval()
        device = torch.cuda.current_device()

        # generate sequence
        start = time.time()
        sequences, attention_mask, action_mask = (
            self._generate_local(prompts, **generate_kwargs)
            if self.vllm_engines is None
            else self._generate_vllm(prompts, **generate_kwargs)
        )
        generate_time = time.time() - start

        num_actions = action_mask.size(1)
        sequences_cpu, attention_mask_cpu, action_mask_cpu = (
            sequences.to("cpu"),
            attention_mask.to("cpu"),
            action_mask.to("cpu"),
        )

        # init log probs
        base_action_log_probs_ref = self.initial_model.forward.remote(sequences_cpu, num_actions, attention_mask_cpu)

        # values
        value_ref = self.critic.forward.remote(sequences_cpu, action_mask_cpu, attention_mask_cpu)

        # rewards
        r_refs = []
        for rm in self.reward_model:
            r_refs.append(rm.forward.remote(sequences_cpu, attention_mask_cpu))

        # log probs
        start = time.time()
        action_log_probs = self.actor(sequences, num_actions, attention_mask)
        actor_time = time.time() - start

        # wait initial/critic/reward model done
        start = time.time()
        ref_values = ray.get([base_action_log_probs_ref, value_ref] + r_refs)
        wait_time = time.time() - start

        base_action_log_probs, value, rewards = ref_values[0], ref_values[1], ref_values[2:]
        base_action_log_probs, value = base_action_log_probs.to(device), value.to(device)
        rewards = [r.to(device) for r in rewards]
        r = self.reward_fn(rewards) if len(rewards) > 0 else rewards[0]

        reward, kl = compute_reward(
            r,
            self.kl_ctl.value,
            action_log_probs,
            base_action_log_probs,
            action_mask=action_mask,
        )
        advantage, returns = self.get_advantages_and_returns(
            value,
            reward,
            action_mask,
            generate_kwargs["gamma"],
            generate_kwargs["lambd"],
        )

        info = {
            "kl": masked_mean(kl, action_mask, dim=-1),
            "reward": r,
            "return": reward.sum(dim=-1),
            "response_length": action_mask.float().sum(dim=-1),
            "total_length": attention_mask.float().sum(dim=-1),
        }

        if self.strategy.args.perf:
            batch_size = 1 if isinstance(prompts, str) else len(prompts)
            info["generate_time"] = torch.full((batch_size,), generate_time, device=device)
            info["actor_time"] = torch.full((batch_size,), actor_time, device=device)
            info["wait_time"] = torch.full((batch_size,), wait_time, device=device)

        experience = Experience(
            sequences,
            action_log_probs,
            value,
            returns,
            advantage,
            attention_mask,
            action_mask,
            info,
        )

        # send experience to critic
        experience_cpu = deepcopy(experience)
        experience_cpu.to_device("cpu")
        self._ref = self.critic.append.remote(experience_cpu)

        self.actor.train()  # reset model state
        return experience

    def _generate_local(self, prompts: List[str], **kwargs) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        inputs = self.tokenize_fn(prompts, self.prompt_max_len, device="cuda")
        return self.actor.generate(**inputs, **kwargs)

    def _generate_vllm(self, prompts: List[str], **kwargs) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        from vllm import SamplingParams

        # round-robin load balance
        rank = torch.distributed.get_rank()
        llm = self.vllm_engines[rank % len(self.vllm_engines)]

        sampling_params = SamplingParams(
            temperature=kwargs.get("temperature", 1.0),
            top_p=kwargs.get("top_p", 1.0),
            top_k=kwargs.get("top_k", -1),
            max_tokens=kwargs.get("max_new_tokens", 16),
        )

        # TODO: can't pass `max_length` to vLLM's tokenizer for input truncation, remove this once it is supported.
        input_ids = self.tokenize_fn(prompts, self.prompt_max_len, device="cpu")["input_ids"]
        assert self.tokenizer.padding_side == "left", f"tokenizer padding_size should be left"
        pad_indices = (input_ids != self.tokenizer.pad_token_id).to(dtype=torch.int).argmax(dim=-1)
        prompt_token_ids = []
        for i, pad_index in enumerate(pad_indices.numpy()):
            prompt_token_ids.append(input_ids[i][pad_index:].tolist())
        outputs = ray.get(llm.generate.remote(sampling_params=sampling_params, prompt_token_ids=prompt_token_ids))

        # NOTE: concat all outputs to following format:
        #
        # | [PAD] [PAD] token token token | token token [EOS] [PAD] |
        # | token token token token token | token token [EOS] [PAD] |
        # | [PAD] [PAD] [PAD] token token | token token token [EOS] |
        # |<---------- prompt ----------->|<-------- answer ------->|
        max_input_len, max_output_len = 0, 0
        for output in outputs:
            # TODO: how to force vLLM generate at least one token?
            output_token_ids = output.outputs[0].token_ids
            if output_token_ids[0] == self.tokenizer.eos_token_id:
                logger.warning(f"Only EOS output for prompt: {output.prompt}")
                output.outputs[0].token_ids = [self.tokenizer.unk_token_id, self.tokenizer.eos_token_id]

            max_input_len = max(max_input_len, len(output.prompt_token_ids))
            max_output_len = max(max_output_len, len(output_token_ids))

        pad_token_id, eos_token_id = self.tokenizer.pad_token_id, self.tokenizer.eos_token_id
        sequences = []
        for output in outputs:
            # left padding input
            input_len = len(output.prompt_token_ids)
            input_ids = [pad_token_id] * (max_input_len - input_len) + output.prompt_token_ids

            # right padding output
            output_len = len(output.outputs[0].token_ids)
            output_ids = output.outputs[0].token_ids + [pad_token_id] * (max_output_len - output_len)
            if output_ids[output_len - 1] != eos_token_id:
                assert output_len == max_output_len
                output_ids[-1] = eos_token_id

            # concat input and output
            sequences.append(input_ids + output_ids)

        sequences = torch.tensor(sequences)
        sequences, attention_mask, action_mask = self.actor.process_sequences(
            sequences, max_input_len, eos_token_id, pad_token_id
        )
        return sequences.to("cuda"), attention_mask.to("cuda"), action_mask.to("cuda")

    def flush(self):
        "Ensure all experience has been send to critic"
        ray.get(self._ref)
        self._ref = None
