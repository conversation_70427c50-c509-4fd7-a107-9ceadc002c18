import argparse
import itertools
import math
import os
from copy import deepcopy
from datetime import datetime

import sys
sys.path.insert(0, '..')

import torch
from transformers.trainer import get_scheduler

from openrlhf.datasets import PromptDataset, SFTDataset
from openrlhf.models import Actor, get_llm_for_sequence_regression
from openrlhf.trainer import PPOTrainer
from openrlhf.utils import blending_datasets, get_strategy, get_tokenizer

# from embodied_reward import EmbodiedReward


class FixedPromptsDataset:
    def __init__(self) -> None:
        # self.goal_map = {
        #     5: 'ball',
        #     6: 'square',
        #     7: 'pyramid',
        #     8: 'goal',
        #     9: 'key',
        #     10: 'locked door',
        #     11: 'closed door',
        #     12: 'open door',
        #     13: 'hex',
        #     14: 'star'
        # }
        # self.color_map = {
        #     3: 'red',
        #     4: 'green',
        #     5: 'blue',
        #     6: 'purple',
        #     7: 'yellow',
        #     8: 'grey',
        #     9: 'black',
        #     10: 'orange',
        #     11: 'white',
        #     12: 'brown',
        #     13: 'pink'
        # }

        self.tiles_type = {
            # 'EMPTY': 0,
            # 'END_OF_MAP': 1,
            # 'UNSEEN': 2,
            # 'FLOOR': 3,
            # 'WALL': 4,
            'BALL': 3,
            'SQUARE': 4,
            'PYRAMID': 5,
            'GOAL': 6,
            'KEY': 7,
            'DOOR_LOCKED': 8,
            'DOOR_CLOSED': 9,
            'DOOR_OPEN': 10,
            'HEX': 11,
            'STAR': 12}

        self.colors_type = {
            # 'EMPTY': 0,
            # 'END_OF_MAP': 1,
            # 'UNSEEN': 2,
            'RED': 1,
            'GREEN': 2,
            'BLUE': 3,
            'PURPLE': 4,
            'YELLOW': 5,
            'GREY': 6,
            'BLACK': 7,
            'ORANGE': 8,
            'WHITE': 9,
            'BROWN': 10,
            'PINK': 11}

        # self.pattern = \
        #     "answer the users question as best as possible. \n" \
        #     "You are in the 4 rooms flat gridworld with 2 column and 2 rows of rooms.\n " \
        #     "Adjacent rooms are connected with doors. \n" \
        #     f"The objects in the environment are: {', '.join(self.goal_map.values())} \n" \
        #     f"The colors in the environment are: {', '.join(self.color_map.values())} \n" \
        #     "Given the goal of the agent please provide what is the object and what is the color of the object that the agent should approach to? \n" \
        #     'The output should be a markdown code snippet formatted in the following schema, ' \
        #     'including the leading and trailing "```json" and "```":\n' \
        #     '```json\n' \
        #     '{{\n' \
        #     '    "object": string,  // the target object type\n' \
        #     '    "color": string  // target object color\n' \
        #     '}}\n' \
        #     '```\n' \
        #     "Exmaple 1: Goal of the agent is: 'Go to the green square'. Answer: \n" \
        #     '```json\n' \
        #     '{{\n' \
        #     '    "object": "square", \n' \
        #     '    "color": "green" \n' \
        #     '}}\n' \
        #     '```\n' \
        #     "Exmaple 2: Goal of the agent is: 'Pick up the blue key'. Answer: \n" \
        #     '```json\n' \
        #     '{{\n' \
        #     '    "object": "key," \n' \
        #     '    "color": "blue" \n' \
        #     '}}\n' \
        #     '```\n' \
        #     "New: Goal of the agent is: 'Go to the {clr} {objt}'. Answer: \n"
            # "Provide the answer: \n"
        
        self.combinations = [
            ((j, k), (c, o))
            for j, c in enumerate(self.colors_type.values())
            for k, o in enumerate(self.tiles_type.values())
        ]
        
        self.high_act_list=[]
    
    
    ## Add description
    def describe(self, timestep):
        shape_ = timestep.state.grid.shape
        num_cols= shape_[0]//8
        num_rows = shape_[1]//8
        # number_rooms= env_params.grid_type
        room_w= 8
        room_h= 8 #self.room_grid[0][0].size
        player_room_x, player_room_y = timestep.state.agent.position
        player_room_x_room, player_x = player_room_x //room_w , player_room_x % room_w
        player_room_y_room, player_y = player_room_y //room_h , player_room_y % room_h


        objects = 'Here is the objects of each room. '

        items_in_env = {'obj':[], 'color':[]}
        for obj in self.tiles_type.keys():
            for color in self.colors_type.keys():
                items = ((timestep.state.grid[..., 0] == self.tiles_type[obj]) & (timestep.state.grid[..., 1] == self.colors_type[color])).nonzero()
                # print(items)
                for i in range(len(items[0])):
                    items_in_env['obj'].append(obj)
                    items_in_env['color'].append(color)
                    objects += f' a {color} {obj} at {items[1][i]} x-axis and {items[0][i]} y-axis coordination in room at column {items[1][i]//8} and row {items[0][i]//8}, \n'
        
        return (f"You are in the {num_cols * num_rows} rooms flat" +
                f" gridworld with {num_cols} column and {num_rows} rows of rooms. \n" +
                f" This environment can have any of these object: \n {self.tiles_type.keys()} \n "+
                f"with any of these colors: \n"+
                f" {self.colors_type.keys()}. \n"+
                f"/this is World Map: You are located in the room at column {player_room_x_room} and row {player_room_y_room}. \n" +
                f"Your position within that room is ({player_x}, {player_y}) and in the environment is {player_room_x}, {player_room_y} \n"
            " Adjacent rooms are connected with doors. \n" +
            "Each room is {} cells wide and {} cells high. \n ".format(room_w, room_h) +
            objects), items_in_env
    
    ## Add prompt maker function
    def make_prompt(self, timestep, high_act_list, clr, objt):
        ''' it build the input of the LLM'''
        goal = f'Go to the {clr} {objt}'
        desc, _= self.describe(timestep)
        find_goal_prompt = 'What is the current high level action that the agent should do in order to achieve to gosl? You should specify the action using the World Map and the objects in the environment \n'
        if len(high_act_list) == 0: end='No high level action is done yet!\n'
        else:
            end = 'These are the hight level actions which are done till now: \n'
            for i in len(high_act_list):
                end += str(high_act_list[i]) + ', \n'
        end += find_goal_prompt

        prompt_input_LLM = desc + f"Your goal is {goal}. \n Assume the agent can do either 'pick-up an object' or 'open a door'. {end} \n "\
        " The output should be a markdown code snippet formatted in the following schema, \n"\
        'including the leading and trailing "```json" and "```":\n'\
        '```json\n'\
        '```json\n' \
        '{{\n' \
        '    "action": string,  // the target action type\n' \
        '    "color": string  // target object color\n' \
        '    "object": string  // the target object type\n' \
        '}}\n' \
        '```\n' \
        "Exmaple 1: The high level action is pick-up a green key. Answer: \n" \
        '```json\n' \
        '{{\n' \
        '    "action": "pick-up", \n' \
        '    "color": "green" \n' \
        '    "object": "key" \n' \
        '}}\n' \
        '```\n' \
        "Exmaple 2: The high level action is open the red door. Answer \n" \
        '```json\n' \
        '{{\n' \
        '    "action": "open," \n' \
        '    "color": "red" \n' \
        '    "object": "door" \n' \
        '}}\n' \
        '. Just generate the action: '

        return prompt_input_LLM
    
    
    def __len__(self):
        return len(self.combinations) * 10000
    
    def __getitem__(self, item, timestep):
        idx = item % 100
        addr, (color, object) = self.combinations[idx]
        return self.make_prompt(self, timestep, self.high_act_list, color, object)
        # return self.pattern.format(clr=color, objt=object)
        # return torch.tensor(addr), self.pattern.format(color=color, object=object)

def train(args):
    # configure strategy
    # import pdb; pdb.set_trace()
    strategy = get_strategy(args)
    strategy.setup_distributed()

    # configure model
    # load huggingface model
    actor = Actor(
        args.pretrain,
        use_flash_attention_2=args.flash_attn,
        bf16=args.bf16,
        load_in_4bit=args.load_in_4bit,
        lora_rank=args.lora_rank,
        lora_alpha=args.lora_alpha,
        target_modules=args.target_modules,
        ds_config=strategy.get_ds_train_config(is_actor=True),
    )

    if args.actor_init_on_gpu:
        actor = actor.to(torch.cuda.current_device())

    critic = get_llm_for_sequence_regression(
        args.reward_pretrain,
        "critic",
        normalize_reward=args.normalize_reward,
        use_flash_attention_2=args.flash_attn,
        bf16=args.bf16,
        load_in_4bit=args.load_in_4bit,
        lora_rank=args.lora_rank,
        lora_alpha=args.lora_alpha,
        target_modules=args.target_modules,
        ds_config=strategy.get_ds_train_config(is_actor=False),
        cache_dir=os.environ['SCRATCH'] + '/llama3/models',
    )
    # reward_model = get_llm_for_sequence_regression(
    #     args.reward_pretrain,
    #     "reward",
    #     normalize_reward=args.normalize_reward,
    #     use_flash_attention_2=args.flash_attn,
    #     bf16=args.bf16,
    #     load_in_4bit=args.load_in_4bit,
    #     ds_config=strategy.get_ds_train_config(is_actor=False),
    # )
    # reward_model = EmbodiedReward()
    reward_model = lambda *args, **kwargs: 1.0

    # configure tokenizer
    tokenizer = get_tokenizer(args.pretrain, actor.model, "left", strategy, use_fast=not args.disable_fast_tokenizer)
    critic_tokenizer = get_tokenizer(args.reward_pretrain, critic, "left", strategy, use_fast=not args.disable_fast_tokenizer)
    # get_tokenizer(args.reward_pretrain, reward_model, "left", strategy, use_fast=not args.disable_fast_tokenizer)

    strategy.print(actor)
    strategy.print(critic)

    # load weights for reference actor
    initial_model = Actor(
        args.pretrain,
        use_flash_attention_2=args.flash_attn,
        bf16=args.bf16,
        load_in_4bit=args.load_in_4bit,
        ds_config=strategy.get_ds_eval_config(offload=False),
    )
    get_tokenizer(args.pretrain, initial_model.model, "left", strategy)

    strategy.print("reward normalization status: {}".format(args.normalize_reward))
    # strategy.print("mean: {}, std {}".format(reward_model.mean, reward_model.std))

    if args.enable_ema:
        ema_model = Actor(
            args.pretrain,
            use_flash_attention_2=args.flash_attn,
            bf16=args.bf16,
            load_in_4bit=args.load_in_4bit,
            ds_config=strategy.get_ds_eval_config(offload=True),
        )
    else:
        ema_model = None

    # configure optimizer
    actor_optim = strategy.create_optimizer(
        actor, lr=args.actor_learning_rate, betas=(0.9, 0.95), weight_decay=args.l2
    )
    critic_optim = strategy.create_optimizer(
        critic, lr=args.critic_learning_rate, betas=(0.9, 0.95), weight_decay=args.l2
    )

    # prepare datasets
    # prompts_data = blending_datasets(
    #     args.prompt_data,
    #     args.prompt_data_probs,
    #     strategy,
    #     args.seed,
    #     max_count=args.max_samples,
    #     return_eval=False,
    # )
    # prompts_data = prompts_data.select(range(min(args.max_samples, len(prompts_data))))
    # prompts_dataset = PromptDataset(prompts_data, tokenizer, strategy, input_template=args.input_template)
    prompts_dataset = FixedPromptsDataset()
    prompts_dataloader = strategy.setup_dataloader(prompts_dataset, args.micro_rollout_batch_size, True, True)

    if args.pretrain_data:
        pretrain_data = blending_datasets(
            args.pretrain_data,
            args.pretrain_data_probs,
            strategy,
            args.seed,
            return_eval=False,
        )
        pretrain_max_len = args.max_len if args.max_len else args.prompt_max_len + args.generate_max_len
        pretrain_dataset = SFTDataset(
            pretrain_data.select(range(min(len(pretrain_data), args.max_epochs * len(prompts_dataset)))),
            tokenizer,
            pretrain_max_len,
            strategy,
            pretrain_mode=True,
        )
        pretrain_dataloader = itertools.cycle(
            iter(
                strategy.setup_dataloader(
                    pretrain_dataset,
                    args.micro_train_batch_size,
                    True,
                    True,
                    pretrain_dataset.collate_fn,
                )
            )
        )
    else:
        pretrain_dataloader = None

    # configure scheduler
    num_update_steps_per_episodes = (
        int(len(prompts_dataloader) * (args.micro_rollout_batch_size / args.micro_train_batch_size))
        * args.max_epochs
        // strategy.accumulated_gradient
    )

    max_steps = math.ceil(args.num_episodes * num_update_steps_per_episodes)

    actor_scheduler = get_scheduler(
        "cosine",
        actor_optim,
        num_warmup_steps=math.ceil(max_steps * 0.03),
        num_training_steps=max_steps,
    )

    critic_scheduler = get_scheduler(
        "cosine",
        critic_optim,
        num_warmup_steps=math.ceil(max_steps * 0.03),
        num_training_steps=max_steps,
    )

    # gradient_checkpointing
    if args.gradient_checkpointing:
        actor.gradient_checkpointing_enable(
            gradient_checkpointing_kwargs={"use_reentrant": args.gradient_checkpointing_use_reentrant}
        )
        critic.gradient_checkpointing_enable(
            gradient_checkpointing_kwargs={"use_reentrant": args.gradient_checkpointing_use_reentrant}
        )

    # prepare models/optimizers...
    (
        (actor, actor_optim, actor_scheduler),
        (critic, critic_optim, critic_scheduler),
        # reward_model,
        initial_model,
    ) = strategy.prepare(
        (actor, actor_optim, actor_scheduler),
        (critic, critic_optim, critic_scheduler),
        # reward_model,
        initial_model,
        is_rlhf=True,
    )

    if ema_model:
        ema_model._offload = True
        ema_model = strategy.prepare(ema_model, is_rlhf=True)

    # load checkpoint
    if args.load_checkpoint:
        strategy.print("Load checkpoint: ", args.save_path)

    os.makedirs(args.save_path, exist_ok=True)

    # configure Trainer
    trainer = PPOTrainer(
        strategy,
        actor,
        critic,
        reward_model,
        initial_model,
        ema_model,
        actor_optim,
        critic_optim,
        actor_scheduler,
        critic_scheduler,
        max_epochs=args.max_epochs,
        micro_train_batch_size=args.micro_train_batch_size,
        micro_rollout_batch_size=args.micro_rollout_batch_size,
        gradient_checkpointing=args.gradient_checkpointing,
        tokenizer=tokenizer,
        critic_tokenizer=critic_tokenizer,
        prompt_max_len=args.prompt_max_len,
        value_clip=args.value_clip,
        eps_clip=args.eps_clip,
        gamma=args.gamma,
        lambd=args.lambd,
        init_kl_coef=args.init_kl_coef,
        kl_target=args.kl_target,
        ema_beta=0.992,
        ptx_coef=args.ptx_coef,
        max_norm=args.max_norm,
        # fro GPT generation
        do_sample=True,
        max_new_tokens=args.generate_max_len,
        max_length=args.max_len,
        temperature=args.temperature,
        top_p=args.top_p,
        pad_token_id=tokenizer.pad_token_id,
        eos_token_id=tokenizer.eos_token_id,
    )

    trainer.fit(
        prompts_dataloader,
        pretrain_dataloader,
        args,
    )

    # save model checkpoint after fitting on only rank0
    strategy.save_model(
        ema_model if args.enable_ema else actor,
        tokenizer,
        args.save_path,
    )


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--prompt_data", type=str, default=None)
    parser.add_argument(
        "--prompt_data_probs",
        type=str,
        default="1.0",
        help="sampling probs for datasets",
    )
    parser.add_argument("--pretrain_data", type=str, default=None)
    parser.add_argument(
        "--pretrain_data_probs",
        type=str,
        default="1.0",
        help="sampling probs for datasets",
    )
    parser.add_argument("--pretrain", type=str, default=None)
    parser.add_argument("--reward_pretrain", type=str, default=None)
    parser.add_argument("--save_path", type=str, default="./ckpt")
    parser.add_argument("--save_steps", type=int, default=-1)
    parser.add_argument("--logging_steps", type=int, default=1)
    parser.add_argument("--eval_steps", type=int, default=-1)
    parser.add_argument("--ckpt_path", type=str, default="./ckpt/checkpoints_ppo")
    parser.add_argument("--max_ckpt_num", type=int, default=3)
    parser.add_argument("--max_ckpt_mem", type=int, default=1000)  # 1000GB
    parser.add_argument("--num_episodes", type=int, default=1)
    parser.add_argument("--rollout_batch_size", type=int, default=512)
    parser.add_argument("--micro_rollout_batch_size", type=int, default=8)
    parser.add_argument("--max_epochs", type=int, default=1)
    parser.add_argument("--prompt_max_len", type=int, default=1024)
    parser.add_argument("--generate_max_len", type=int, default=1024)
    parser.add_argument("--max_len", type=int, default=None)
    parser.add_argument("--max_samples", type=int, default=100000)
    parser.add_argument("--max_norm", type=float, default=1.0)
    parser.add_argument("--l2", type=float, default=0.0)
    parser.add_argument("--ptx_coef", type=float, default=0.05)
    parser.add_argument("--eps_clip", type=float, default=0.2)
    parser.add_argument("--value_clip", type=float, default=0.2)
    parser.add_argument("--lambd", type=float, default=0.95)
    parser.add_argument("--gamma", type=float, default=1)
    parser.add_argument("--micro_train_batch_size", type=int, default=4)
    parser.add_argument("--train_batch_size", type=int, default=128)
    parser.add_argument("--load_checkpoint", action="store_true", default=False)
    parser.add_argument("--normalize_reward", action="store_true", default=False)
    parser.add_argument("--top_p", type=float, default=1.0)
    parser.add_argument("--temperature", type=float, default=1.0)
    parser.add_argument("--seed", type=int, default=42)
    parser.add_argument("--slurm_job", type=str, default=None)

    parser.add_argument("--local_rank", type=int, default=-1, help="local_rank for deepspeed")
    parser.add_argument("--zero_stage", type=int, default=2)
    parser.add_argument("--gradient_checkpointing", action="store_true", default=False)
    parser.add_argument("--bf16", action="store_true", default=False)
    parser.add_argument("--actor_learning_rate", type=float, default=1e-6)
    parser.add_argument("--critic_learning_rate", type=float, default=9e-6)
    parser.add_argument("--kl_target", type=float, default=None)
    parser.add_argument("--init_kl_coef", type=float, default=0.02)
    ## Make EMA as an optional feature
    parser.add_argument("--enable_ema", action="store_true", help="Enable EMA checkpoint for the model.")
    parser.add_argument("--zpg", type=int, default=1, help="ZeRO++ max partition size")
    parser.add_argument("--adam_offload", action="store_true", default=False)
    parser.add_argument("--actor_init_on_gpu", action="store_true", default=False)
    parser.add_argument("--flash_attn", action="store_true", default=False)
    parser.add_argument("--aux_loss_coef", type=float, default=0)
    parser.add_argument("--grad_accum_dtype", type=str, default=None)
    parser.add_argument("--disable_trace_cache", action="store_true", default=False)
    parser.add_argument("--load_in_4bit", action="store_true", default=False)
    parser.add_argument("--lora_rank", type=int, default=0)
    parser.add_argument("--lora_alpha", type=int, default=16)
    parser.add_argument("--target_modules", type=list, default=None)
    parser.add_argument("--input_template", type=str, default="Human: {}\nAssistant: ")
    parser.add_argument("--gradient_checkpointing_use_reentrant", action="store_true")
    parser.add_argument("--disable_fast_tokenizer", action="store_true", default=False)

    parser.add_argument("--bos_token", type=str, default=None)
    parser.add_argument("--eos_token", type=str, default=None)
    parser.add_argument("--pad_token", type=str, default=None)
    parser.add_argument("--unk_token", type=str, default=None)

    # custom dataset key name
    parser.add_argument("--input_key", type=str, default=None)

    # wandb pamameters
    parser.add_argument("--use_wandb", type=str, default=None)
    parser.add_argument("--wandb_org", type=str, default="gree")
    parser.add_argument("--wandb_group", type=str, default='test')
    parser.add_argument("--wandb_project", type=str, default="openrlhf_train_ppo")
    parser.add_argument(
        "--wandb_run_name",
        type=str,
        default="ppo_%s" % datetime.now().strftime("%m%dT%H:%M"),
    )

    args = parser.parse_args()
    train(args)
