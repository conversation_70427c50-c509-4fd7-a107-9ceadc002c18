import os
import imageio
import jax
import jax.numpy as jnp
import jmp
import orbax.checkpoint
import xminigrid
import flax
import torch
from jax_nn import ActorCriticRNN
from xminigrid.rendering.text_render import print_ruleset
from xminigrid.wrappers import GymAutoResetWrapper
import numpy as np
import math
import jax.tree_util as jtu
from tqdm import tqdm
from transformers import AutoTokenizer, BertForMaskedLM, BertForPreTraining, FlaxBertForPreTraining
from flax import struct
from flax.training.train_state import TrainState
from xminigrid.environment import Environment, EnvParams

from langchain.output_parsers import ResponseSchema, StructuredOutputParser

class RolloutStats(struct.PyTreeNode):
    reward: jax.Array = jnp.asarray(0.0)
    length: jax.Array = jnp.asarray(0)
    dones: jax.Array = jnp.asarray(0.0)
    episodes: jax.Array = jnp.asarray(0)

goal_map = {
    5: 'ball',
    6: 'square',
    7: 'pyramid',
    8: 'goal',
    9: 'key',
    10: 'locked door',
    11: 'closed door',
    12: 'open door',
    13: 'hex',
    14: 'star'
}
color_map = {
    3: 'red',
    4: 'green',
    5: 'blue',
    6: 'purple',
    7: 'yellow',
    8: 'grey',
    9: 'black',
    10: 'orange',
    11: 'white',
    12: 'brown',
    13: 'pink'
}
inv_color_map = {v: k for k, v in color_map.items()}
inv_goal_map = {v: k for k, v in goal_map.items()}

class EmbodiedReward:
    def __init__(self):
        env, env_params = xminigrid.make("XLand-MiniGrid-R4-9x9")
        env = GymAutoResetWrapper(env)
        self.amp_policy = jmp.get_policy('params=float32,compute=float32,output=float32') 
        self.policy = ActorCriticRNN(
            num_actions=env.num_actions(env_params),
            action_emb_dim=128,
            rnn_hidden_dim=512,
            rnn_num_layers=1,
            head_hidden_dim=512,
            cnn='minigrid',
            amp_policy=self.amp_policy,
            dtype=jnp.float32
        )
        self.result_schema = [
            ResponseSchema(name="object", description="the target object type"),
            ResponseSchema(
                name="color",
                description="target object color",
            ),
        ]
        self.output_parser = StructuredOutputParser.from_response_schemas(self.result_schema)
        self.env = env
        orbax_checkpointer = orbax.checkpoint.PyTreeCheckpointer()
        checkpoint = orbax_checkpointer.restore(
            os.path.join(os.environ.get("POLICIES", os.environ["SCRATCH"] + '/language_policies'), "symbolic_policy")
        )
        self.params = checkpoint['params']
        self.rng_counter = 0
        # self.tokenizer = AutoTokenizer.from_pretrained("google-bert/bert-base-uncased")
        # self.instruction_backbone = FlaxBertForPreTraining.from_pretrained("google-bert/bert-base-uncased")
        apply_fn, reset_fn, step_fn = jax.jit(self.policy.apply), jax.jit(env.reset), jax.jit(env.step)
        self.env = env
        self.env_params = env_params
        self.apply_policy = apply_fn
        self.reset_env = reset_fn
        self.step_env = step_fn
        def rollout(
            rng: jax.Array,
            env: Environment,
            env_params: EnvParams,
            goals: jax.Array,
            params: flax.core.FrozenDict,
            init_hstate: jax.Array,
            num_consecutive_episodes: int = 1,
            amp_policy: jmp.Policy = None,
        ) -> RolloutStats:
            def _cond_fn(carry):
                rng, stats, timestep, prev_action, prev_reward, hstate = carry
                return jnp.less(stats.episodes, num_consecutive_episodes)

            def _body_fn(carry):
                rng, stats, timestep, prev_action, prev_reward, hstate = carry

                rng, _rng = jax.random.split(rng)
                my_goal_vec = timestep.state.goal_encoding
                my_goal_vec2 = goals
                # o, c = timestep.state.goal_encoding[..., 1], timestep.state.goal_encoding[..., 2]
                # idx = jax.random.randint(_rng, (), 0, 19)
                # goal_vec = goals#[o, c, idx]
                params_ = amp_policy.cast_to_param(params)
                inputs, hidden = amp_policy.cast_to_compute((
                    {
                        "observation": timestep.observation[None, None, ...],
                        "prev_action": prev_action[None, None, ...],
                        "prev_reward": prev_reward[None, None, ...],
                        "goal": my_goal_vec2[None, None, ...]
                    },
                    hstate,
                ))
                dist, _, hstate = self.apply_policy(
                    params_, inputs, hidden
                )
                action = dist.sample(seed=_rng).squeeze()
                timestep = env.step(env_params, timestep, action)

                stats = stats.replace(
                    reward=stats.reward + timestep.reward,
                    length=stats.length + 1,
                    dones=stats.dones + (1 - timestep.discount),
                    episodes=stats.episodes + timestep.last(),
                )
                carry = (rng, stats, timestep, action, timestep.reward, hstate)
                return carry

            timestep = env.reset(env_params, rng)
            prev_action = jnp.asarray(0)
            prev_reward = jnp.asarray(0)
            init_hstate = amp_policy.cast_to_compute(init_hstate)
            init_carry = (rng, RolloutStats(), timestep, prev_action, prev_reward, init_hstate)

            final_carry = jax.lax.while_loop(_cond_fn, _body_fn, init_val=init_carry)
            return final_carry[1]

        self.compute_eval_stats = jax.vmap(rollout, in_axes=(0, None, 0, 0, None, None, None, None))


    def __call__(self, sequences, attention_mask, *args, **kwargs):
        replies = [s.split('Answer:')[-1] for s in sequences]
        structured_inputs = []
        rewards = []
        for reply in replies:
            try:
                structured_input = self.output_parser.invoke(reply)
                color = structured_input['color']
                object = structured_input['object']
                color_id = inv_color_map[color]
                obj_id = inv_goal_map[object]
                structured_inputs.append([color_id, obj_id])
            except:
                structured_inputs.append(None)
        result = torch.tensor([0.0 for _ in range(len(structured_inputs))]).to(attention_mask.device)
        valid = [s for s in structured_inputs if s is not None]
        valid_ids = [j for j, s in enumerate(structured_inputs) if s is not None]
        valid_ids = np.array(valid_ids)
        if len(valid) == 0:
            return result
        scores = np.ones(len(valid)).astype(np.float32) * 0.5
        result[valid_ids] = torch.from_numpy(scores).to(result.device) + 0.5
        return result
        self.rng_counter += 1
        rngs = jax.random.split(jax.random.key(self.rng_counter), len(valid))
        # outputs = self.instruction_backbone(**inputs, output_hidden_states=True)
        # goal_vec = outputs.hidden_states[-1][:,0] # [batch , 768]
        goal_vec = np.array(valid)
        # prev_reward = jnp.asarray(0)
        # prev_action = jnp.asarray(0)
        hidden = self.policy.initialize_carry(len(valid))
        suff = jnp.zeros_like(goal_vec)
        pref = jnp.ones_like(goal_vec)[:, :1]
        my_goal_vec = jnp.concatenate([pref, goal_vec, suff], axis=-1)
        rules = jnp.tile(self.env_params.ruleset.rules, (len(valid), 1, 1))
        init_tiles = jnp.tile(self.env_params.ruleset.init_tiles, (len(valid), 1))
        ruleset = self.env_params.ruleset.replace(
            goal=my_goal_vec, rules=rules, init_tiles=init_tiles)
        rngs_ = jnp.tile(rngs, (len(valid), 1))
        env_params = self.env_params.replace(
            ruleset=ruleset
        )
        my_goal_vec_for_net = jnp.eye(16)[my_goal_vec].reshape(my_goal_vec.shape[0], -1)
        stats = self.compute_eval_stats(
            rngs, self.env, env_params, my_goal_vec_for_net, self.params, hidden, 10, self.amp_policy)
        scores = np.array((stats.dones / stats.episodes))
        result[valid_ids] = torch.from_numpy(scores).to(result.device) + 0.5
        return result
