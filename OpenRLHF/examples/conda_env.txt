}
}
ARCHIVE=/network/archive/a/artem.zholus
BASH_COMPLETION_SINGULARITY=/cvmfs/ai.mila.quebec/apps/arch/distro/singularity/3.7.1/bash_completion.d/singularity
BASH_ENV=/cvmfs/ai.mila.quebec/apps/x86_64/debian/lmod/lmod/init/bash
BASH_FUNC_ml%%=() {  eval $($LMOD_DIR/ml_cmd "$@")
BASH_FUNC_module%%=() {  eval $($LMOD_CMD bash "$@") && eval $(${LMOD_SETTARG_CMD:-:} -s sh)
_CE_CONDA=
_CE_M=
CONDA_DEFAULT_ENV=/network/scratch/a/artem.zholus/conda_envs/rl_jax_torch
CONDA_EXE=/home/<USER>/a/artem.zholus/.conda/bin/conda
CONDA_PREFIX_1=/home/<USER>/a/artem.zholus/.conda
CONDA_PREFIX=/network/scratch/a/artem.zholus/conda_envs/rl_jax_torch
CONDA_PROMPT_MODIFIER=(rl_jax_torch)
CONDA_PYTHON_EXE=/home/<USER>/a/artem.zholus/.conda/bin/python
CONDA_SHLVL=2
CPATH=/cvmfs/ai.mila.quebec/apps/arch/common/cuda/12.1.1/include:/cvmfs/ai.mila.quebec/apps/arch/distro/openmpi/4.0.4/include
CUDA_DIR=/cvmfs/ai.mila.quebec/apps/arch/common/cuda/12.1.1
CUDA_HOME=/cvmfs/ai.mila.quebec/apps/arch/common/cuda/12.1.1
CUDA_SDK=/cvmfs/ai.mila.quebec/apps/arch/common/cuda/12.1.1/samples
CUDA_VISIBLE_DEVICES=0,1
DBUS_SESSION_BUS_ADDRESS=unix:path=/run/user/1500000655/bus
DISPLAY=localhost:11.0
FPATH=/cvmfs/ai.mila.quebec/apps/x86_64/debian/lmod/lmod/init/ksh_funcs
GPU_DEVICE_ORDINAL=0,1
HOME=/home/<USER>/a/artem.zholus
HYDRA_BOOTSTRAP=slurm
HYDRA_LAUNCHER_EXTRA_ARGS=--external-launcher
I_MPI_HYDRA_BOOTSTRAP_EXEC_EXTRA_ARGS=--external-launcher
I_MPI_HYDRA_BOOTSTRAP=slurm
__Init_Default_Modules=1
LANG=en_US.UTF-8
LD_LIBRARY_PATH=/cvmfs/ai.mila.quebec/apps/arch/common/cuda/12.1.1/lib64:/cvmfs/ai.mila.quebec/apps/arch/distro/openmpi/4.0.4/lib
LESSCLOSE=/usr/bin/lesspipe %s %s
LESSOPEN=| /usr/bin/lesspipe %s
LIBRARY_PATH=/cvmfs/ai.mila.quebec/apps/arch/common/cuda/12.1.1/lib64:/cvmfs/ai.mila.quebec/apps/arch/distro/openmpi/4.0.4/lib
_LMFILES_=/cvmfs/config.mila.quebec/modules/Core/Mila.lua:/cvmfs/config.mila.quebec/modules/Core/singularity/3.7.1.lua:/cvmfs/config.mila.quebec/modules/Core/openmpi/4.0.4.lua:/cvmfs/config.mila.quebec/modules/Cuda/cudatoolkit/12.1.1.lua
LMOD_CMD=/cvmfs/ai.mila.quebec/apps/x86_64/debian/lmod/lmod/libexec/lmod
LMOD_DIR=/cvmfs/ai.mila.quebec/apps/x86_64/debian/lmod/lmod/libexec
LMOD_PACKAGE_PATH=/cvmfs/config.mila.quebec/etc/lmod/
LMOD_PKG=/cvmfs/ai.mila.quebec/apps/x86_64/debian/lmod/lmod
__LMOD_Priority_PATH=/network/scratch/a/artem.zholus/conda_envs/rl_jax_torch/bin:-100
__LMOD_REF_COUNT_CPATH=/cvmfs/ai.mila.quebec/apps/arch/common/cuda/12.1.1/include:1;/cvmfs/ai.mila.quebec/apps/arch/distro/openmpi/4.0.4/include:1
__LMOD_REF_COUNT_LD_LIBRARY_PATH=/cvmfs/ai.mila.quebec/apps/arch/common/cuda/12.1.1/lib64:1;/cvmfs/ai.mila.quebec/apps/arch/distro/openmpi/4.0.4/lib:1
__LMOD_REF_COUNT_LIBRARY_PATH=/cvmfs/ai.mila.quebec/apps/arch/common/cuda/12.1.1/lib64:1;/cvmfs/ai.mila.quebec/apps/arch/distro/openmpi/4.0.4/lib:1
__LMOD_REF_COUNT__LMFILES_=/cvmfs/config.mila.quebec/modules/Core/Mila.lua:1;/cvmfs/config.mila.quebec/modules/Core/singularity/3.7.1.lua:1;/cvmfs/config.mila.quebec/modules/Core/openmpi/4.0.4.lua:1;/cvmfs/config.mila.quebec/modules/Cuda/cudatoolkit/12.1.1.lua:1
__LMOD_REF_COUNT_LOADEDMODULES=Mila:1;singularity/3.7.1:1;openmpi/4.0.4:1;cudatoolkit/12.1.1:1
__LMOD_REF_COUNT_MANPATH=/cvmfs/ai.mila.quebec/apps/arch/common/cuda/12.1.1/doc/man:1;/cvmfs/ai.mila.quebec/apps/x86_64/debian/lmod/lmod/share/man:1
__LMOD_REF_COUNT_MODULEPATH=/cvmfs/config.mila.quebec/modules/Core:1;/cvmfs/config.mila.quebec/modules/Compiler:1;/cvmfs/config.mila.quebec/modules/Environments:1;/cvmfs/config.mila.quebec/modules/Cuda:1;/cvmfs/config.mila.quebec/modules/Pytorch:1;/cvmfs/config.mila.quebec/modules/Tensorflow:1
__LMOD_REF_COUNT_PATH=/network/scratch/a/artem.zholus/conda_envs/rl_jax_torch/bin:1;/cvmfs/ai.mila.quebec/apps/arch/common/cuda/12.1.1/bin:1;/cvmfs/ai.mila.quebec/apps/arch/distro/openmpi/4.0.4/bin:1;/cvmfs/ai.mila.quebec/apps/arch/distro/singularity/3.7.1/bin:1;/home/<USER>/a/artem.zholus/.local/bin:2;/home/<USER>/a/artem.zholus/.conda/condabin:1;/cvmfs/config.mila.quebec/bin/local:1;/cvmfs/config.mila.quebec/bin:1;/opt/slurm/bin:6;/usr/local/sbin:1;/usr/local/bin:1;/usr/sbin:1;/usr/bin:1;/sbin:1;/bin:1;/usr/games:1;/usr/local/games:1
LMOD_ROOT=/cvmfs/ai.mila.quebec/apps/x86_64/debian/lmod
__LMOD_SET_FPATH=1
LMOD_SETTARG_FULL_SUPPORT=no
LMOD_sys=Linux
LMOD_SYSTEM_DEFAULT_MODULES=Mila
LMOD_VERSION=8.3.17
LOADEDMODULES=Mila:singularity/3.7.1:openmpi/4.0.4:cudatoolkit/12.1.1
LOGNAME=artem.zholus
LS_COLORS=rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:
MANPATH=/cvmfs/ai.mila.quebec/apps/arch/common/cuda/12.1.1/doc/man:/cvmfs/ai.mila.quebec/apps/x86_64/debian/lmod/lmod/share/man::
MIG_PARTED_CHECKPOINT_FILE=/var/lib/nvidia-mig-manager/checkpoint.json
MIG_PARTED_CONFIG_FILE=/etc/nvidia-mig-manager/config.yaml
MIG_PARTED_HOOKS_FILE=/etc/nvidia-mig-manager/hooks.yaml
MODULEPATH=/cvmfs/config.mila.quebec/modules/Core:/cvmfs/config.mila.quebec/modules/Compiler:/cvmfs/config.mila.quebec/modules/Environments:/cvmfs/config.mila.quebec/modules/Cuda:/cvmfs/config.mila.quebec/modules/Pytorch:/cvmfs/config.mila.quebec/modules/Tensorflow
MODULEPATH_ROOT=/cvmfs/config.mila.quebec/modules
MODULERCFILE=/cvmfs/config.mila.quebec/etc/lmod/modulerc.lua
MODULESHOME=/cvmfs/ai.mila.quebec/apps/x86_64/debian/lmod/lmod
_ModuleTable001_=X01vZHVsZVRhYmxlXz17WyJNVHZlcnNpb24iXT0zLFsiY19yZWJ1aWxkVGltZSJdPWZhbHNlLFsiY19zaG9ydFRpbWUiXT1mYWxzZSxkZXB0aFQ9e30sZmFtaWx5PXt9LG1UPXtNaWxhPXtbImZuIl09Ii9jdm1mcy9jb25maWcubWlsYS5xdWViZWMvbW9kdWxlcy9Db3JlL01pbGEubHVhIixbImZ1bGxOYW1lIl09Ik1pbGEiLFsibG9hZE9yZGVyIl09MSxwcm9wVD17bG1vZD17WyJzdGlja3kiXT0xLH0sfSxbInN0YWNrRGVwdGgiXT0wLFsic3RhdHVzIl09ImFjdGl2ZSIsWyJ1c2VyTmFtZSJdPSJNaWxhIix9LGN1ZGF0b29sa2l0PXtbImZuIl09Ii9jdm1mcy9jb25maWcubWlsYS5xdWViZWMvbW9kdWxlcy9DdWRhL2N1ZGF0b29sa2l0LzEyLjEuMS5sdWEiLFsiZnVsbE5hbWUi
_ModuleTable002_=XT0iY3VkYXRvb2xraXQvMTIuMS4xIixbImxvYWRPcmRlciJdPTQscHJvcFQ9e30sWyJzdGFja0RlcHRoIl09MCxbInN0YXR1cyJdPSJhY3RpdmUiLFsidXNlck5hbWUiXT0iY3VkYXRvb2xraXQvMTIuMS4xIix9LG9wZW5tcGk9e1siZm4iXT0iL2N2bWZzL2NvbmZpZy5taWxhLnF1ZWJlYy9tb2R1bGVzL0NvcmUvb3Blbm1waS80LjAuNC5sdWEiLFsiZnVsbE5hbWUiXT0ib3Blbm1waS80LjAuNCIsWyJsb2FkT3JkZXIiXT0zLHByb3BUPXt9LFsic3RhY2tEZXB0aCJdPTAsWyJzdGF0dXMiXT0iYWN0aXZlIixbInVzZXJOYW1lIl09Im9wZW5tcGkvNC4wLjQiLH0sc2luZ3VsYXJpdHk9e1siZm4iXT0iL2N2bWZzL2NvbmZpZy5taWxhLnF1ZWJlYy9tb2R1bGVzL0NvcmUvc2luZ3Vs
_ModuleTable003_=YXJpdHkvMy43LjEubHVhIixbImZ1bGxOYW1lIl09InNpbmd1bGFyaXR5LzMuNy4xIixbImxvYWRPcmRlciJdPTIscHJvcFQ9e30sWyJzdGFja0RlcHRoIl09MCxbInN0YXR1cyJdPSJhY3RpdmUiLFsidXNlck5hbWUiXT0ic2luZ3VsYXJpdHkiLH0sfSxtcGF0aEE9eyIvY3ZtZnMvY29uZmlnLm1pbGEucXVlYmVjL21vZHVsZXMvQ29yZSIsIi9jdm1mcy9jb25maWcubWlsYS5xdWViZWMvbW9kdWxlcy9Db21waWxlciIsIi9jdm1mcy9jb25maWcubWlsYS5xdWViZWMvbW9kdWxlcy9FbnZpcm9ubWVudHMiLCIvY3ZtZnMvY29uZmlnLm1pbGEucXVlYmVjL21vZHVsZXMvQ3VkYSIsIi9jdm1mcy9jb25maWcubWlsYS5xdWViZWMvbW9kdWxlcy9QeXRvcmNoIiwiL2N2bWZzL2NvbmZp
_ModuleTable004_=Zy5taWxhLnF1ZWJlYy9tb2R1bGVzL1RlbnNvcmZsb3ciLH0sWyJzeXN0ZW1CYXNlTVBBVEgiXT0iL2N2bWZzL2NvbmZpZy5taWxhLnF1ZWJlYy9tb2R1bGVzL0NvcmU6L2N2bWZzL2NvbmZpZy5taWxhLnF1ZWJlYy9tb2R1bGVzL0NvbXBpbGVyOi9jdm1mcy9jb25maWcubWlsYS5xdWViZWMvbW9kdWxlcy9FbnZpcm9ubWVudHM6L2N2bWZzL2NvbmZpZy5taWxhLnF1ZWJlYy9tb2R1bGVzL0N1ZGE6L2N2bWZzL2NvbmZpZy5taWxhLnF1ZWJlYy9tb2R1bGVzL1B5dG9yY2g6L2N2bWZzL2NvbmZpZy5taWxhLnF1ZWJlYy9tb2R1bGVzL1RlbnNvcmZsb3ciLH0=
_ModuleTable_Sz_=4
MOTD_SHOWN=pam
NCCL_DEBUG=INFO
OLDPWD=/home/<USER>/a/artem.zholus
OMPI_MCA_plm_slurm_args=--external-launcher
PATH=/network/scratch/a/artem.zholus/conda_envs/rl_jax_torch/bin:/cvmfs/ai.mila.quebec/apps/arch/common/cuda/12.1.1/bin:/cvmfs/ai.mila.quebec/apps/arch/distro/openmpi/4.0.4/bin:/cvmfs/ai.mila.quebec/apps/arch/distro/singularity/3.7.1/bin:/home/<USER>/a/artem.zholus/.local/bin:/home/<USER>/a/artem.zholus/.conda/condabin:/cvmfs/config.mila.quebec/bin/local:/cvmfs/config.mila.quebec/bin:/opt/slurm/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games
PRTE_MCA_plm_slurm_args=--external-launcher
PWD=/home/<USER>/a/artem.zholus/workspace/language_grounding
ROCR_VISIBLE_DEVICES=0,1
SACCT_FORMAT=User,JobID,Jobname,partition,state,time,start,end,elapsed,nnodes,ncpus,reqmem,alloctres,nodelist,workdir
SCRATCH=/network/scratch/a/artem.zholus
SHELL=/bin/bash
SHLVL=2
SINFO_FORMAT=%18N %.6D %.11T %.4c %.8z %.6m %.8d %.6w %.22f %80E
SINGULARITY_CACHEDIR=/network/scratch/a/artem.zholus/.singularity
SLURM_CONF=/etc/slurm/slurm.conf
SLURM_CPU_BIND_LIST=0x00000000000000FF
SLURM_CPU_BIND=quiet,mask_cpu:0x00000000000000FF
SLURM_CPU_BIND_TYPE=mask_cpu:
SLURM_CPU_BIND_VERBOSE=quiet
SLURM_CPUS_ON_NODE=8
SLURMD_DEBUG=2
SLURM_DISTRIBUTION=cyclic
SLURMD_NODENAME=cn-g026
SLURM_GPUS_ON_NODE=2
SLURM_GTIDS=0
SLURM_JOB_CPUS_PER_NODE=8
SLURM_JOB_END_TIME=1714710405
SLURM_JOB_GID=1500000655
SLURM_JOB_ID=4674777
SLURM_JOBID=4674777
SLURM_JOB_NAME=bash
SLURM_JOB_NODELIST=cn-g026
SLURM_JOB_PARTITION=main
SLURM_JOB_START_TIME=1714451205
SLURM_JOB_UID=1500000655
SLURM_JOB_USER=artem.zholus
SLURM_LAUNCH_NODE_IPADDR=************
SLURM_LOCALID=0
SLURM_MEM_PER_NODE=49152
SLURM_NNODES=1
SLURM_NODEID=0
SLURM_NODELIST=cn-g026
SLURM_NPROCS=1
SLURM_NTASKS=1
SLURM_PRIO_PROCESS=0
SLURM_PROCID=0
SLURM_PTY_PORT=39735
SLURM_PTY_WIN_COL=166
SLURM_PTY_WIN_ROW=52
SLURM_SCRIPT_CONTEXT=prolog_task
SLURM_SRUN_COMM_HOST=************
SLURM_SRUN_COMM_PORT=38051
SLURM_STEP_GPUS=0,1
SLURM_STEP_ID=0
SLURM_STEPID=0
SLURM_STEP_LAUNCHER_PORT=38051
SLURM_STEP_NODELIST=cn-g026
SLURM_STEP_NUM_NODES=1
SLURM_STEP_NUM_TASKS=1
SLURM_STEP_TASKS_PER_NODE=1
SLURM_TASK_PID=1001566
SLURM_TASKS_PER_NODE=1
SLURM_TMPDIR=/tmp
SLURM_TOPOLOGY_ADDR=cn-g026
SLURM_TOPOLOGY_ADDR_PATTERN=node
SLURM_UMASK=0002
SQUEUE_FORMAT=%.10A %.10i %.8u %.12P %.25j %.3t %16S %.10M %.5D %.4C %.15b %.7m %N (%r) %k
SRUN_DEBUG=3
SSH_AUTH_SOCK=/tmp/ssh-XXXXdhLj3s/agent.1026419
SSH_CLIENT=************* 44442 22
SSH_CONNECTION=************* 56408 ************ 22
SSH_TTY=/dev/pts/67
TERM_PROGRAM=tmux
TERM_PROGRAM_VERSION=3.2a
TERM=screen
TMPDIR=/tmp
TMUX_PANE=%1
TMUX=/tmp/tmux-1500000655/default,985205,0
TORCH_CPP_LOG_LEVEL=INFO
TORCH_DISTRIBUTED_DEBUG=INFO
USER=artem.zholus
_=/usr/bin/env
XDG_RUNTIME_DIR=/run/user/1500000655
XDG_SESSION_CLASS=user
XDG_SESSION_ID=2870
XDG_SESSION_TYPE=tty
XLA_FLAGS=--xla_gpu_cuda_data_dir=/cvmfs/ai.mila.quebec/apps/arch/common/cuda/12.1.1
ZE_AFFINITY_MASK=0,1
