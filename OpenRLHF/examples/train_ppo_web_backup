import argparse
import itertools
import math
import os
from copy import deepcopy
from datetime import datetime

import sys
sys.path.insert(0, '..')

import torch
from transformers.trainer import get_scheduler

from openrlhf.datasets import PromptDataset, SFTDataset
from openrlhf.models import Actor, get_llm_for_sequence_regression
from openrlhf.trainer import PPOTrainer
from openrlhf.utils import blending_datasets, get_strategy, get_tokenizer

# from embodied_reward import EmbodiedReward

from copy import deepcopy
from dataclasses import asdict, dataclass
from functools import partial
from warnings import warn
from browsergym.core.env import BrowserEnv

import bgym
from browsergym.experiments.agent import Agent, AgentInfo
from browsergym.miniwob.all import UseSpinnerTask
from agentlab.agents import dynamic_prompting as dp
from agentlab.agents.agent_args import AgentArgs
from agentlab.llm.chat_api import BaseModelArgs, make_system_message, make_user_message
from agentlab.llm.llm_utils import Discussion, ParseError, SystemMessage, AIMessage, retry
from agentlab.llm.tracking import cost_tracker_decorator
from agentlab.agents.generic_agent.agent_configs import FLAGS_8B
from agentlab.agents.generic_agent.generic_agent_prompt import MainPrompt
from goal_agent_prompt import GoalBasedPromptFlags, MainGoalBasedPrompt
FLAGS_8B.obs.use_html = True
FLAGS_8B.obs.use_ax_tree = False
# MAX_JOBS=10 pip install --no-build-isolation flash-attn==2.5.0

class WebAgentPrompt:
    def __init__(
        self,
        num_envs: int,
        flags: GoalBasedPromptFlags,
        tokenizer, 
        max_retry: int = 4,
    ):
        self.max_retry = max_retry
        self.num_ens = num_envs
        self.tokenizer = tokenizer

        self.flags = flags
        self.action_set = self.flags.action.action_set.make_action_set()
        self._obs_preprocessor = dp.make_obs_preprocessor(flags.obs)

        self._check_flag_constancy()
        self.reset(seed=None)

    def obs_preprocessor(self, obs: dict) -> dict:
        return self._obs_preprocessor(obs)

    # @cost_tracker_decorator
    def next_prompt(self, obs):

        self.obs_history.append(obs)
        main_prompt = MainPrompt(
            action_set=self.action_set,
            obs_history=self.obs_history,
            actions=self.actions,
            memories=self.memories,
            thoughts=self.thoughts,
            previous_plan=self.plan,
            step=self.plan_step,
            flags=self.flags,
        )

        max_prompt_tokens, max_trunc_itr = 10000, 20

        system_prompt = SystemMessage(dp.SystemPrompt().prompt)

        human_prompt = dp.fit_tokens(
            shrinkable=main_prompt,
            max_prompt_tokens=max_prompt_tokens,
            model_name="gemma-2-2b-it",
            max_iterations=max_trunc_itr,
            additional_prompts=system_prompt,
        )
        # try:
        # TODO, we would need to further shrink the prompt if the retry
        # cause it to be too long

        chat_messages = Discussion([system_prompt, human_prompt])
        chat_messages.merge()
        # TODO: put everything into the user role.
        prompt = self.tokenizer.apply_chat_template(chat_messages, tokenize=False)
        return prompt
        # ans_dict = retry(
        #     self.chat_llm,
        #     chat_messages,
        #     n_retry=self.max_retry,
        #     parser=main_prompt._parse_answer,
        # )
        # ans_dict["busted_retry"] = 0
        # inferring the number of retries, TODO: make this less hacky
        # ans_dict["n_retry"] = (len(chat_messages) - 3) / 2
        # except ParseError as e:
        #     ans_dict = dict(
        #         action=None,
        #         n_retry=self.max_retry + 1,
        #         busted_retry=1,
        #     )

        # stats = self.chat_llm.get_stats()
        # stats["n_retry"] = ans_dict["n_retry"]
        # stats["busted_retry"] = ans_dict["busted_retry"]

        # self.plan = ans_dict.get("plan", self.plan)
        # self.plan_step = ans_dict.get("step", self.plan_step)
        # self.actions.append(ans_dict["action"])
        # self.memories.append(ans_dict.get("memory", None))
        # self.thoughts.append(ans_dict.get("think", None))

        # agent_info = AgentInfo(
        #     think=ans_dict.get("think", None),
        #     chat_messages=chat_messages,
        #     stats=stats,
        #     extra_info={"chat_model_args": asdict(self.chat_model_args)},
        # )
        # return ans_dict["action"], agent_info

    def reset(self, seed=None):
        self.seed = seed
        self.plan = "No plan yet"
        self.plan_step = -1
        self.memories = []
        self.thoughts = []
        self.actions = []
        self.obs_history = []

    def _check_flag_constancy(self):
        flags = self.flags
        if flags.obs.use_som:
            if not flags.obs.use_screenshot:
                warn(
                    """
Warning: use_som=True requires use_screenshot=True. Disabling use_som."""
                )
                flags.obs.use_som = False
        if flags.obs.use_screenshot:
            if not self.chat_model_args.vision_support:
                warn(
                    """
Warning: use_screenshot is set to True, but the chat model \
does not support vision. Disabling use_screenshot."""
                )
                flags.obs.use_screenshot = False
        return flags

    def _get_maxes(self):
        maxes = (
            self.flags.max_prompt_tokens,
            self.chat_model_args.max_total_tokens,
            self.chat_model_args.max_input_tokens,
        )
        maxes = [m for m in maxes if m is not None]
        max_prompt_tokens = min(maxes) if maxes else None
        max_trunc_itr = (
            self.flags.max_trunc_itr
            if self.flags.max_trunc_itr
            else 20  # dangerous to change the default value here?
        )
        return max_prompt_tokens, max_trunc_itr

class WebAgentPromptDataset:
    def __init__(self, n_envs, flags, tokenizer) -> None:
        # TODO: one env so far
        self.prompter = [WebAgentPrompt(
            num_envs=1, flags=flags, tokenizer=tokenizer
        ) for _ in range(n_envs)]
        self.prompts = []
    
    def is_empty(self):
        return len(self.prompts) == 0

    def __len__(self):
        return 1000
    
    def __getitem__(self, item):
        return 'test'

def train(args):
    # configure strategy
    # import pdb; pdb.set_trace()
    strategy = get_strategy(args)
    strategy.setup_distributed()

    # configure model
    # load huggingface model
    actor = Actor(
        args.pretrain,
        use_flash_attention_2=args.flash_attn,
        bf16=args.bf16,
        load_in_4bit=args.load_in_4bit,
        lora_rank=args.lora_rank,
        lora_alpha=args.lora_alpha,
        target_modules=args.target_modules,
        ds_config=strategy.get_ds_train_config(is_actor=True),
    )

    if args.actor_init_on_gpu:
        actor = actor.to(torch.cuda.current_device())

    critic = get_llm_for_sequence_regression(
        args.reward_pretrain,
        "critic",
        normalize_reward=args.normalize_reward,
        use_flash_attention_2=args.flash_attn,
        bf16=args.bf16,
        load_in_4bit=args.load_in_4bit,
        lora_rank=args.lora_rank,
        lora_alpha=args.lora_alpha,
        target_modules=args.target_modules,
        ds_config=strategy.get_ds_train_config(is_actor=False),
        cache_dir=os.environ['SCRATCH'] + '/hf',
    )
    # reward_model = get_llm_for_sequence_regression(
    #     args.reward_pretrain,
    #     "reward",
    #     normalize_reward=args.normalize_reward,
    #     use_flash_attention_2=args.flash_attn,
    #     bf16=args.bf16,
    #     load_in_4bit=args.load_in_4bit,
    #     ds_config=strategy.get_ds_train_config(is_actor=False),
    # )
    # reward_model = EmbodiedReward()

    # configure tokenizer
    tokenizer = get_tokenizer(args.pretrain, actor.model, "left", strategy, use_fast=not args.disable_fast_tokenizer)
    critic_tokenizer = get_tokenizer(args.reward_pretrain, critic, "left", strategy, use_fast=not args.disable_fast_tokenizer)
    # get_tokenizer(args.reward_pretrain, reward_model, "left", strategy, use_fast=not args.disable_fast_tokenizer)

    strategy.print(actor)
    strategy.print(critic)

    # load weights for reference actor
    initial_model = Actor(
        args.pretrain,
        use_flash_attention_2=args.flash_attn,
        bf16=args.bf16,
        load_in_4bit=args.load_in_4bit,
        ds_config=strategy.get_ds_eval_config(offload=False),
    )
    get_tokenizer(args.pretrain, initial_model.model, "left", strategy)

    strategy.print("reward normalization status: {}".format(args.normalize_reward))
    # strategy.print("mean: {}, std {}".format(reward_model.mean, reward_model.std))

    if args.enable_ema:
        ema_model = Actor(
            args.pretrain,
            use_flash_attention_2=args.flash_attn,
            bf16=args.bf16,
            load_in_4bit=args.load_in_4bit,
            ds_config=strategy.get_ds_eval_config(offload=True),
        )
    else:
        ema_model = None

    # configure optimizer
    actor_optim = strategy.create_optimizer(
        actor, lr=args.actor_learning_rate, betas=(0.9, 0.95), weight_decay=args.l2
    )
    critic_optim = strategy.create_optimizer(
        critic, lr=args.critic_learning_rate, betas=(0.9, 0.95), weight_decay=args.l2
    )

    # prepare datasets
    # prompts_data = blending_datasets(
    #     args.prompt_data,
    #     args.prompt_data_probs,
    #     strategy,
    #     args.seed,
    #     max_count=args.max_samples,
    #     return_eval=False,
    # )
    # prompts_data = prompts_data.select(range(min(args.max_samples, len(prompts_data))))
    # prompts_dataset = PromptDataset(prompts_data, tokenizer, strategy, input_template=args.input_template)
    action_set = FLAGS_8B.action.action_set.make_action_set()
    envs = [BrowserEnv(
        task_entrypoint=UseSpinnerTask,
        task_kwargs=dict(
            # base_url=os.environ['MINIWOB_URL'],
            base_url=f'file:///home/<USER>/m/maryam.hashemzadeh/projects/artem/language_grounding/web_agents/miniwob-plusplus/miniwob/html/miniwob/',
            # base_url=f"file://{os.getcwd()}../../web_agents/miniwob-plusplus/miniwob/html/miniwob/"
            # base_url=f"file:///home/<USER>/a/artem.zholus/workspace/language_grounding/web_agents/miniwob-plusplus/miniwob/html/miniwob/"
            # base_url=f"file://{os.getcwd()}/custom_miniwob_html/"
            # base_url="file:///home/<USER>/a/artem.zholus/workspace/language_grounding/web_agents/custom_miniwob_html/"
        ),
        action_mapping=action_set.to_python_code,
        # exp_dir=exp_dir,
        # /home/<USER>/a/artem.zholus/workspace/language_grounding/web_agents/miniwob-plusplus/miniwob/html/miniwob/
    ) for _ in range(args.micro_rollout_batch_size)]
    prompts_dataset = WebAgentPromptDataset(n_envs=len(envs), flags=FLAGS_8B, tokenizer=tokenizer)
    prompts_dataloader = strategy.setup_dataloader(prompts_dataset, args.micro_rollout_batch_size, True, True)

    if args.pretrain_data:
        pretrain_data = blending_datasets(
            args.pretrain_data,
            args.pretrain_data_probs,
            strategy,
            args.seed,
            return_eval=False,
        )
        pretrain_max_len = args.max_len if args.max_len else args.prompt_max_len + args.generate_max_len
        pretrain_dataset = SFTDataset(
            pretrain_data.select(range(min(len(pretrain_data), args.max_epochs * len(prompts_dataset)))),
            tokenizer,
            pretrain_max_len,
            strategy,
            pretrain_mode=True,
        )
        pretrain_dataloader = itertools.cycle(
            iter(
                strategy.setup_dataloader(
                    pretrain_dataset,
                    args.micro_train_batch_size,
                    True,
                    True,
                    pretrain_dataset.collate_fn,
                )
            )
        )
    else:
        pretrain_dataloader = None
    N_PROMPTS = 100
    # configure scheduler
    num_update_steps_per_episodes = (
        int(N_PROMPTS * (args.micro_rollout_batch_size / args.micro_train_batch_size))
        * args.max_epochs
        // strategy.accumulated_gradient
    )

    max_steps = math.ceil(args.num_episodes * num_update_steps_per_episodes)

    actor_scheduler = get_scheduler(
        "cosine",
        actor_optim,
        num_warmup_steps=math.ceil(max_steps * 0.03),
        num_training_steps=max_steps,
    )

    critic_scheduler = get_scheduler(
        "cosine",
        critic_optim,
        num_warmup_steps=math.ceil(max_steps * 0.03),
        num_training_steps=max_steps,
    )

    # gradient_checkpointing
    if args.gradient_checkpointing:
        actor.gradient_checkpointing_enable(
            gradient_checkpointing_kwargs={"use_reentrant": args.gradient_checkpointing_use_reentrant}
        )
        critic.gradient_checkpointing_enable(
            gradient_checkpointing_kwargs={"use_reentrant": args.gradient_checkpointing_use_reentrant}
        )

    # prepare models/optimizers...
    (
        (actor, actor_optim, actor_scheduler),
        (critic, critic_optim, critic_scheduler),
        # reward_model,
        initial_model,
    ) = strategy.prepare(
        (actor, actor_optim, actor_scheduler),
        (critic, critic_optim, critic_scheduler),
        # reward_model,
        initial_model,
        is_rlhf=True,
    )

    if ema_model:
        ema_model._offload = True
        ema_model = strategy.prepare(ema_model, is_rlhf=True)

    # load checkpoint
    if args.load_checkpoint:
        strategy.print("Load checkpoint: ", args.save_path)

    os.makedirs(args.save_path, exist_ok=True)
    reward_model=lambda *args, **kwargs: 1.0
    # configure Trainer
    trainer = PPOTrainer(
        strategy,
        actor,
        critic,
        reward_model,
        initial_model,
        ema_model,
        actor_optim,
        critic_optim,
        actor_scheduler,
        critic_scheduler,
        max_epochs=args.max_epochs,
        micro_train_batch_size=args.micro_train_batch_size,
        micro_rollout_batch_size=args.micro_rollout_batch_size,
        gradient_checkpointing=args.gradient_checkpointing,
        tokenizer=tokenizer,
        critic_tokenizer=critic_tokenizer,
        prompt_max_len=args.prompt_max_len,
        value_clip=args.value_clip,
        eps_clip=args.eps_clip,
        gamma=args.gamma,
        lambd=args.lambd,
        init_kl_coef=args.init_kl_coef,
        kl_target=args.kl_target,
        ema_beta=0.992,
        ptx_coef=args.ptx_coef,
        max_norm=args.max_norm,
        # fro GPT generation
        do_sample=True,
        max_new_tokens=args.generate_max_len,
        max_length=args.max_len,
        temperature=args.temperature,
        top_p=args.top_p,
        pad_token_id=tokenizer.pad_token_id,
        eos_token_id=tokenizer.eos_token_id,
    )
    # prompts_dataloader = None
    trainer.fit(
        envs,
        prompts_dataset,
        prompts_dataloader,
        pretrain_dataloader,
        args,
    )

    # save model checkpoint after fitting on only rank0
    # strategy.save_model(
    #     ema_model if args.enable_ema else actor,
    #     tokenizer,
    #     args.save_path,
    # )


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--prompt_data", type=str, default=None)
    parser.add_argument(
        "--prompt_data_probs",
        type=str,
        default="1.0",
        help="sampling probs for datasets",
    )
    parser.add_argument("--pretrain_data", type=str, default=None)
    parser.add_argument(
        "--pretrain_data_probs",
        type=str,
        default="1.0",
        help="sampling probs for datasets",
    )
    parser.add_argument("--pretrain", type=str, default=None)
    parser.add_argument("--reward_pretrain", type=str, default=None)
    parser.add_argument("--save_path", type=str, default="./ckpt")
    parser.add_argument("--save_steps", type=int, default=-1)
    parser.add_argument("--logging_steps", type=int, default=1)
    parser.add_argument("--eval_steps", type=int, default=-1)
    parser.add_argument("--ckpt_path", type=str, default="./ckpt/checkpoints_ppo")
    parser.add_argument("--max_ckpt_num", type=int, default=3)
    parser.add_argument("--max_ckpt_mem", type=int, default=1000)  # 1000GB
    parser.add_argument("--num_episodes", type=int, default=1)
    parser.add_argument("--rollout_batch_size", type=int, default=512)
    parser.add_argument("--micro_rollout_batch_size", type=int, default=8)
    parser.add_argument("--max_epochs", type=int, default=1)
    parser.add_argument("--prompt_max_len", type=int, default=1024)
    parser.add_argument("--generate_max_len", type=int, default=1024)
    parser.add_argument("--max_len", type=int, default=None)
    parser.add_argument("--max_samples", type=int, default=100000)
    parser.add_argument("--max_norm", type=float, default=1.0)
    parser.add_argument("--l2", type=float, default=0.0)
    parser.add_argument("--ptx_coef", type=float, default=0.05)
    parser.add_argument("--eps_clip", type=float, default=0.2)
    parser.add_argument("--value_clip", type=float, default=0.2)
    parser.add_argument("--lambd", type=float, default=0.95)
    parser.add_argument("--gamma", type=float, default=1)
    parser.add_argument("--micro_train_batch_size", type=int, default=4)
    parser.add_argument("--train_batch_size", type=int, default=128)
    parser.add_argument("--load_checkpoint", action="store_true", default=False)
    parser.add_argument("--normalize_reward", action="store_true", default=False)
    parser.add_argument("--top_p", type=float, default=1.0)
    parser.add_argument("--temperature", type=float, default=1.0)
    parser.add_argument("--seed", type=int, default=42)
    parser.add_argument("--slurm_job", type=str, default=None)

    parser.add_argument("--local_rank", type=int, default=-1, help="local_rank for deepspeed")
    parser.add_argument("--zero_stage", type=int, default=2)
    parser.add_argument("--gradient_checkpointing", action="store_true", default=False)
    parser.add_argument("--bf16", action="store_true", default=False)
    parser.add_argument("--actor_learning_rate", type=float, default=1e-6)
    parser.add_argument("--critic_learning_rate", type=float, default=9e-6)
    parser.add_argument("--kl_target", type=float, default=None)
    parser.add_argument("--init_kl_coef", type=float, default=0.02)
    ## Make EMA as an optional feature
    parser.add_argument("--enable_ema", action="store_true", help="Enable EMA checkpoint for the model.")
    parser.add_argument("--zpg", type=int, default=1, help="ZeRO++ max partition size")
    parser.add_argument("--adam_offload", action="store_true", default=False)
    parser.add_argument("--actor_init_on_gpu", action="store_true", default=False)
    parser.add_argument("--flash_attn", action="store_true", default=False)
    parser.add_argument("--aux_loss_coef", type=float, default=0)
    parser.add_argument("--grad_accum_dtype", type=str, default=None)
    parser.add_argument("--disable_trace_cache", action="store_true", default=False)
    parser.add_argument("--load_in_4bit", action="store_true", default=False)
    parser.add_argument("--lora_rank", type=int, default=0)
    parser.add_argument("--lora_alpha", type=int, default=16)
    parser.add_argument("--target_modules", type=list, default=None)
    parser.add_argument("--input_template", type=str, default="Human: {}\nAssistant: ")
    parser.add_argument("--gradient_checkpointing_use_reentrant", action="store_true")
    parser.add_argument("--disable_fast_tokenizer", action="store_true", default=False)

    parser.add_argument("--bos_token", type=str, default=None)
    parser.add_argument("--eos_token", type=str, default=None)
    parser.add_argument("--pad_token", type=str, default=None)
    parser.add_argument("--unk_token", type=str, default=None)

    # custom dataset key name
    parser.add_argument("--input_key", type=str, default=None)

    # wandb pamameters
    parser.add_argument("--use_wandb", action='store_true', default=False)
    parser.add_argument("--wandb_org", type=str, default="gree")
    parser.add_argument("--wandb_group", type=str, default='test')
    parser.add_argument("--wandb_project", type=str, default="openrlhf_train_ppo")
    parser.add_argument(
        "--wandb_run_name",
        type=str,
        default="ppo_%s" % datetime.now().strftime("%m%dT%H:%M"),
    )

    args = parser.parse_args()
    train(args)
