# Model adapted from minigrid baselines:
# https://github.com/lcswillems/rl-starter-files/blob/master/model.py
import math
from typing import TypedDict

import distrax
import flax
import flax.linen as nn
import jax
import jmp
import jax.numpy as jnp
from flax.linen.initializers import glorot_normal, orthogonal, zeros_init

# from xminigrid.core.constants import NUM_COLORS, NUM_TILES


class GRU(nn.Module):
    hidden_dim: int
    amp_policy: jmp.Policy

    @nn.compact
    def __call__(self, xs, init_state):
        seq_len, input_dim = xs.shape
        # this init might not be optimal, for example bias for reset gate should be -1 (for now ok)
        Wi = self.param("Wi", glorot_normal(in_axis=1, out_axis=0), (self.hidden_dim * 3, input_dim))
        Wh = self.param("Wh", orthogonal(column_axis=0), (self.hidden_dim * 3, self.hidden_dim))
        bi = self.param("bi", zeros_init(), (self.hidden_dim * 3,))
        bn = self.param("bn", zeros_init(), (self.hidden_dim,))

        def _step_fn(h, x):
            (Wi_, Wh_, bi_, bn_), (h, x) = self.amp_policy.cast_to_compute(
                ((Wi, Wh, bi, bn), (h, x))
            ) 
            igates = jnp.split(Wi_ @ x + bi_, 3)
            hgates = jnp.split(Wh_ @ h, 3)

            reset = nn.sigmoid(igates[0] + hgates[0])
            update = nn.sigmoid(igates[1] + hgates[1])
            new = nn.tanh(igates[2] + reset * (hgates[2] + bn_))
            next_h = (1 - update) * new + update * h
            return next_h, next_h

        last_state, all_states = jax.lax.scan(_step_fn, init=init_state, xs=xs)
        return all_states, last_state


class RNNModel(nn.Module):
    hidden_dim: int
    num_layers: int
    amp_policy: jmp.Policy

    @nn.compact
    def __call__(self, xs, init_state):
        # xs: [seq_len, input_dim]
        # init_state: [num_layers, hidden_dim]
        outs, states = [], []
        for layer in range(self.num_layers):
            xs, state = GRU(hidden_dim=self.hidden_dim, amp_policy=self.amp_policy)(xs, init_state[layer])
            outs.append(xs)
            states.append(state)

        # sum outputs from all layers, kinda like in ResNet
        return jnp.array(outs).sum(0), jnp.array(states)


BatchedRNNModel = flax.linen.vmap(
    RNNModel, variable_axes={"params": None}, split_rngs={"params": False}, axis_name="batch"
)

from typing import Any, Optional, Tuple

def default_conv_init(scale: Optional[float] = jnp.sqrt(2)):
    return nn.initializers.xavier_uniform()

def default_mlp_init(scale: Optional[float] = 0.01):
    return nn.initializers.orthogonal(scale)

def default_logits_init(scale: Optional[float] = 0.01):
    return nn.initializers.orthogonal(scale)


class ResidualBlock(nn.Module):
    """Residual block."""
    num_channels: int
    prefix: str
    dtype: Any = jnp.float32

    @nn.compact
    def __call__(self, x):
        # Conv branch
        y = nn.relu(x)
        y = nn.Conv(self.num_channels,
                    kernel_size=[1, 1],
                    strides=(1, 1),
                    padding='SAME',
                    dtype=self.dtype,
                    kernel_init=default_conv_init(),
                    name=self.prefix + '/conv2d_1')(y)
        y = nn.relu(y)
        y = nn.Conv(self.num_channels,
                    kernel_size=[1, 1],
                    strides=(1, 1),
                    padding='SAME',
                    dtype=self.dtype,
                    kernel_init=default_conv_init(),
                    name=self.prefix + '/conv2d_2')(y)

        return y + x

class Impala(nn.Module):
    """IMPALA architecture."""
    prefix: str
    dtype: Any = jnp.float32

    @nn.compact
    def __call__(self, x):
        out = x
        for i, (num_channels, num_blocks) in enumerate([(128, 2), (64, 2),
                                                        (32, 2)]):
            conv = nn.Conv(num_channels,
                           kernel_size=[1, 1],
                           strides=(1, 1),
                           padding='SAME',
                           dtype=self.dtype,
                           kernel_init=default_conv_init(),
                           name=self.prefix + '/conv2d_%d' % i)
            out = conv(out)

            # out = nn.max_pool(out,
            #                   window_shape=(3, 3),
            #                   strides=(2, 2),
            #                   padding='SAME')
            for j in range(num_blocks):
                block = ResidualBlock(num_channels,
                                      prefix='residual_{}_{}'.format(i, j),
                                      dtype=self.dtype,)
                out = block(out)

        out = out.reshape(out.shape[0], -1)
        out = nn.relu(out)
        # out = nn.Dense(256, kernel_init=default_mlp_init(), name=self.prefix + '/representation')(out)
        # out = nn.relu(out)
        return out


class MaxPool2d(nn.Module):
    kernel_size: tuple[int, int]

    @nn.compact
    def __call__(self, x):
        return nn.max_pool(inputs=x, window_shape=self.kernel_size, strides=self.kernel_size, padding="VALID")


# not used currently
# class EmbeddingEncoder(nn.Module):
#     emb_dim: int = 2
#
#     @nn.compact
#     def __call__(self, img):
#         entity_emb = nn.Embed(NUM_TILES, self.emb_dim)
#         color_emb = nn.Embed(NUM_COLORS, self.emb_dim)
#
#         # [..., channels]
#         img_emb = jnp.concatenate([
#             entity_emb(img[..., 0]),
#             color_emb(img[..., 1]),
#         ], axis=-1)
#         return img_emb


class ActorCriticInput(TypedDict):
    observation: jax.Array
    prev_action: jax.Array
    prev_reward: jax.Array


class ActorCriticRNN(nn.Module):
    num_actions: int
    action_emb_dim: int = 16
    rnn_hidden_dim: int = 64
    rnn_num_layers: int = 1
    head_hidden_dim: int = 64
    cnn: str = 'impala'
    dtype: Any = jnp.float32
    amp_policy: jmp.Policy = None

    @nn.compact
    def __call__(self, inputs: ActorCriticInput, hidden: jax.Array) -> tuple[distrax.Categorical, jax.Array, jax.Array]:
        B, S = inputs["observation"].shape[:2]
        # encoder from https://github.com/lcswillems/rl-starter-files/blob/master/model.py
        if self.cnn == 'minigrid':
            img_encoder = nn.Sequential(
                [
                    nn.Conv(16, (2, 2), padding="VALID", kernel_init=orthogonal(math.sqrt(2)), dtype=self.dtype),
                    nn.relu,
                    # MaxPool2d((2, 2)),
                    nn.Conv(32, (2, 2), padding="VALID", kernel_init=orthogonal(math.sqrt(2)), dtype=self.dtype),
                    nn.relu,
                    nn.Conv(64, (2, 2), padding="VALID", kernel_init=orthogonal(math.sqrt(2)), dtype=self.dtype),
                    nn.relu,
                ]
            )
        elif self.cnn == 'impala':
            img_encoder = Impala('impala', dtype=self.dtype)
        else:
            raise ValueError()
        action_encoder = nn.Embed(self.num_actions, self.action_emb_dim)

        goal_encoder = nn.Dense(self.head_hidden_dim, dtype=self.dtype)

        rnn_core = BatchedRNNModel(self.rnn_hidden_dim, self.rnn_num_layers, amp_policy=self.amp_policy)
        actor = nn.Sequential(
            [
                nn.Dense(self.head_hidden_dim, kernel_init=orthogonal(2), dtype=self.dtype),
                nn.gelu,
                nn.Dense(self.num_actions, kernel_init=orthogonal(0.01), dtype=self.dtype),
            ]
        )
        critic = nn.Sequential(
            [
                nn.Dense(self.head_hidden_dim, kernel_init=orthogonal(2), dtype=self.dtype),
                nn.gelu,
                nn.Dense(1, kernel_init=orthogonal(1.0), dtype=self.dtype),
            ]
        )

        # [batch_size, seq_len, ...]
        if self.cnn == 'minigrid':
            obs_emb = img_encoder(inputs["observation"]).reshape(B, S, -1)
        elif self.cnn == 'impala':
            obs0 = inputs["observation"][..., 0].astype(jnp.int32) 
            obs1 = inputs["observation"][..., 1].astype(jnp.int32)
            obs = obs1 * 16 + obs0
            obs = self.amp_policy.cast_to_compute(jnp.eye(256)[obs])
            sh = obs.shape[2:]
            obs_emb = img_encoder(obs.reshape(B*S, *sh)).reshape(B, S, -1)
        act_emb = self.amp_policy.cast_to_compute(action_encoder(inputs["prev_action"]))
        goal_emb = goal_encoder(inputs['goal']) #* 0.
        # [batch_size, seq_len, hidden_dim + act_emb_dim + 1]
        out = jnp.concatenate([
            obs_emb, 
            act_emb, 
            # inputs["prev_reward"][..., None], 
            goal_emb], axis=-1)
        # core networks
        out, new_hidden = rnn_core(out, hidden)
        dist = distrax.Categorical(logits=actor(out))
        values = critic(out)

        return dist, jnp.squeeze(values, axis=-1), new_hidden

    def initialize_carry(self, batch_size):
        return jnp.zeros((batch_size, self.rnn_num_layers, self.rnn_hidden_dim))
