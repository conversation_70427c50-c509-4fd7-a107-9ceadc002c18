#!/bin/bash
#SBATCH --exclude=cn-g[001-012]
module load singularity/3.7.1
# on mila
# put your sif image here (better on a salloc'ed node):
# export SINGULARITY_CACHEDIR=$SLURM_TMPDIR/.singularity
# module load singularity 
# cd $SCRATCH
# singularity pull docker://artemzholus/jax_cuda11.8
### SBATCH --reservation=ubuntu2204
export WANDB_MODE=online
export WANDB_ENTITY=gree
export WANDB_PROJECT=lg
export WANDB_DIR=$RUN_DIR/"$SLURM_ARRAY_JOB_ID"_"$SLURM_ARRAY_TASK_ID"
export MASTER_PORT=$(expr 10000 + $(echo -n $SLURM_JOBID | tail -c 4))
export MASTER_ADDR=$(scontrol show hostnames "$SLURM_JOB_NODELIST" | head -n 1)
exec ./scripts/job.sh "$@"
