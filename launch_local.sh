#!/bin/bash
export XLA_PYTHON_CLIENT_PREALLOCATE=false
torchrun --nproc_per_node 4 \
    train_ppo.py \
    --pretrain google/gemma-2b-it \
    --reward_pretrain google/gemma-2b-it \
    --save_steps -1 \
    --logging_steps 1 \
    --eval_steps -1 \
    --micro_train_batch_size 16 \
    --train_batch_size 128 \
    --micro_rollout_batch_size 16 \
    --rollout_batch_size 256 \
    --max_epochs 1 \
    --prompt_max_len 256 \
    --generate_max_len 1024 \
    --zero_stage 2 \
    --bf16 \
    --actor_learning_rate 5e-7 \
    --lora_rank 1 \
    --critic_learning_rate 9e-6 \
    --init_kl_coef 0.01 \
    --max_samples 80000 \
    --normalize_reward \
    --actor_init_on_gpu \
    --adam_offload \
    --flash_attn \
    --gradient_checkpointing
#    --env TORCH_DISTRIBUTED_DEBUG=DETAIL \
#    --env NCCL_DEBUG=INFO \
#    --env TORCH_CPP_LOG_LEVEL=INFO \
#srun --ntasks=$SLURM_JOB_NUM_NODES --ntasks-per-node=1 rsync -r --progress $SCRATCH/data $SLURM_TMPDIR
# srun --ntasks=$SLURM_JOB_NUM_NODES --ntasks-per-node=1 \
#     singularity exec --nv \
#     -H $SCRATCH/tfm0:/home/<USER>
#     -B $SCRATCH/data:/home/<USER>/data \
#     -B $SCRATCH/mol_models:/home/<USER>/models \
#     --env WANDB_DIR=/tmp/ \
#     --env HYDRA_FULL_ERROR=1 \
#     --env TOKENIZERS_PARALLELISM=true \
#     --env HOME=/home \
#     $SCRATCH/img_latest.sif ./myrun.sh
