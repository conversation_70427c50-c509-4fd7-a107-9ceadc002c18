# langauge grounding 

for embodied training script, see `xland-minigrid/training/train_meta_task.py`

## Installation

Conda: 

```sh
echo """
envs_dirs:
  - $SCRATCH/conda_envs
env_prompt: ({name})
""" >> $HOME/.condarc
mkdir -p $SCRATCH/conda_envs
conda create --prefix $SCRATCH/conda_envs/lg python=3.10
conda activate lg
module load openmpi
module load cuda/12.1.1
# TODO: consider deleting linker from conda (env path / compiler_compat/ld)
conda env update -f conda_env.yaml
pip install mpi4py==3.1.4 or  conda install mpi4py   

pip install setuptools==69.5.1
# please run the following in a short-unk with 64 cores and at least 768GB of RAM.
# also , if you see that the .whl is installed (i.e. when you install it uses a .whl files which you see in the `pip isntall .. ` logs ) by default , please delete the .whl file from your computer. If it downloads the .tar.gz file in pip install , everything is good.
MAX_JOBS=64 pip install --no-build-isolation flash-attn==2.5.0
pip install --upgrade "jax[cuda12_pip]==0.4.28" -f https://storage.googleapis.com/jax-releases/jax_cuda_releases.html

pip install \
  "xminigrid @ git+https://github.com/corl-team/xland-minigrid.git" \
  jmp==0.0.4 \
  distrax==0.1.5 \
  orbax==0.1.9 \
  "orbax-checkpoint"==0.5.5 \
  flax==0.8.1
```

Singularity: 

On Mila cluster:

```sh
module load singularity
cd $SCRATCH
export SINGULARITY_CACHEDIR=$SCRATCH/.singularity
mkdir -p $SINGULARITY_CACHEDIR
singularity pull docker://artemzholus/lg
```

On DRAC (CC): just copy the image file from Mila:

TODO
```sh
rsync ...
```

## Running:

First, create an interactive job:

```sh
salloc --gres=gpu:a100l:4 --partition=short-unkillable --mem=512G -c 24 --time=3:00:00 --constraint=ampere'
```

Do not forget to add the policy file to scratch when running for the first time:

```sh
mkdir -p $SCRATCH/language_policies/symbolic_policy
unzip policy.zip && cd checkpoint_0 && cp -r ./* $SCRATCH/language_policies/symbolic_policy && cd .. && rm -rf checkpoint_0
```

Mila cluster, vscode debugger:

1) setup conda environment
2) create a debugger terminal (e.g. by pressing the run button)
3) activate env `conda activate lg`
4) do `module load cuda/12.1.1`
5) go to `./web_agents` and run `export MINIWOB_URL="file://$(pwd)/custom_miniwob_html/"`.

## Troubleshooting

*Address already in use* - this means, the other process is already
