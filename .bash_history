ls
pip list
pip list | grep deep
cd OpenRLHF/examples/
python     train_ppo.py     --pretrain google/gemma-2b-it     --reward_pretrain google/gemma-2b-it     --save_steps -1     --logging_steps 1     --eval_steps -1     --micro_train_batch_size 2     --train_batch_size 128     --micro_rollout_batch_size 4     --rollout_batch_size 1024     --max_epochs 1     --prompt_max_len 256     --generate_max_len 1024     --zero_stage 2     --bf16     --actor_learning_rate 5e-7     --critic_learning_rate 9e-6     --init_kl_coef 0.01     --max_samples 80000     --normalize_reward     --actor_init_on_gpu     --adam_offload     --flash_attn     --gradient_checkpointing
cd OpenRLHF/examples/
ls
python     train_ppo.py     --pretrain google/gemma-2b-it     --reward_pretrain google/gemma-2b-it     --save_steps -1     --logging_steps 1     --eval_steps -1     --micro_train_batch_size 2     --train_batch_size 128     --micro_rollout_batch_size 4     --rollout_batch_size 1024     --max_epochs 1     --prompt_max_len 256     --generate_max_len 1024     --zero_stage 2     --bf16     --actor_learning_rate 5e-7     --critic_learning_rate 9e-6     --init_kl_coef 0.01     --max_samples 80000     --normalize_reward     --actor_init_on_gpu     --adam_offload     --flash_attn     --gradient_checkpointing
hugginface login
huggingface-cli login
ls
ls
cd
ls
ls -lah
vim .cache/
ls
cd OpenRLHF/examples/
python     train_ppo.py     --pretrain google/gemma-2b-it     --reward_pretrain google/gemma-2b-it     --save_steps -1     --logging_steps 1     --eval_steps -1     --micro_train_batch_size 2     --train_batch_size 128     --micro_rollout_batch_size 4     --rollout_batch_size 1024     --max_epochs 1     --prompt_max_len 256     --generate_max_len 1024     --zero_stage 2     --bf16     --actor_learning_rate 5e-7     --critic_learning_rate 9e-6     --init_kl_coef 0.01     --max_samples 80000     --normalize_reward     --actor_init_on_gpu     --adam_offload     --flash_attn     --gradient_checkpointing
python     train_ppo.py     --pretrain google/gemma-2b-it     --reward_pretrain google/gemma-2b-it     --save_steps -1     --logging_steps 1     --eval_steps -1     --micro_train_batch_size 2     --train_batch_size 128     --micro_rollout_batch_size 4     --rollout_batch_size 1024     --max_epochs 1     --prompt_max_len 256     --generate_max_len 1024     --zero_stage 2     --bf16     --actor_learning_rate 5e-7     --critic_learning_rate 9e-6     --init_kl_coef 0.01     --max_samples 80000     --normalize_reward     --actor_init_on_gpu     --adam_offload     --flash_attn     --gradient_checkpointing
cd OpenRLHF/examples/
python     train_ppo.py     --pretrain google/gemma-2b-it     --reward_pretrain google/gemma-2b-it     --save_steps -1     --logging_steps 1     --eval_steps -1     --micro_train_batch_size 2     --train_batch_size 128     --micro_rollout_batch_size 4     --rollout_batch_size 1024     --max_epochs 1     --prompt_max_len 256     --generate_max_len 1024     --zero_stage 2     --bf16     --actor_learning_rate 5e-7     --critic_learning_rate 9e-6     --init_kl_coef 0.01     --max_samples 80000     --normalize_reward     --actor_init_on_gpu     --adam_offload     --flash_attn     --gradient_checkpointing
python     train_ppo.py     --pretrain google/gemma-2b-it     --reward_pretrain google/gemma-2b-it     --save_steps -1     --logging_steps 1     --eval_steps -1     --micro_train_batch_size 2     --train_batch_size 128     --micro_rollout_batch_size 4     --rollout_batch_size 1024     --max_epochs 1     --prompt_max_len 256     --generate_max_len 1024     --zero_stage 2     --bf16     --actor_learning_rate 5e-7     --critic_learning_rate 9e-6     --init_kl_coef 0.01     --max_samples 80000     --normalize_reward     --actor_init_on_gpu     --adam_offload     --flash_attn     --gradient_checkpointing
cd OpenRLHF/examples/
python     train_ppo.py     --pretrain google/gemma-2b-it     --reward_pretrain google/gemma-2b-it     --save_steps -1     --logging_steps 1     --eval_steps -1     --micro_train_batch_size 2     --train_batch_size 128     --micro_rollout_batch_size 4     --rollout_batch_size 1024     --max_epochs 1     --prompt_max_len 256     --generate_max_len 1024     --zero_stage 2     --bf16     --actor_learning_rate 5e-7     --critic_learning_rate 9e-6     --init_kl_coef 0.01     --max_samples 80000     --normalize_reward     --actor_init_on_gpu     --adam_offload     --flash_attn     --gradient_checkpointing
cd OpenRLHF/examples/
python     train_ppo.py     --pretrain google/gemma-2b-it     --reward_pretrain google/gemma-2b-it     --save_steps -1     --logging_steps 1     --eval_steps -1     --micro_train_batch_size 2     --train_batch_size 128     --micro_rollout_batch_size 4     --rollout_batch_size 1024     --max_epochs 1     --prompt_max_len 256     --generate_max_len 1024     --zero_stage 2     --bf16     --actor_learning_rate 5e-7     --critic_learning_rate 9e-6     --init_kl_coef 0.01     --max_samples 80000     --normalize_reward     --actor_init_on_gpu     --adam_offload     --flash_attn     --gradient_checkpointing
pip list | grep vllm
python     train_ppo.py     --pretrain google/gemma-2b-it     --reward_pretrain google/gemma-2b-it     --save_steps -1     --logging_steps 1     --eval_steps -1     --micro_train_batch_size 2     --train_batch_size 128     --micro_rollout_batch_size 4     --rollout_batch_size 1024     --max_epochs 1     --prompt_max_len 256     --generate_max_len 1024     --zero_stage 2     --bf16     --actor_learning_rate 5e-7     --critic_learning_rate 9e-6     --init_kl_coef 0.01     --max_samples 80000     --normalize_reward     --actor_init_on_gpu     --adam_offload     --flash_attn     --gradient_checkpointing --lora_rank 8
cd OpenRLHF/examples/
python     train_ppo.py     --pretrain google/gemma-2b-it     --reward_pretrain google/gemma-2b-it     --save_steps -1     --logging_steps 1     --eval_steps -1     --micro_train_batch_size 2     --train_batch_size 128     --micro_rollout_batch_size 4     --rollout_batch_size 1024     --max_epochs 1     --prompt_max_len 256     --generate_max_len 1024     --zero_stage 2     --bf16     --actor_learning_rate 5e-7     --critic_learning_rate 9e-6     --init_kl_coef 0.01     --max_samples 80000     --normalize_reward     --actor_init_on_gpu     --adam_offload     --flash_attn     --gradient_checkpointing --lora_rank 8
python     train_ppo.py     --pretrain google/gemma-2b-it     --reward_pretrain google/gemma-2b-it     --save_steps -1     --logging_steps 1     --eval_steps -1     --micro_train_batch_size 2     --train_batch_size 128     --micro_rollout_batch_size 4     --rollout_batch_size 1024     --max_epochs 1     --prompt_max_len 256     --generate_max_len 1024     --zero_stage 2     --bf16     --actor_learning_rate 5e-7     --critic_learning_rate 9e-6     --init_kl_coef 0.01     --max_samples 80000     --normalize_reward     --actor_init_on_gpu     --adam_offload     --flash_attn     --gradient_checkpointing --lora_rank 8
python     train_ppo.py     --pretrain google/gemma-2b-it     --reward_pretrain google/gemma-2b-it     --save_steps -1     --logging_steps 1     --eval_steps -1     --micro_train_batch_size 2     --train_batch_size 128     --micro_rollout_batch_size 4     --rollout_batch_size 1024     --max_epochs 1     --prompt_max_len 256     --generate_max_len 1024     --zero_stage 2     --bf16     --actor_learning_rate 5e-7     --critic_learning_rate 9e-6     --init_kl_coef 0.01     --max_samples 80000     --normalize_reward     --actor_init_on_gpu     --adam_offload     --flash_attn     --gradient_checkpointing --lora_rank 8
python     train_ppo.py     --pretrain google/gemma-2b-it     --reward_pretrain google/gemma-2b-it     --save_steps -1     --logging_steps 1     --eval_steps -1     --micro_train_batch_size 2     --train_batch_size 128     --micro_rollout_batch_size 4     --rollout_batch_size 1024     --max_epochs 1     --prompt_max_len 256     --generate_max_len 1024     --zero_stage 2     --bf16     --actor_learning_rate 5e-7     --critic_learning_rate 9e-6     --init_kl_coef 0.01     --max_samples 80000     --normalize_reward     --actor_init_on_gpu     --adam_offload     --flash_attn     --gradient_checkpointing --lora_rank 8
python     train_ppo.py     --pretrain google/gemma-2b-it     --reward_pretrain google/gemma-2b-it     --save_steps -1     --logging_steps 1     --eval_steps -1     --micro_train_batch_size 2     --train_batch_size 128     --micro_rollout_batch_size 4     --rollout_batch_size 1024     --max_epochs 1     --prompt_max_len 256     --generate_max_len 1024     --zero_stage 2     --bf16     --actor_learning_rate 5e-7     --critic_learning_rate 9e-6     --init_kl_coef 0.01     --max_samples 80000     --normalize_reward     --actor_init_on_gpu     --adam_offload     --flash_attn     --gradient_checkpointing --lora_rank 8
python     train_ppo.py     --pretrain google/gemma-2b-it     --reward_pretrain google/gemma-2b-it     --save_steps -1     --logging_steps 1     --eval_steps -1     --micro_train_batch_size 2     --train_batch_size 128     --micro_rollout_batch_size 4     --rollout_batch_size 1024     --max_epochs 1     --prompt_max_len 256     --generate_max_len 1024     --zero_stage 2     --bf16     --actor_learning_rate 5e-7     --critic_learning_rate 9e-6     --init_kl_coef 0.01     --max_samples 80000     --normalize_reward     --actor_init_on_gpu     --adam_offload     --flash_attn     --gradient_checkpointing --lora_rank 8
python     train_ppo.py     --pretrain google/gemma-2b-it     --reward_pretrain google/gemma-2b-it     --save_steps -1     --logging_steps 1     --eval_steps -1     --micro_train_batch_size 2     --train_batch_size 128     --micro_rollout_batch_size 4     --rollout_batch_size 1024     --max_epochs 1     --prompt_max_len 256     --generate_max_len 1024     --zero_stage 2     --bf16     --actor_learning_rate 5e-7     --critic_learning_rate 9e-6     --init_kl_coef 0.01     --max_samples 80000     --normalize_reward     --actor_init_on_gpu     --adam_offload     --flash_attn     --gradient_checkpointing --lora_rank 8
export XLA_PYTHON_CLIENT_PREALLOCATE=false
export XLA_PYTHON_CLIENT_PREALLOCATE=false
python     train_ppo.py     --pretrain google/gemma-2b-it     --reward_pretrain google/gemma-2b-it     --save_steps -1     --logging_steps 1     --eval_steps -1     --micro_train_batch_size 2     --train_batch_size 128     --micro_rollout_batch_size 4     --rollout_batch_size 1024     --max_epochs 1     --prompt_max_len 256     --generate_max_len 1024     --zero_stage 2     --bf16     --actor_learning_rate 5e-7     --critic_learning_rate 9e-6     --init_kl_coef 0.01     --max_samples 80000     --normalize_reward     --actor_init_on_gpu     --adam_offload     --flash_attn     --gradient_checkpointing --lora_rank 8
python     train_ppo.py     --pretrain google/gemma-2b-it     --reward_pretrain google/gemma-2b-it     --save_steps -1     --logging_steps 1     --eval_steps -1     --micro_train_batch_size 2     --train_batch_size 128     --micro_rollout_batch_size 4     --rollout_batch_size 1024     --max_epochs 1     --prompt_max_len 256     --generate_max_len 1024     --zero_stage 2     --bf16     --actor_learning_rate 5e-7     --critic_learning_rate 9e-6     --init_kl_coef 0.01     --max_samples 80000     --normalize_reward     --actor_init_on_gpu     --adam_offload     --flash_attn     --gradient_checkpointing --lora_rank 8 --use_wandb True
cd
