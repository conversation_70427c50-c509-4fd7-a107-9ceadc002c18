torchrun --nproc_per_node 4 train_ppo_web.py --pretrain Qwen/Qwen2.5-3B-Instruct --reward_pretrain Qwen/Qwen2.5-3B-Instruct --save_steps -1 --logging_steps 1 --eval_steps -1 --micro_train_batch_size 2 --train_batch_size 128 --micro_rollout_batch_size 4 --rollout_batch_size 128 --max_epochs 1 --prompt_max_len 4096 --generate_max_len 256 --zero_stage 2 --bf16 --temperature 0.8 --actor_learning_rate 5e-7 --critic_learning_rate 9e-6 --init_kl_coef 0.01 --max_samples 80000 --normalize_reward --actor_init_on_gpu --flash_attn --gradient_checkpointing --num_episodes 100 --use_wandb
