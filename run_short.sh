#!/bin/bash
export N_GPUS=4
export N_NODES=1
export NSEEDS=0
export SLURM_CPU=--cpus-per-task=24
export SLURM_TIME=--time=0-03:00
export SLURM_MEM=--mem=128G
export PARTITION="short-unkillable"
./scripts/spawn.sh train_ppo.py \
    --pretrain google/gemma-2b-it \
    --reward_pretrain google/gemma-2b-it \
    --save_steps -1 \
    --logging_steps 1 \
    --eval_steps -1 \
    --micro_train_batch_size 2 \
    --train_batch_size 1024 \
    --micro_rollout_batch_size 2 \
    --rollout_batch_size 1024 \
    --max_epochs 2 \
    --prompt_max_len 256 \
    --generate_max_len 256 \
    --zero_stage 2 \
    --bf16 \
    --actor_learning_rate 5e-7 \
    --critic_learning_rate 9e-6 \
    --init_kl_coef 0.01 \
    --max_samples 80000 \
    --normalize_reward \
    --actor_init_on_gpu \
    --flash_attn \
    --gradient_checkpointing \
    --use_wandb True \
    --wandb_org chandar-rl
#  --adam_offload \
