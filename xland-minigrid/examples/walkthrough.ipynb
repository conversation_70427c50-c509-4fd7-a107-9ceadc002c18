{"cells": [{"cell_type": "markdown", "id": "f093150d-1b9b-433a-ba6d-82173d71bfea", "metadata": {}, "source": ["<a target=\"_blank\" href=\"https://colab.research.google.com/github/corl-team/xland-minigrid/blob/main/examples/walkthrough.ipynb\">\n", "  <img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/>\n", "</a>"]}, {"cell_type": "markdown", "id": "9bbba0e7-f695-4094-a24e-a9f320416d63", "metadata": {}, "source": ["# XLand-MiniGrid Walkthrough\n", "\n", "**Last updated: 01.12.2023**\n", "\n", "Welcome to the walkthrough the XLand-MiniGrid library. This notebook will showcase our environments and bechmarks APIs, explaning the details and our motivations. It will also provide vectorization and multi-device parallelization examples. For full baselines training demo see our notebooks with standalone PPO implementations.\n", "\n", "> ⚠️ Ensure you select a GPU from `Runtime > Change runtime type`. ⚠️\n", "\n", "> ⚠️ Colab TPU runtime is not compatible with new JAX versions (> 0.4.0). Please, use kaggle notebooks if you want to use TPUs. There is no quick way to open notebook from github in kaggle (like colab badge above), so you will need to manually upload it. ⚠️\n", "\n", "> 🔥 Instances with multiple T4 gpus are available on Kaggle for free! Multi-gpu can speed up examples with `pmap`. 🔥"]}, {"cell_type": "markdown", "id": "520f633a-9b22-4bd9-9cb2-4e705387c2ee", "metadata": {}, "source": ["### Install dependencies"]}, {"cell_type": "markdown", "id": "17e39f65-b099-486e-8c1f-9087aac7d8e2", "metadata": {}, "source": ["We install JAX with GPU support and some other utilities, it may take a while... "]}, {"cell_type": "code", "execution_count": null, "id": "578368ae-f59e-48f2-81dd-ac48dd00fc45", "metadata": {}, "outputs": [], "source": ["# jax is already installed on the colab, uncomment only if needed\n", "# !pip install --upgrade \"jax[cuda11_pip]\" -f https://storage.googleapis.com/jax-releases/jax_cuda_releases.html\n", "\n", "# !pip install xminigrid\n", "!pip install \"xminigrid[baselines] @ git+https://github.com/corl-team/xland-minigrid.git\""]}, {"cell_type": "code", "execution_count": null, "id": "0d3a10ef-7e67-4a5b-974b-a2403448a19a", "metadata": {}, "outputs": [], "source": ["# general utilities\n", "import jax\n", "import jax.numpy as jnp\n", "import jax.tree_util as jtu\n", "import numpy as np\n", "\n", "import timeit\n", "import imageio\n", "import matplotlib.pyplot as plt\n", "from tqdm.auto import trange, tqdm\n", "\n", "def show_img(img, dpi=32):\n", "    plt.figure(dpi=dpi)\n", "    plt.axis('off')\n", "    plt.imshow(img)"]}, {"cell_type": "markdown", "id": "d94ce761-537f-4bd0-bcc2-2dd867b97891", "metadata": {}, "source": ["## Environments"]}, {"cell_type": "markdown", "id": "81271879-0e6c-4830-b336-33abe70f39e5", "metadata": {}, "source": ["As we noted in the README, currently, there are a lot of new JAX-based environments appearing, each offering its own variant of API. Initially, we tried to reuse Jumaji, but it turned out that its design is not suitable for meta learning. The Gymnax design appeared to be more appropriate, but unfortunately it is not actively supported and often departs from the idea that parameters should only be contained in env_params. Furthermore, splitting timestep into multiple entities seems suboptimal to us, as it complicates many things, such as envpool or dm-env style auto reset, where the reset occurs on the next step (we need access to done of previous step).\n", "\n", "Therefore, we decided that we would make a minimal interface that would cover just our needs without the goal of making it generic. The core of our library is interface independent, and we plan to switch to the new one when/if a better design becomes available (e.g. when stable Gymnasium FuncEnv is released)."]}, {"cell_type": "markdown", "id": "8c14fc87-0e29-4e60-8bc1-43816719516e", "metadata": {}, "source": ["### API"]}, {"cell_type": "markdown", "id": "bd568245-33fa-4adc-9621-70e1e0f772db", "metadata": {}, "source": ["Most users who are familiar with other popular JAX-based environments (such as Gymnax or Jumnaji), will find that the interface is very similar. On the high level, current API combines DeepMind Environment and gymnax interfaces.\n", "\n", "\n", "All our environments follow the structure of the following base class:\n", "```python\n", "class EnvParams(struct.PyTreeNode):\n", "    ...\n", "\n", "\n", "class Environment:\n", "    def default_params(self, **kwargs) -> EnvParams:\n", "        return EnvParams().replace(**kwargs)\n", "\n", "    def num_actions(self, params: EnvParams) -> int:\n", "        ...\n", "\n", "    def observation_shape(self, params: EnvParams) -> tuple[int, int, int]:\n", "        ...\n", "\n", "    def time_limit(self, params: EnvParams) -> int:\n", "        ...\n", "\n", "    def _generate_problem(self, params: Env<PERSON>ara<PERSON>, key: jax.<PERSON><PERSON><PERSON>) -> State:\n", "        ...\n", "\n", "    def reset(self, params: Env<PERSON><PERSON><PERSON>, key: jax.<PERSON><PERSON><PERSON>) -> TimeStep:\n", "        ...\n", "\n", "    def step(self, params: EnvParams, timestep: TimeStep, action: int) -> TimeStep:\n", "        ...\n", "\n", "    def render(self, params: EnvParams, timestep: TimeStep) -> jax.Array:\n", "        ...    \n", "```\n", "\n", "with main methods being `reset` and `step` which are fully jit-compatible. Overall the environment instance itself is stateless. The environment state as well as step info is stored in the `TimeStep` container:\n", "\n", "```python\n", "class TimeStep(struct.PyTreeNode):\n", "    # hidden environment state, such as grid, agent, goal, etc \n", "    state: State\n", "\n", "    # similar to the dm_env enterface\n", "    step_type: StepType\n", "    reward: jax.<PERSON><PERSON><PERSON>\n", "    discount: jax.Array\n", "    observation: j<PERSON><PERSON><PERSON>\n", "```"]}, {"cell_type": "markdown", "id": "28836673-d9b4-40ef-9ab4-bd6739c5e1c4", "metadata": {}, "source": ["Let's create simple environment with the help of `make`, jit-compile the `reset` and `step`, step the environment and visualize the resuling state:"]}, {"cell_type": "code", "execution_count": null, "id": "6ce21f46-dc20-47ca-83ca-94114dce52f8", "metadata": {}, "outputs": [], "source": ["import xminigrid\n", "\n", "key = jax.random.PRNG<PERSON>ey(0)\n", "key, reset_key = jax.random.split(key)\n", "\n", "# to list available environments: xminigrid.registered_environments()\n", "env, env_params = xminigrid.make(\"MiniGrid-Empty-8x8\")\n", "print(\"Observation shape:\", env.observation_shape(env_params))\n", "print(\"Num actions:\", env.num_actions(env_params))\n", "\n", "# fully jit-compatible step and reset methods\n", "timestep = jax.jit(env.reset)(env_params, reset_key)\n", "timestep = jax.jit(env.step)(env_params, timestep, action=0)\n", "\n", "print(\"TimeStep shapes:\", jtu.tree_map(jnp.shape, timestep))\n", "\n", "show_img(env.render(env_params, timestep), dpi=64)"]}, {"cell_type": "markdown", "id": "838553a4-a3d4-4664-b10d-87cc319e21d1", "metadata": {}, "source": ["### Registry"]}, {"cell_type": "markdown", "id": "f6eceb55-0c4b-4666-8ca6-4b5a4caa7be2", "metadata": {}, "source": ["Similar to the other libraries we provide convenient registy utilities, such that users can get all registered environments or `register` new environments (or variations of the already provided) for the further usage with `make`. Registration is not compatible with jit, so creating environments has to happen outside. However, you can create them inside jit with the original classes from `xminigrid.envs` directly. "]}, {"cell_type": "code", "execution_count": null, "id": "098753af-8c8b-4ee6-9d8f-2ed6f5004eaf", "metadata": {}, "outputs": [], "source": ["# first four registered standard environments\n", "xminigrid.registered_environments()[:4]"]}, {"cell_type": "markdown", "id": "08422b38-bea6-47f5-8be4-43149f14424a", "metadata": {}, "source": ["Let's register new example environment. You can provide arbitrary environment arguments, which will be provided to the `env.default_params` upon creating inside `make`. Given that the environment itself does not contain parameters and states, one can realise that registration actually just provides a convenient way to get default parameters for different setups. Users can freely change these `env_params` after creation. However, we do not advice this outside of the meta-RL applications. Better to explicitly register new parameters variations."]}, {"cell_type": "code", "execution_count": null, "id": "b03fc26f-dbef-4773-92b9-020b46bc8984", "metadata": {}, "outputs": [], "source": ["xminigrid.register(\n", "    id=\"MiniGrid-EmptyRandom-9x9\",\n", "    entry_point=\"xminigrid.envs.minigrid.empty:EmptyRandom\",\n", "    height=9,\n", "    width=9\n", ")"]}, {"cell_type": "markdown", "id": "71ca394a-53e1-44a4-8792-e1e472b702d3", "metadata": {}, "source": ["We enforce some limitations on the environment names for the consistency. All names should start from the `XLand-MiniGrid` (for meta-RL) or `MiniGrid` (for just RL). This is just a convention."]}, {"cell_type": "code", "execution_count": null, "id": "d6d5873e-d78e-4251-941a-c40a9fa2d543", "metadata": {}, "outputs": [], "source": ["xminigrid.register(\n", "    id=\"Some_name\",\n", "    entry_point=\"xminigrid.envs.minigrid.empty:EmptyRandom\",\n", "    height=9,\n", "    width=9\n", ")"]}, {"cell_type": "markdown", "id": "c8ec980c-63ae-467d-806c-c409150d2b2d", "metadata": {}, "source": ["All environment parameters can be provided to the `make` and will overwrite the default ones. This is equivalent to the `env_params.replace(view_size=3)` afther creation."]}, {"cell_type": "code", "execution_count": null, "id": "0c4913f8-19f9-4b54-a265-b341fe91e81f", "metadata": {}, "outputs": [], "source": ["env, env_params = xminigrid.make(\"MiniGrid-EmptyRandom-9x9\", view_size=3)\n", "timestep = jax.jit(env.reset)(env_params, reset_key)\n", "\n", "print(\"Params:\", env_params)\n", "show_img(env.render(env_params, timestep), dpi=64)"]}, {"cell_type": "markdown", "id": "ff96b8d5-727d-4a8c-bdef-2602b5d578f9", "metadata": {}, "source": ["### <PERSON><PERSON> through entire episodes"]}, {"cell_type": "markdown", "id": "604c3610-f126-4d6a-8724-85a2a53ccaaf", "metadata": {}, "source": ["Main advantage of jit-compatible environments is ability to scan through the entire episodes avoiding python. We provide here a small example of such scanned rollout with random policy. However, this template is quite general and we use it in our baselines implementations. With such design it is extremely easy to vectorize and parallelize it with `vmap` and `pmap` transformations. We also use `xminigrid.wrappers.GymAutoResetWrapper` here to auto-reset the environment. Two auto-reset styles are available: `GymAutoResetWrapper` and `DmEnvAutoResetWrapper`. With gym style environment will reset on the same step with `done`, while with dm_env style environment will reset on the next step after done (action will be ignored).\n", "\n", "For the examples on how to rollout until `done` see rollout function used for evaluation during baselines training."]}, {"cell_type": "code", "execution_count": null, "id": "4a3df1c4-1361-4ac9-9aba-20ac37e1be23", "metadata": {}, "outputs": [], "source": ["from xminigrid.wrappers import GymAutoResetWrapper\n", "\n", "# alternatively users can provide step_fn and reset_fn instead\n", "# of the closure, but in whis way it is simpler to use after the creation\n", "def build_rollout(env, env_params, num_steps):\n", "    def rollout(rng):\n", "        def _step_fn(carry, _):\n", "            rng, timestep = carry\n", "            rng, _rng = jax.random.split(rng)\n", "            action = jax.random.randint(_rng, shape=(), minval=0, maxval=env.num_actions(env_params))\n", "            \n", "            timestep = env.step(env_params, timestep, action)\n", "            return (rng, timestep), timestep\n", "    \n", "        rng, _rng = jax.random.split(rng)\n", "    \n", "        timestep = env.reset(env_params, _rng)\n", "        rng, transitions = jax.lax.scan(_step_fn, (rng, timestep), None, length=num_steps)\n", "        return transitions\n", "\n", "    return rollout\n", "\n", "\n", "env, env_params = xminigrid.make(\"MiniGrid-EmptyRandom-8x8\")\n", "# do not forget to use auto reset wrapper!\n", "env = GymAutoResetWrapper(env)\n", "\n", "# jiting the entire rollout\n", "rollout_fn = jax.jit(build_rollout(env, env_params, num_steps=1000))\n", "\n", "# first execution will compile\n", "transitions = rollout_fn(jax.random.PRNGKey(0))\n", "\n", "print(\"Transitions shapes: \\n\", jtu.tree_map(jnp.shape, transitions))"]}, {"cell_type": "markdown", "id": "a68b561f-07da-48ef-b465-75d5dc671f3d", "metadata": {}, "source": ["### Visualization"]}, {"cell_type": "markdown", "id": "4e58df57-baa2-44b8-a841-2c10a8cce1bb", "metadata": {}, "source": ["Rollouts can be visualized as usuall, just record all timesteps, then render them with `env.render`:"]}, {"cell_type": "code", "execution_count": null, "id": "c157ab12-ca46-44bb-beee-0aadd1955337", "metadata": {}, "outputs": [], "source": ["images = []\n", "\n", "for i in trange(1000):\n", "    timestep = jtu.tree_map(lambda x: x[i], transitions)\n", "    images.append(env.render(env_params, timestep))\n", "\n", "imageio.mimsave(\"example_rollout.mp4\", images, fps=32, format=\"mp4\")"]}, {"cell_type": "code", "execution_count": null, "id": "7cf05301-8fe1-49fa-b804-b2c78897435a", "metadata": {}, "outputs": [], "source": ["from IPython.display import Video\n", "\n", "Video(\"example_rollout.mp4\", embed=True)"]}, {"cell_type": "markdown", "id": "737ffb48-ecb9-4227-95bd-315c5d138c37", "metadata": {}, "source": ["### Vectorization"]}, {"cell_type": "markdown", "id": "25d6e40c-8a24-463c-a6d1-37f4448e26ae", "metadata": {}, "source": ["We can easily vectorize the rollout with `jax.vmap`:"]}, {"cell_type": "code", "execution_count": null, "id": "2a119bce-f198-4165-8a33-71642daa428c", "metadata": {}, "outputs": [], "source": ["vmap_rollout = jax.jit(jax.vmap(build_rollout(env, env_params, num_steps=1000)))\n", "rngs = jax.random.split(jax.random.PRNGKey(0), num=1024)\n", "\n", "vmap_transitions = vmap_rollout(rngs)\n", "\n", "print(\"Transitions shapes: \\n\", jtu.tree_map(jnp.shape, vmap_transitions))"]}, {"cell_type": "markdown", "id": "0165e343-f4f8-474d-ab43-0dfd4e2708f2", "metadata": {}, "source": ["After that we cat just as easily parallelize among devices available with `jax.pmap`:"]}, {"cell_type": "code", "execution_count": null, "id": "90c187e1-6deb-4b5d-8256-af2ebd846c49", "metadata": {}, "outputs": [], "source": ["# pmap will jit\n", "pmap_rollout = jax.pmap(jax.vmap(build_rollout(env, env_params, 1000)))\n", "pmap_rngs = rngs.reshape(jax.local_device_count(), 1024 // jax.local_device_count(), -1)\n", "\n", "pmap_transitions = pmap_rollout(pmap_rngs)\n", "print(\"Transitions shapes: \\n\", jtu.tree_map(jnp.shape, pmap_transitions))"]}, {"cell_type": "markdown", "id": "317943b1-a79f-416d-ac8b-147938c91bf7", "metadata": {}, "source": ["### Measuring FPS"]}, {"cell_type": "markdown", "id": "206a6a54-3b9a-4ee4-8906-0a5a6badd9c4", "metadata": {}, "source": ["Knowing all of the above we can make a small benchmark to measure FPS for a random policy. At the beginning we will make a scanned rollout, then vectorize and parallelize it."]}, {"cell_type": "code", "execution_count": null, "id": "e395f8d6-6f83-47b1-8d94-5ff7db14bbb5", "metadata": {}, "outputs": [], "source": ["def build_benchmark(env_id, num_envs, timesteps):\n", "    env, env_params = xminigrid.make(env_id)\n", "    env = GymAutoResetWrapper(env)\n", "    \n", "    def benchmark_fn(key):\n", "        def _step_fn(timestep, action):\n", "            new_timestep = jax.vmap(env.step, in_axes=(None, 0, 0))(env_params, timestep, action)\n", "            return new_timestep, None\n", "\n", "        key, actions_key = jax.random.split(key)\n", "        keys = jax.random.split(key, num=num_envs)\n", "        actions = jax.random.randint(\n", "            actions_key, shape=(timesteps, num_envs), minval=0, maxval=env.num_actions(env_params)\n", "        )\n", "        timestep = jax.vmap(env.reset, in_axes=(None, 0))(env_params, keys)\n", "        timestep = jax.lax.scan(_step_fn, timestep, actions, unroll=1)[0]\n", "        return timestep\n", "\n", "    return benchmark_fn\n", "\n", "\n", "# see https://stackoverflow.com/questions/56763416/what-is-diffrence-between-number-and-repeat-in-python-timeit\n", "# on why we divide by num_iter\n", "def timeit_benchmark(benchmark_fn, num_iter=1, num_repeat=10):\n", "    benchmark_fn().state.grid.block_until_ready()\n", "    times = timeit.repeat(\n", "        lambda: benchmark_fn().state.grid.block_until_ready(),\n", "        number=num_iter,\n", "        repeat=num_repeat,\n", "    )\n", "    times = np.array(times) / num_iter\n", "    elapsed_time = np.max(times)\n", "\n", "    return elapsed_time"]}, {"cell_type": "code", "execution_count": null, "id": "d95d4e4f-2380-4455-a6fe-dbb6f2435129", "metadata": {}, "outputs": [], "source": ["num_devices = jax.local_device_count()\n", "envs_range = [512, 1024, 2048, 4096, 8192]\n", "print(\"Num devices for pmap:\", num_devices)\n", "\n", "vmap_stats, pmap_stats = [], []\n", "for num_envs in tqdm(envs_range):\n", "    # building vmap for vectorization benchmarking\n", "    benchmark_fn_vmap = build_benchmark(\"MiniGrid-EmptyRandom-8x8\", num_envs, 1024)\n", "    benchmark_fn_vmap = jax.jit(benchmark_fn_vmap)\n", "    # building pmap for multi-gpu benchmarking (each doing (num_envs / num_devices) vmaps)\n", "    benchmark_fn_pmap = build_benchmark(\"MiniGrid-EmptyRandom-8x8\", num_envs // num_devices, 1024)\n", "    benchmark_fn_pmap = jax.pmap(benchmark_fn_pmap)\n", "\n", "    key = jax.random.PRNG<PERSON>ey(0)\n", "    pmap_keys = jax.random.split(key, num=num_devices)\n", "\n", "    elapsed_time = timeit_benchmark(jax.tree_util.Partial(benchmark_fn_vmap, key))\n", "    vmap_fps = (1024 * num_envs) / elapsed_time\n", "\n", "    elapsed_time = timeit_benchmark(jax.tree_util.Partial(benchmark_fn_pmap, pmap_keys))\n", "    pmap_fps = (1024 * num_envs) / elapsed_time\n", "\n", "    vmap_stats.append(vmap_fps)\n", "    pmap_stats.append(pmap_fps)"]}, {"cell_type": "markdown", "id": "eee66c3c-264c-4223-87f9-7e38d99bbf98", "metadata": {}, "source": ["Note that the actual values may differ from the reported in the paper, as the default colab GPU is less powerfull that A100 used in the main experiments. But even on the one T4 GPU users can expect to get ~**10M** steps per second (and ~**25M** with two T4 GPUs on kaggle notebooks with `pmap`)!! However, compared to A100 scaling will saturate a lot earilier with respect to number of parallel environments."]}, {"cell_type": "code", "execution_count": null, "id": "f9001dd7-90dd-4917-a53a-9b9aed2fcd45", "metadata": {}, "outputs": [], "source": ["for n, vfps, pfps in zip(envs_range, vmap_stats, pmap_stats):\n", "    print(f\"{n} envs. vmap fps: {vfps}, pmap fps: {pfps}\")"]}, {"cell_type": "code", "execution_count": null, "id": "d889d7a2-fa0c-4cfd-94cc-36933cc10f77", "metadata": {}, "outputs": [], "source": ["plt.plot(envs_range, vmap_stats, \"o-\", label=\"vmap\", linewidth=2.0);\n", "plt.plot(envs_range, pmap_stats, \"o-\", label=\"pmap\", linewidth=2.0);\n", "plt.xscale('log', base=2)\n", "plt.yscale('log', base=10)\n", "plt.grid();\n", "plt.ylabel(\"Steps per second\");\n", "plt.xlabel(\"Environments\")\n", "plt.legend(loc=\"upper left\");"]}, {"cell_type": "markdown", "id": "785c5ae2-4787-49c0-a184-98b456df1897", "metadata": {}, "source": ["## Rules & Goals"]}, {"cell_type": "markdown", "id": "6db4f8e7-c5a7-4999-9575-c1bed7d692dd", "metadata": {}, "source": ["In XLand-MiniGrid, the system of rules and goals is the cornerstone of the emergent complexity and diversity. In the original MiniGrid some environments have dynamic goals, but the dynamics are never changed. To train and evaluate highly adaptive agents, we need to be able to change the dynamics in non-trivial ways.\n", "\n", "**Rules** are the functions that can change the environment state in some deterministic way according to the given conditions. **Goals** are similar to rules, except they do not change the state, they only test conditions. \n", "\n", "This section examples are a bit low level, but they will help you to understand how rules and goals work under the hood."]}, {"cell_type": "code", "execution_count": null, "id": "29ea909c-807c-4b2f-904a-08d0c5c0cf39", "metadata": {}, "outputs": [], "source": ["# utils for the demonstation\n", "from xminigrid.core.grid import room\n", "from xminigrid.types import AgentState\n", "from xminigrid.core.actions import take_action\n", "from xminigrid.core.constants import Tiles, Colors, TILES_REGISTRY\n", "from xminigrid.rendering.rgb_render import render\n", "\n", "# rules and goals\n", "from xminigrid.core.goals import check_goal, AgentNearGoal\n", "from xminigrid.core.rules import check_rule, AgentNearRule"]}, {"cell_type": "markdown", "id": "12369935-e088-4cb9-930a-462dac3b5786", "metadata": {}, "source": ["Let's setup simple one-room environment. We will spawn one yellow ball near the agent. After that we will perform an action moving agent one tile foward with the help of `take_action`. This function returns udpated grid, agent and also some position of interest (usually the position of object that somehow changed), which is needed for goals and rules checking. "]}, {"cell_type": "code", "execution_count": null, "id": "44825355-55eb-4781-a7d2-39dfb0820ca1", "metadata": {}, "outputs": [], "source": ["# for testing\n", "grid = room(6, 6)\n", "grid = grid.at[1, 1].set(TILES_REGISTRY[Tiles.BALL, Colors.YELLOW])\n", "\n", "agent = AgentState(position=jnp.array((1, 3)), direction=3)\n", "\n", "# taking move forward action\n", "move_forward_action = jnp.asarray(0)\n", "new_grid, new_agent, position = take_action(grid, agent, move_forward_action)\n", "\n", "show_img(render(grid, agent), dpi=64)\n", "show_img(render(new_grid, new_agent), dpi=64)"]}, {"cell_type": "markdown", "id": "08b6d5f8-350a-4a58-895d-64724331a53d", "metadata": {}, "source": ["As we described above, goals are functions (actually classes in code) that take some arguments and validates come conditions. For example, let's create `AgentNearGoal` that checks that agent is near some object (only horizontally and vertically, not diagonally). This goal requires us only to specify the tile and after that we can use it.\n", "\n", "For efficiency and compatibility with JAX, all goals (or rules) have vector representations that encode all the necessary information from which the goal (or rule) can be accurately recreated. First index is always an **ID** of the rule or goal. It is usually followed by arguments sequentially. \n", "\n", "Why is that? The main purpose is so that goals and rules arguments can be changed between resets and thus be dynamic. It is also important for us to be able to store them in a compact form during benchmark generation, as there could be millions of them. At the same time, we can decode them with `jax.lax.switch` (that what `check_goal` does) every time they are needed, which actually has a small overhead (from our limited testing). The alternative would be to first decode the whole million rulesets into classes when loading the benchmark, which would take a long time. And lastly, during training we will sample different rulesets inside the jit, which means that all shapes or pytree structures should stay the same, which is not true for different set of rulesets as classes. Encoding as a vector allows us to bring everything to the same shape.\n"]}, {"cell_type": "code", "execution_count": null, "id": "1705c39f-8a04-499c-a72c-58233df889a2", "metadata": {}, "outputs": [], "source": ["goal = AgentNearGoal(tile=TILES_REGISTRY[Tiles.BALL, Colors.YELLOW])\n", "goal_encoding = goal.encode()\n", "\n", "print(\"Goal:\", goal)\n", "print(\"Goal encoding:\", goal_encoding)\n", "print(\"Goal decoding:\", AgentNearGoal.decode(goal_encoding))"]}, {"cell_type": "markdown", "id": "e98acf31-2ea0-4979-807d-a2848bea7f3f", "metadata": {}, "source": ["Goals can be checked directly, but usually you will see `check_goal` in the codebase, as it works with encodings. But why does it accept such bizarre arguments? It is done, again, for efficiency. Many rules and goals don't need to be checked on every step, and most only need to be checked for any particular location that has changed in any way after the action. For example, `AgentNearGoal` can be checked only after the `move_forward` action."]}, {"cell_type": "code", "execution_count": null, "id": "8c60e493-4274-4803-bc03-bd804ce8f737", "metadata": {}, "outputs": [], "source": ["(\n", "    goal(new_grid, new_agent, move_forward_action, position) \\\n", "    == check_goal(goal_encoding, new_grid, new_agent, move_forward_action, position)\n", ")"]}, {"cell_type": "markdown", "id": "d4df2a3a-0a9c-4ec6-8dfe-66b2298d6ec6", "metadata": {}, "source": ["How to get the class instance from it's **ID** in the encoding? There seems to be only one path, and that's the one we're using:\n", "```python\n", "def check_goal(encoding, grid, agent, action, position):\n", "    check = jax.lax.switch(\n", "        encoding[0],\n", "        (\n", "            # empty goal first, we use them as paddings during benchmark generation\n", "            lambda: EmptyGoal.decode(encoding)(grid, agent, action, position),\n", "            lambda: AgentHoldGoal.decode(encoding)(grid, agent, action, position),\n", "            lambda: AgentOnTileGoal.decode(encoding)(grid, agent, action, position),\n", "            ...\n", "        ),\n", "    )\n", "    return check\n", "```\n"]}, {"cell_type": "markdown", "id": "ce696c1a-3d1b-47eb-a20b-a7869700e3a6", "metadata": {}, "source": ["All of the above is also true for the rules:"]}, {"cell_type": "code", "execution_count": null, "id": "462e932c-6130-4f7c-9aa5-172ce67aff57", "metadata": {}, "outputs": [], "source": ["rule = AgentNearRule(\n", "    tile=TILES_REGISTRY[Tiles.BALL, Colors.YELLOW], \n", "    prod_tile=TILES_REGISTRY[Tiles.SQUARE, Colors.PURPLE],\n", ")\n", "rule_encoding = rule.encode()\n", "\n", "print(\"Rule:\", rule)\n", "print(\"Rule encoding:\", rule_encoding)\n", "print(\"Rule decoding:\", AgentNearRule.decode(rule_encoding))"]}, {"cell_type": "code", "execution_count": null, "id": "bad0bb22-74fb-4d5d-881b-c223c10ada14", "metadata": {}, "outputs": [], "source": ["# or rule(new_grid, new_agent, move_forward_action, position)\n", "rule_grid, rule_agent = check_rule(rule_encoding[None, ...], new_grid, new_agent, move_forward_action, position)"]}, {"cell_type": "code", "execution_count": null, "id": "dc70397b-d37a-46af-bd67-76a1d618aba9", "metadata": {}, "outputs": [], "source": ["show_img(render(grid, agent), dpi=64)\n", "show_img(render(new_grid, new_agent), dpi=64)\n", "show_img(render(rule_grid, rule_agent), dpi=64)"]}, {"cell_type": "markdown", "id": "4ff08d13-a03c-40ee-8488-067bdd997bd9", "metadata": {}, "source": ["Rules are evaluated similar to the goals, except that is it can accept multiple rules encodings at once:\n", "\n", "```python\n", "def check_rule(encodings, grid, agent, action, position):\n", "    # check single rule\n", "    def _check(carry, encoding):\n", "        grid, agent = carry\n", "        grid, agent = jax.lax.switch(\n", "            encoding[0],\n", "            (\n", "                # empty rule first, we use them as paddings during benchmark generation\n", "                lambda: EmptyRule.decode(encoding)(grid, agent, action, position),\n", "                lambda: AgentHoldRule.decode(encoding)(grid, agent, action, position),\n", "                lambda: AgentNearRule.decode(encoding)(grid, agent, action, position),\n", "                lambda: TileNearRule.decode(encoding)(grid, agent, action, position),\n", "            ),\n", "        )\n", "        return (grid, agent), None\n", "\n", "    # check every rule in the ruleset\n", "    (grid, agent), _ = jax.lax.scan(_check, (grid, agent), encodings)\n", "\n", "    return grid, agent\n", "```\n", "\n", "**P.S.** We tested several designs and the current one proved to be the easiest and most efficient in terms of performance. However, it has significant limitations. For example, due to the monolithic nature of `jax.lax.switch` we cannot give an easy interface for customization and adding new goals and rules by users. We will continue to think of new approaches and would love to hear your suggestions!"]}, {"cell_type": "markdown", "id": "0bec8099-85d0-4888-adc8-045ec3586bfb", "metadata": {}, "source": ["## Benchmarks"]}, {"cell_type": "markdown", "id": "2de4e5f1-982b-48d3-8a1b-50b61a3d6364", "metadata": {}, "source": ["While composing rules and goals by hand is flexible, it can quickly become cumbersome. Besides, it's hard to express efficiently in a JAX-compatible way due to the high number of heterogeneous computations\n", "\n", "To avoid significant overhead during training and facilitate reliable comparisons between agents, we pre-sampled several benchmarks with up to **five million unique** tasks (apart from the randomization of object positions during reset), following the procedure used to train [DeepMind AdA](https://sites.google.com/view/adaptive-agent/) agent from the original XLand. These benchmarks differ in the generation configs, producing distributions with varying levels of diversity and average difficulty of the tasks. They can be used for different purposes, for example the `trivial-1m` benchmark can be used to debug your agents, allowing quick iterations. \n", "\n", "**Generation protocol**:\n", "\n", "<img src=\"https://i.ibb.co/pdWJ49S/presample.png\" align=\"center\" width=\"40%\">\n", "\n", "We provide the [script]() used to generate these benchmarks. Users can use it for their own purposes:\n", "\n", "```commandline\n", "python scripts/ruleset_generator.py --help\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "407fab86-7f7e-47c5-b2b7-93711f39c504", "metadata": {}, "outputs": [], "source": ["import os\n", "from xminigrid.types import RuleSet\n", "from xminigrid.benchmarks import Benchmark, load_benchmark, load_bz2_pickle, DATA_PATH, NAME2HFFILENAME\n", "from xminigrid.rendering.text_render import print_ruleset"]}, {"cell_type": "markdown", "id": "cb14acb8-1322-4ef9-a931-da6e32275d31", "metadata": {}, "source": ["Every task should be described with a goal, rules and initial objects. We call these **rulesets**:"]}, {"cell_type": "code", "execution_count": null, "id": "80dafd20-22a9-4bef-baff-fd91031c3126", "metadata": {}, "outputs": [], "source": ["goal = AgentNearGoal(tile=TILES_REGISTRY[Tiles.SQUARE, Colors.PURPLE])\n", "rule = AgentNearRule(\n", "    tile=TILES_REGISTRY[Tiles.BALL, Colors.YELLOW], \n", "    prod_tile=TILES_REGISTRY[Tiles.SQUARE, Colors.PURPLE],\n", ")\n", "\n", "ruleset = RuleSet(\n", "    goal=goal.encode(),\n", "    rules=rule.encode()[None, ...],\n", "    init_tiles=jnp.array((\n", "        TILES_REGISTRY[Tiles.BALL, Colors.YELLOW],\n", "    ))\n", ")\n", "print_ruleset(ruleset)"]}, {"cell_type": "markdown", "id": "27749537-97cf-45e1-8e53-5f4c7ec22517", "metadata": {}, "source": ["Rulesets can be freely combined with different variations of `XLand-MiniGrid` environment. Currently, these variations differ only in grid layouts. For example R1 has one room, while R9 has nine rooms."]}, {"cell_type": "code", "execution_count": null, "id": "136e5121-e10b-426a-9098-0b5157b3f7dc", "metadata": {}, "outputs": [], "source": ["xminigrid.registered_environments()[:15]"]}, {"cell_type": "code", "execution_count": null, "id": "d16d5319-ba1b-4fbd-b3e5-aa5530491881", "metadata": {}, "outputs": [], "source": ["env, env_params = xminigrid.make(\"XLand-MiniGrid-R4-9x9\")\n", "env_params = env_params.replace(ruleset=ruleset)\n", "\n", "timestep = env.reset(env_params, jax.random.PRNGKey(0))\n", "\n", "show_img(env.render(env_params, timestep), dpi=64)"]}, {"cell_type": "markdown", "id": "91416ad8-c513-497e-b3ca-d4ee327d4f46", "metadata": {}, "source": ["Pre-sampled benchmarks are hosted on [HuggingFace](https://huggingface.co/datasets/Howuhh/xland_minigrid/tree/main) and will be downloaded and cached on the first use."]}, {"cell_type": "code", "execution_count": null, "id": "c515af51-2e0a-4927-b029-88dcbf327f64", "metadata": {}, "outputs": [], "source": ["print(\"Benchmarks available:\", xminigrid.registered_benchmarks())\n", "\n", "benchmark = xminigrid.load_benchmark(name=\"trivial-1m\")\n", "print(\"Total rulesets:\", benchmark.num_rulesets())\n", "print(\"Ruleset with id 128: \\n\", benchmark.get_ruleset(ruleset_id=128))\n", "print(\"Random ruleset: \\n\", benchmark.sample_ruleset(jax.random.PRNGKey(0)))"]}, {"cell_type": "code", "execution_count": null, "id": "3a0a8049-8ea8-4e74-b7b6-901964612b3c", "metadata": {}, "outputs": [], "source": ["print_ruleset(benchmark.get_ruleset(ruleset_id=128))"]}, {"cell_type": "code", "execution_count": null, "id": "5150bedd-557d-474c-97ce-76346033a8de", "metadata": {}, "outputs": [], "source": ["env_params = env_params.replace(ruleset=benchmark.get_ruleset(ruleset_id=128))\n", "timestep = env.reset(env_params, jax.random.PRNGKey(0))\n", "\n", "show_img(env.render(env_params, timestep), dpi=64)"]}, {"cell_type": "markdown", "id": "24d222de-fbc9-47c1-ba90-d0e6de849af7", "metadata": {}, "source": ["Uses can generate benchmarks yourself with the help of `scripts/ruleset_generator.py`. They can be futher loaded to the benchmark as follows:"]}, {"cell_type": "code", "execution_count": null, "id": "7264889d-9dfc-44b8-97bf-e3fa1ecc7735", "metadata": {}, "outputs": [], "source": ["# example path, can be any your valid path\n", "bechmark_path = os.path.join(DATA_PATH, NAME2HFFILENAME[\"trivial-1m\"])\n", "\n", "rulesets_clear = load_bz2_pickle(bechmark_path)\n", "loaded_benchmark = Benchmark(\n", "    goals=rulesets_clear[\"goals\"],\n", "    rules=rulesets_clear[\"rules\"],\n", "    init_tiles=rulesets_clear[\"init_tiles\"],\n", "    num_rules=rulesets_clear[\"num_rules\"]\n", ")"]}, {"cell_type": "markdown", "id": "0a08d28a-60fd-4a09-acb2-7e4d42538b6e", "metadata": {}, "source": ["You also my need splitting functionality to test generalization of your agents. For this users can use `split` or `filter_split`:"]}, {"cell_type": "code", "execution_count": null, "id": "653b678b-65c1-47a8-9644-4f2d519d9e55", "metadata": {}, "outputs": [], "source": ["train, test = benchmark.shuffle(key=jax.random.PRNGKey(0)).split(prop=0.8)\n", "\n", "# or, by some function:\n", "def cond_fn(goal, rules):\n", "    # 0 index in the encoding is the ID\n", "    return jnp.logical_not(\n", "        jnp.logical_and(\n", "            jnp.greater_equal(goal[0], 7),\n", "            jnp.less_equal(goal[0], 14)\n", "        )\n", "    )\n", "    \n", "train, test = benchmark.filter_split(fn=cond_fn)"]}, {"cell_type": "markdown", "id": "4786beac-9bea-4bbc-bcf9-92c21b5770d2", "metadata": {}, "source": ["### Vectorization"]}, {"cell_type": "markdown", "id": "55db41eb-ea76-4e5a-82c6-42afce4332f6", "metadata": {}, "source": ["For meta-RL users can sample multiple rulesets, vectorizing step and reset methods to work with whole distribution of tasks:"]}, {"cell_type": "code", "execution_count": null, "id": "133ed460-9112-4761-bb3f-5c8b6ea485cf", "metadata": {}, "outputs": [], "source": ["rulesets = jax.vmap(benchmark.get_ruleset)(jnp.arange(100))\n", "jtu.tree_map(jnp.shape, rulesets)"]}, {"cell_type": "code", "execution_count": null, "id": "72d2a71f-7a0b-413e-843a-a4e4aa4b3d90", "metadata": {}, "outputs": [], "source": ["env_params = env_params.replace(ruleset=rulesets)\n", "timestep = jax.vmap(env.reset, in_axes=(0, None))(env_params, jax.random.PRNGKey(0))"]}, {"cell_type": "code", "execution_count": null, "id": "22705a0a-4f47-478e-8953-6d9b91adccae", "metadata": {"scrolled": true}, "outputs": [], "source": ["timestep.state.goal_encoding"]}, {"cell_type": "markdown", "id": "a0b55667-d2c3-4b5c-b074-d98c0b14abc9", "metadata": {}, "source": ["As we vmapped only on rulesets and not the keys, initial positions for agent and objects are the same. In practice, we also vmap on keys to randomize their positions too."]}, {"cell_type": "code", "execution_count": null, "id": "c66a196a-5344-49d2-b819-b355d5e76855", "metadata": {}, "outputs": [], "source": ["ruleset1_img = env.render(\n", "    jtu.tree_map(lambda x: x[5], env_params), \n", "    jtu.tree_map(lambda x: x[5], timestep)\n", ")\n", "ruleset2_img = env.render(\n", "    jtu.tree_map(lambda x: x[9], env_params), \n", "    jtu.tree_map(lambda x: x[9], timestep)\n", ")\n", "\n", "show_img(ruleset1_img, dpi=64)\n", "show_img(ruleset2_img, dpi=64)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}