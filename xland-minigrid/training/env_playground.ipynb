{"cells": [{"cell_type": "code", "execution_count": 2, "id": "7a7def4c-5f1f-4798-8c82-702a6d516fe1", "metadata": {}, "outputs": [], "source": ["import os\n", "# os.environ['JAX_PLATFORM_NAME'] = 'cpu'"]}, {"cell_type": "code", "execution_count": 1, "id": "a270d535-9421-4eea-a8db-372fd6ab3dc8", "metadata": {}, "outputs": [], "source": ["import jax\n", "from jax import numpy as jnp"]}, {"cell_type": "code", "execution_count": 3, "id": "07824f3c-4bb5-47cf-a72c-fd4ed97f2166", "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.insert(0, 'src')"]}, {"cell_type": "code", "execution_count": 4, "id": "cbe504e0-b3af-464b-849f-e72aceb7e3ff", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/network/scratch/a/artem.zholus/conda_envs/lg/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import imageio\n", "import jax\n", "import jax.numpy as jnp\n", "import jmp\n", "import orbax.checkpoint\n", "import xminigrid\n", "from nn import ActorCriticRNN\n", "from xminigrid.rendering.text_render import print_ruleset\n", "from xminigrid.wrappers import GymAutoResetWrapper\n", "\n", "# utils for the demonstation\n", "from xminigrid.core.grid import room\n", "from xminigrid.types import AgentState\n", "from xminigrid.core.actions import take_action\n", "from xminigrid.core.constants import Tiles, Colors, TILES_REGISTRY\n", "from xminigrid.rendering.rgb_render import render\n", "\n", "# rules and goals\n", "from xminigrid.core.goals import check_goal, AgentNearGoal\n", "from xminigrid.core.rules import check_rule, AgentNearRule\n", "import timeit\n", "import imageio\n", "import matplotlib.pyplot as plt\n", "from tqdm.auto import trange, tqdm\n", "\n", "def show_img(img, dpi=32):\n", "    plt.figure(dpi=dpi)\n", "    plt.axis('off')\n", "    plt.imshow(img)\n"]}, {"cell_type": "code", "execution_count": 6, "id": "190bc8c6-3f6c-40e6-a3d9-0f2890486494", "metadata": {}, "outputs": [], "source": ["# orbax_checkpointer = orbax.checkpoint.PyTreeCheckpointer()\n", "# checkpoint = orbax_checkpointer.restore(\"/network/scratch/a/artem.zholus/language_policies/1/checkpoint_0\")\n", "# config = checkpoint[\"config\"]\n", "# params = checkpoint['params']\n", "\n", "env, env_params = xminigrid.make(\"XLand-MiniGrid-R9-25x25\")\n", "env = GymAutoResetWrapper(env)"]}, {"cell_type": "code", "execution_count": 7, "id": "196a3940-d8a8-4767-8cb2-d2ba99580174", "metadata": {}, "outputs": [], "source": ["import jax.random\n", "import xminigrid\n", "from xminigrid.benchmarks import Benchmark\n", "benchmark: Benchmark = xminigrid.load_benchmark(name=\"high-3m\")"]}, {"cell_type": "code", "execution_count": 8, "id": "715125a5-246a-40d0-9cea-959400e031d6", "metadata": {}, "outputs": [], "source": ["def build_rollout(env, env_params, num_steps):\n", "    def rollout(rng):\n", "        def _step_fn(carry, _):\n", "            rng, timestep = carry\n", "            rng, _rng = jax.random.split(rng)\n", "            action = jax.random.randint(_rng, shape=(), minval=0, maxval=env.num_actions(env_params))\n", "            \n", "            timestep = env.step(env_params, timestep, action)\n", "            return (rng, timestep), timestep\n", "    \n", "        rng, _rng = jax.random.split(rng)\n", "    \n", "        timestep = env.reset(env_params, _rng)\n", "        rng, transitions = jax.lax.scan(_step_fn, (rng, timestep), None, length=num_steps)\n", "        return transitions\n", "\n", "    return rollout\n"]}, {"cell_type": "code", "execution_count": 9, "id": "169cd4c6-63f0-450b-8e76-8ddb593c91fb", "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.image.AxesImage at 0x7f0e745c7340>"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["benchmark.sample_ruleset(jax.random.key(0))\n", "ruleset = benchmark.get_ruleset(ruleset_id=11)\n", "env_params = env_params.replace(ruleset=ruleset)\n", "timestep = env.reset(env_params, jax.random.key(0))\n", "plt.imshow(env.render(env_params, timestep))"]}, {"cell_type": "code", "execution_count": 30, "id": "80af2cbb-94ae-407b-918a-4377bb59a58d", "metadata": {}, "outputs": [{"data": {"text/plain": ["(<PERSON><PERSON><PERSON>([15, 23], dtype=int32), <PERSON><PERSON><PERSON>([ 3, 19], dtype=int32))"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["(timestep.state.grid[..., 0] == Tiles.SQUARE).nonzero()"]}, {"cell_type": "code", "execution_count": 10, "id": "a6141a39-e8b2-4e97-b62a-bd3eefb2349c", "metadata": {}, "outputs": [], "source": ["def randomly_lock_doors(rng, grid):\n", "    door_mask = grid[..., 0] == Tiles.DOOR_CLOSED\n", "    key_mask = grid[..., 0] == Tiles.KEY\n", "    for color in [Colors.RED,\n", "              Colors.GREEN,\n", "              Colors.BLUE,\n", "              Colors.PURPLE,\n", "              Colors.YELLOW,\n", "              Colors.GREY,\n", "              Colors.BLACK,\n", "              Colors.ORANGE,\n", "              Colors.WHITE,\n", "              Colors.BROWN,\n", "              Colors.PINK]:\n", "        color_mask = grid[..., 1] == color\n", "        has_key_for_door = (color_mask & key_mask).any() & (color_mask & door_mask)\n", "        # randomly close doors\n", "        rng, curr_rng = jax.random.split(rng, 2)\n", "        # uniformly choosing\n", "        random_mask = jax.random.randint(curr_rng, timestep.state.grid[..., 0].shape, 0, 2) == 1\n", "        random_locked_closed = jnp.where(random_mask, Tiles.DOOR_LOCKED, Tiles.DOOR_CLOSED)\n", "        # if we have a certain key of a certain color - we randomly decide to close the door or leave it open. \n", "        # otherwise we leave it open\n", "        new_grid = jnp.where(has_key_for_door, random_locked_closed, grid[..., 0])\n", "        grid = jnp.stack([new_grid, grid[..., 1]], axis=-1)\n", "    return rng, grid\n", "def add_more_keys(rng, tiles, max_num_keys=6):\n", "    key_tiles = tiles.at[:, 0].set(Tiles.KEY)\n", "    rng, curr_key = jax.random.split(rng, 2)\n", "    key_colors = key_tiles.at[:, 1].set(jax.random.randint(curr_key, (key_tiles.shape[0],), 1, 12)) # colors are ints\n", "    tiles_with_keys = jnp.where(tiles != 0, tiles, key_colors)\n", "    rng, curr_key = jax.random.split(rng, 2)\n", "    total = (tiles != 0).all(1).sum()\n", "    max_objs = jax.random.randint(curr_key, (1,), total, total + max_num_keys)\n", "    return rng, jnp.where((jnp.arange(tiles.shape[0]) <= max_objs)[:, None], tiles_with_keys, jnp.zeros_like(tiles))\n"]}, {"cell_type": "code", "execution_count": 61, "id": "ec640a8b-994c-426d-a3bb-8979e535dbf1", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/network/scratch/a/artem.zholus/conda_envs/lg/lib/python3.10/site-packages/jax/_src/ops/scatter.py:96: FutureWarning: scatter inputs have incompatible types: cannot safely cast value from dtype=int32 to dtype=uint8 with jax_numpy_dtype_promotion='standard'. In future JAX releases this will result in an error.\n", "  warnings.warn(\n"]}, {"data": {"text/plain": ["<matplotlib.image.AxesImage at 0x7f947027b5e0>"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ruleset = benchmark.get_ruleset(ruleset_id=14)\n", "rng = jax.random.key(8)\n", "rng, new_tiles = add_more_keys(rng, ruleset.init_tiles, max_num_keys=10)\n", "ruleset = ruleset.replace(init_tiles=new_tiles)\n", "env_params = env_params.replace(ruleset=ruleset)\n", "rng, subkey = jax.random.split(rng, 2)\n", "timestep = env.reset(env_params, subkey)\n", "rng, new_grid = randomly_lock_doors(rng, timestep.state.grid)\n", "\n", "ruleset = ruleset.replace(rules=ruleset.rules[-1:])\n", "objects = jnp.stack((timestep.state.grid[..., 0] > 4).nonzero()).T\n", "rng_, subkey = jax.random.split(rng, 2)\n", "new_goal = ruleset.goal.at[0].set(2).at[1:3].set(jax.random.choice(subkey, objects))\n", "ruleset = ruleset.replace(goal=new_goal)\n", "\n", "timestep = timestep.replace(state=timestep.state.replace(grid=new_grid))\n", "# timestep.state.grid = new_grid\n", "plt.imshow(env.render(env_params, timestep))"]}, {"cell_type": "code", "execution_count": 55, "id": "c3fd06fe-ef1d-483c-8c2f-fa58b87d0d96", "metadata": {}, "outputs": [{"data": {"text/plain": ["RuleSet(goal=Array([ 2, 15,  3,  0,  0], dtype=int32), rules=Array([[0, 0, 0, 0, 0, 0, 0]], dtype=uint8), init_tiles=Array([[11, 10],\n", "       [ 6, 10],\n", "       [ 4,  2],\n", "       [ 3,  4],\n", "       [ 7,  5],\n", "       [ 7,  5],\n", "       [12,  8],\n", "       [ 7,  7],\n", "       [ 7, 10],\n", "       [ 7,  9],\n", "       [ 7, 11],\n", "       [ 7,  5],\n", "       [ 7,  8],\n", "       [ 7,  9],\n", "       [ 7,  6],\n", "       [ 7,  1],\n", "       [ 7,  6],\n", "       [ 0,  0],\n", "       [ 0,  0],\n", "       [ 0,  0],\n", "       [ 0,  0],\n", "       [ 0,  0],\n", "       [ 0,  0]], dtype=uint8))"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["ruleset = ruleset.replace(rules=ruleset.rules[-1:])\n", "objects = jnp.stack((timestep.state.grid[..., 0] > 4).nonzero()).T\n", "rng_, subkey = jax.random.split(rng, 2)\n", "new_goal = ruleset.goal.at[0].set(2).at[1:3].set(jax.random.choice(subkey, objects))\n", "ruleset = ruleset.replace(goal=new_goal)\n", "ruleset"]}, {"cell_type": "code", "execution_count": null, "id": "4576b082-368c-46aa-adeb-2b2422c34e3c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 154, "id": "a794de55-7ce9-4844-8793-a4c57acc1635", "metadata": {}, "outputs": [], "source": ["rng_ = jax.random.key(2)"]}, {"cell_type": "code", "execution_count": 198, "id": "8db455b0-ae52-49a8-88d1-7dbd353d06ab", "metadata": {}, "outputs": [], "source": ["timestep.state.grid.shape\n", "rng = rng_\n", "door_mask = timestep.state.grid[..., 0] == Tiles.DOOR_CLOSED\n", "key_mask = timestep.state.grid[..., 0] == Tiles.KEY\n", "color = Colors.PURPLE\n", "# for color in range(12): # TODO: change this to enumeration\n", "if True:\n", "    color_mask = timestep.state.grid[..., 1] == color\n", "    has_key_for_door = (color_mask & key_mask).any() & (color_mask & door_mask)\n", "    # randomly close doors\n", "    rng, curr_rng = jax.random.split(rng, 2)\n", "    # uniformly choosing\n", "    random_mask = jax.random.randint(curr_rng, timestep.state.grid[..., 0].shape, 0, 2) == 1\n", "    random_open_closed = jnp.where(random_mask, Tiles.DOOR_LOCKED, Tiles.DOOR_CLOSED)\n", "    # if we have a certain key of a certain color - we randomly decide to close the door or leave it open. \n", "    # otherwise we leave it open\n", "    new_grid = jnp.where(has_key_for_door, random_open_closed, timestep.state.grid[..., 0])\n", "new_grid = jnp.stack([new_grid, timestep.state.grid[..., 1]], axis=-1)"]}, {"cell_type": "code", "execution_count": 19, "id": "18345960-9a4c-4287-9ab4-9861b6e74279", "metadata": {}, "outputs": [], "source": ["# utils for the demonstation\n", "from xminigrid.core.grid import room\n", "from xminigrid.types import AgentState\n", "from xminigrid.core.actions import take_action\n", "from xminigrid.core.constants import Tiles, Colors, TILES_REGISTRY\n", "from xminigrid.rendering.rgb_render import render\n", "\n", "# rules and goals\n", "from xminigrid.core.goals import check_goal, AgentNearGoal\n", "from xminigrid.core.rules import check_rule, AgentNearRule\n", "import timeit\n", "import imageio\n", "import matplotlib.pyplot as plt\n", "from tqdm.auto import trange, tqdm\n", "\n", "def show_img(img, dpi=32):\n", "    plt.figure(dpi=dpi)\n", "    plt.axis('off')\n", "    plt.imshow(img)"]}, {"cell_type": "code", "execution_count": 158, "id": "7d365b8f-6e51-41f0-99a6-ee2d0151b22a", "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.image.AxesImage at 0x7feecc6db190>"]}, "execution_count": 158, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.imshow(random_open_closed)"]}, {"cell_type": "code", "execution_count": 193, "id": "5a79bb7a-d8b8-4890-9e75-cc24459d3bdd", "metadata": {}, "outputs": [{"data": {"text/plain": ["(25, 25, 2)"]}, "execution_count": 193, "metadata": {}, "output_type": "execute_result"}], "source": ["timestep.state.grid.shape"]}, {"cell_type": "code", "execution_count": 20, "id": "d161a299-5f42-4b8a-9b4d-6ac1fed8db55", "metadata": {}, "outputs": [], "source": ["def randomly_lock_doors(rng, grid):\n", "    door_mask = grid[..., 0] == Tiles.DOOR_CLOSED\n", "    key_mask = grid[..., 0] == Tiles.KEY\n", "    for color in [Colors.RED,\n", "              Colors.GREEN,\n", "              Colors.BLUE,\n", "              Colors.PURPLE,\n", "              Colors.YELLOW,\n", "              Colors.GREY,\n", "              Colors.BLACK,\n", "              Colors.ORANGE,\n", "              Colors.WHITE,\n", "              Colors.BROWN,\n", "              Colors.PINK]:\n", "        color_mask = grid[..., 1] == color\n", "        has_key_for_door = (color_mask & key_mask).any() & (color_mask & door_mask)\n", "        # randomly close doors\n", "        rng, curr_rng = jax.random.split(rng, 2)\n", "        # uniformly choosing\n", "        random_mask = jax.random.randint(curr_rng, timestep.state.grid[..., 0].shape, 0, 2) == 1\n", "        random_locked_closed = jnp.where(random_mask, Tiles.DOOR_LOCKED, Tiles.DOOR_CLOSED)\n", "        # if we have a certain key of a certain color - we randomly decide to close the door or leave it open. \n", "        # otherwise we leave it open\n", "        new_grid = jnp.where(has_key_for_door, random_locked_closed, grid[..., 0])\n", "        grid = jnp.stack([new_grid, grid[..., 1]], axis=-1)\n", "    return rng, grid\n", "def add_more_keys(rng, tiles, max_num_keys=6):\n", "    key_tiles = tiles.at[:, 0].set(Tiles.KEY)\n", "    rng, curr_key = jax.random.split(rng, 2)\n", "    key_colors = key_tiles.at[:, 1].set(jax.random.randint(curr_key, (key_tiles.shape[0],), 1, 12)) # colors are ints\n", "    tiles_with_keys = jnp.where(tiles != 0, tiles, key_colors)\n", "    rng, curr_key = jax.random.split(rng, 2)\n", "    total = (tiles != 0).all(1).sum()\n", "    max_objs = jax.random.randint(curr_key, (1,), total, total + max_num_keys)\n", "    return rng, jnp.where((jnp.arange(tiles.shape[0]) <= max_objs)[:, None], tiles_with_keys, jnp.zeros_like(tiles))"]}, {"cell_type": "code", "execution_count": 21, "id": "5aa076f9-d8c7-442f-903c-c01961bb4116", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/network/scratch/a/artem.zholus/conda_envs/lg/lib/python3.10/site-packages/jax/_src/ops/scatter.py:96: FutureWarning: scatter inputs have incompatible types: cannot safely cast value from dtype=int32 to dtype=uint8 with jax_numpy_dtype_promotion='standard'. In future JAX releases this will result in an error.\n", "  warnings.warn(\n"]}, {"data": {"text/plain": ["<matplotlib.image.AxesImage at 0x7f1810519030>"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ruleset = benchmark.get_ruleset(ruleset_id=13)\n", "rng = jax.random.key(5)\n", "rng, new_tiles = add_more_keys(rng, ruleset.init_tiles, max_num_keys=10)\n", "ruleset = ruleset.replace(init_tiles=new_tiles)\n", "env_params = env_params.replace(ruleset=ruleset)\n", "timestep = env.reset(env_params, jax.random.key(0))\n", "rng, new_grid = randomly_lock_doors(rng, timestep.state.grid)\n", "timestep = timestep.replace(state=timestep.state.replace(grid=new_grid))\n", "# timestep.state.grid = new_grid\n", "plt.imshow(env.render(env_params, timestep))"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}