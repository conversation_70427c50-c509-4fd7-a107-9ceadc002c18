# utilities for PPO training and evaluation
import jax
import jmp
import jax.numpy as jnp
from flax import struct
from flax.training.train_state import TrainState
from xminigrid.environment import Environment, EnvParams


# Training stuff
class Transition(struct.PyTreeNode):
    done: jax.Array
    action: jax.Array
    value: jax.Array
    reward: jax.Array
    goal: jax.Array
    log_prob: jax.Array
    obs: jax.Array
    # for rnn policy
    prev_action: jax.Array
    prev_reward: jax.Array


def calculate_gae(
    transitions: Transition,
    last_val: jax.Array,
    gamma: float,
    gae_lambda: float,
) -> tuple[jax.Array, jax.Array]:
    # single iteration for the loop
    def _get_advantages(gae_and_next_value, transition):
        gae, next_value = gae_and_next_value
        delta = transition.reward + gamma * next_value * (1 - transition.done) - transition.value
        gae = delta + gamma * gae_lambda * (1 - transition.done) * gae
        return (gae, transition.value), gae

    _, advantages = jax.lax.scan(
        _get_advantages,
        (jnp.zeros_like(last_val), last_val),
        transitions,
        reverse=True,
    )
    # advantages and values (Q)
    return advantages, advantages + transitions.value


def ppo_update_networks(
    train_state: TrainState,
    transitions: Transition,
    init_hstate: jax.Array,
    advantages: jax.Array,
    targets: jax.Array,
    clip_eps: float,
    vf_coef: float,
    ent_coef: float,
    amp_policy: jmp.Policy,
    loss_scale: jmp.LossScale,
    goals: jax.Array,
    rng: jax.Array
):
    # NORMALIZE ADVANTAGES
    advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)

    def _loss_fn(params):
        # RERUN NETWORK
        ps = amp_policy.cast_to_param(
            params
        )
        inputs, hidden = amp_policy.cast_to_compute(
            ({
                # [batch_size, seq_len, ...]
                "observation": transitions.obs,
                "prev_action": transitions.prev_action,
                "prev_reward": transitions.prev_reward,
                "goal": transitions.goal
            },
            init_hstate),
        )
        dist, value, _ = train_state.apply_fn(
            ps, inputs, hidden    
        )
        full_policy = amp_policy.with_output_dtype(jnp.float32)
        dist, value = full_policy.cast_to_output((dist, value))
        log_prob = dist.log_prob(transitions.action)

        # CALCULATE VALUE LOSS
        value_pred_clipped = transitions.value + (value - transitions.value).clip(-clip_eps, clip_eps)
        value_loss = jnp.square(value - targets)
        value_loss_clipped = jnp.square(value_pred_clipped - targets)
        value_loss = 0.5 * jnp.maximum(value_loss, value_loss_clipped).mean()
        # TODO: ablate this!
        # value_loss = jnp.square(value - targets).mean()

        # CALCULATE ACTOR LOSS
        ratio = jnp.exp(log_prob - transitions.log_prob)
        actor_loss1 = advantages * ratio
        actor_loss2 = advantages * jnp.clip(ratio, 1.0 - clip_eps, 1.0 + clip_eps)
        actor_loss = -jnp.minimum(actor_loss1, actor_loss2).mean()
        entropy = dist.entropy().mean()

        total_loss = actor_loss + vf_coef * value_loss - ent_coef * entropy
        total_loss = loss_scale.scale(total_loss) if loss_scale is not None else total_loss
        return total_loss, (value_loss, actor_loss, entropy)

    (loss, (vloss, aloss, entropy)), grads = jax.value_and_grad(_loss_fn, has_aux=True)(train_state.params)
    (loss, vloss, aloss, entropy, grads) = jax.lax.pmean((loss, vloss, aloss, entropy, grads), axis_name="devices")
    grads = loss_scale.unscale(grads) if loss_scale is not None else grads
    grads_flat, _ = jax.tree_util.tree_flatten(grads)
    global_l2 = jnp.sqrt(sum([jnp.vdot(p, p) for p in grads_flat]))
    train_state = train_state.apply_gradients(grads=grads)
    update_info = {
        "total_loss": loss_scale.unscale(loss) if loss_scale is not None else loss,
        "value_loss": vloss,
        "actor_loss": aloss,
        "entropy": entropy,
        "grad_norm": global_l2
    }
    return train_state, update_info


# for evaluation (evaluate for N consecutive episodes, sum rewards)
# N=1 single task, N>1 for meta-RL
class RolloutStats(struct.PyTreeNode):
    reward: jax.Array = jnp.asarray(0.0)
    length: jax.Array = jnp.asarray(0)
    dones: jax.Array = jnp.asarray(0.0)
    episodes: jax.Array = jnp.asarray(0)


def rollout(
    rng: jax.Array,
    env: Environment,
    env_params: EnvParams,
    train_state: TrainState,
    init_hstate: jax.Array,
    goals: jax.Array,
    num_consecutive_episodes: int = 1,
    amp_policy: jmp.Policy = None,
) -> RolloutStats:
    def _cond_fn(carry):
        rng, stats, timestep, prev_action, prev_reward, hstate = carry
        return jnp.less(stats.episodes, num_consecutive_episodes)

    def _body_fn(carry):
        rng, stats, timestep, prev_action, prev_reward, hstate = carry

        rng, _rng = jax.random.split(rng)
        # g = timestep.state.goal_encoding
        # goal_vec = jnp.eye(16)[g].reshape(-1)
        # goal_vec = timestep.state.goal_vec
        o, c = timestep.state.goal_encoding[..., 1], timestep.state.goal_encoding[..., 2]
        idx = jax.random.randint(_rng, (), 0, 19)
        goal_vec = goals[o, c, idx]
        params = amp_policy.cast_to_param(train_state.params)
        inputs, hidden = amp_policy.cast_to_compute((
            {
                "observation": timestep.observation[None, None, ...],
                "prev_action": prev_action[None, None, ...],
                "prev_reward": prev_reward[None, None, ...],
                "goal": goal_vec[None, None]
            },
            hstate,
        ))
        dist, _, hstate = train_state.apply_fn(
            params, inputs, hidden
        )
        action = dist.sample(seed=_rng).squeeze()
        timestep = env.step(env_params, timestep, action)

        stats = stats.replace(
            reward=stats.reward + timestep.reward,
            length=stats.length + 1,
            dones=stats.dones + (1 - timestep.discount),
            episodes=stats.episodes + timestep.last(),
        )
        carry = (rng, stats, timestep, action, timestep.reward, hstate)
        return carry

    timestep = env.reset(env_params, rng)
    prev_action = jnp.asarray(0)
    prev_reward = jnp.asarray(0)
    init_hstate = amp_policy.cast_to_compute(init_hstate)
    init_carry = (rng, RolloutStats(), timestep, prev_action, prev_reward, init_hstate)

    final_carry = jax.lax.while_loop(_cond_fn, _body_fn, init_val=init_carry)
    return final_carry[1]
