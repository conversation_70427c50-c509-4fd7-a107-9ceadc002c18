# Adapted from PureJaxRL implementation and minigrid baselines, source:
# https://github.com/lupuandr/explainable-policies/blob/50acbd777dc7c6d6b8b7255cd1249e81715bcb54/purejaxrl/ppo_rnn.py#L4
# https://github.com/lcswillems/rl-starter-files/blob/master/model.py
import os
import sys
sys.path.insert(0, 'src')
import shutil
import time
from dataclasses import asdict, dataclass
from functools import partial
from typing import Optional

import jax
import jax.numpy as jnp
import numpy as np
import jmp
import jax.tree_util as jtu
import orbax.checkpoint as ocp
import optax
import orbax
import pyrallis
import wandb
import xminigrid
from flax.jax_utils import replicate, unreplicate
from flax.training import orbax_utils, checkpoints
from flax.training.train_state import TrainState
from nn import ActorCriticRNN
from utils import Transition, calculate_gae, ppo_update_networks, rollout
from xminigrid.benchmarks import Benchmark
from xminigrid.environment import Environment, EnvParams
from xminigrid.wrappers import GymAutoResetWrapper

# this will be default in new jax versions anyway
jax.config.update("jax_threefry_partitionable", True)


@dataclass
class TrainConfig:
    project: str = "xminigrid"
    group: str = "default"
    name: str = "meta-task-ppo"
    env_id: str = "XLand-MiniGrid-R4-9x9"
    benchmark_id: str = "my_trivial_1m_v2"
    # agent
    action_emb_dim: int = 128
    rnn_hidden_dim: int = 512
    rnn_num_layers: int = 1
    head_hidden_dim: int = 512
    cnn: str = 'minigrid' # 'minigrid' | 'impala'
    half_precision: bool = False
    # training
    num_envs: int = 32768
    num_steps_per_env: int = 256
    num_steps_per_update: int = 256
    update_epochs: int = 1
    num_minibatches: int = 32
    total_timesteps: int = 100_000_000_000
    lr: float = 0.001
    clip_eps: float = 0.2
    gamma: float = 0.99
    gae_lambda: float = 0.95
    ent_coef: float = 0.01
    vf_coef: float = 0.5
    max_grad_norm: float = 0.5
    eval_num_envs: int = 512
    eval_num_episodes: int = 10
    eval_seed: int = 42
    train_seed: int = 42
    checkpoint_path: Optional[str] = None

    def __post_init__(self):
        num_devices = jax.local_device_count()
        # splitting computation across all available devices
        self.num_envs_per_device = self.num_envs // num_devices
        self.total_timesteps_per_device = self.total_timesteps // num_devices
        self.eval_num_envs_per_device = self.eval_num_envs // num_devices
        assert self.num_envs % num_devices == 0
        self.num_meta_updates = round(
            self.total_timesteps_per_device / (self.num_envs_per_device * self.num_steps_per_env)
        )
        self.num_inner_updates = self.num_steps_per_env // self.num_steps_per_update
        assert self.num_steps_per_env % self.num_steps_per_update == 0
        print(f"Num devices: {num_devices}, Num meta updates: {self.num_meta_updates}")


def my_load_benchmark(name: str) -> Benchmark:
    from xminigrid.benchmarks import load_bz2_pickle
    benchmark_dict = load_bz2_pickle(name)
    # goals = np.load('/network/scratch/a/artem.zholus/bert_features_v2.npy')
    goals = np.load('/network/scratch/a/artem.zholus/bert_features_rephrase.npy')
    benchmark = Benchmark(
        goals=benchmark_dict["goals"],
        rules=benchmark_dict["rules"],
        init_tiles=benchmark_dict["init_tiles"],
        num_rules=benchmark_dict["num_rules"],
        # goal_vecs=goals
    )
    return benchmark, goals

def make_states(config: TrainConfig):
    # for learning rage scheduling
    def linear_schedule(count):
        total_inner_updates = config.num_minibatches * config.update_epochs * config.num_inner_updates
        frac = 1.0 - (count // total_inner_updates) / config.num_meta_updates
        return config.lr * frac

    # setup environment
    if "XLand" not in config.env_id:
        raise ValueError("Only meta-task environments are supported.")

    env, env_params = xminigrid.make(config.env_id)
    env = GymAutoResetWrapper(env)

    # loading benchmark
    benchmark, goals = my_load_benchmark(config.benchmark_id)

    # set up training state
    rng = jax.random.PRNGKey(config.train_seed)
    rng, _rng = jax.random.split(rng)

    policy = jmp.get_policy(
        'params=float32,compute=float16,output=float16'
        if config.half_precision else
        'params=float32,compute=float32,output=float32'
    )
    dtype = jnp.float16 if config.half_precision else jnp.float32
    loss_scale = (
        jmp.DynamicLossScale(jmp.half_dtype()(2 ** 15))
        if config.half_precision else
        None
    )
    network = ActorCriticRNN(
        num_actions=env.num_actions(env_params),
        action_emb_dim=config.action_emb_dim,
        rnn_hidden_dim=config.rnn_hidden_dim,
        rnn_num_layers=config.rnn_num_layers,
        head_hidden_dim=config.head_hidden_dim,
        cnn=config.cnn,
        amp_policy=policy,
        dtype=dtype
    )
    # [batch_size, seq_len, ...]
    init_obs = {
        "observation": jnp.zeros((config.num_envs_per_device, 1, *env.observation_shape(env_params)), dtype=dtype),
        "prev_action": jnp.zeros((config.num_envs_per_device, 1), dtype=jnp.int32),
        "prev_reward": jnp.zeros((config.num_envs_per_device, 1), dtype=dtype),
        "goal": jnp.zeros((config.num_envs_per_device, 1, 768), dtype=dtype),
    }
    init_hstate = network.initialize_carry(batch_size=config.num_envs_per_device).astype(dtype)

    network_params = network.init(_rng, init_obs, init_hstate)
    tx = optax.chain(
        optax.clip_by_global_norm(config.max_grad_norm),
        optax.inject_hyperparams(optax.adam)(learning_rate=linear_schedule, eps=1e-8),  # eps=1e-5
    )
    
    train_state = TrainState.create(
        apply_fn=network.apply, params=network_params, tx=tx,
    )
    if os.path.exists(
        os.path.join(os.path.abspath(config.checkpoint_path), 'checkpoint_0')
    ):    
        train_state = checkpoints.restore_checkpoint(ckpt_dir=os.path.abspath(config.checkpoint_path),
                                target=train_state, orbax_transforms={},
                                step=0)

    return rng, env, env_params, benchmark, init_hstate, train_state, policy, loss_scale, jnp.array(goals)


def make_train(
    env: Environment,
    env_params: EnvParams,
    benchmark: Benchmark,
    config: TrainConfig,
    policy: jmp.Policy,
    loss_scale: jmp.LossScale, 
):
    @partial(jax.pmap, axis_name="devices")
    def train(
        rng: jax.Array,
        train_state: TrainState,
        init_hstate: jax.Array,
        goals: jax.Array
    ):
        # META TRAIN LOOP
        def _meta_step(meta_state):
            rng, train_state = meta_state

            # INIT ENV
            rng, _rng1, _rng2 = jax.random.split(rng, num=3)
            ruleset_rng = jax.random.split(rng, num=config.num_envs_per_device)
            reset_rng = jax.random.split(rng, num=config.num_envs_per_device)

            # sample rulesets for this meta update
            rulesets = jax.vmap(benchmark.sample_ruleset)(ruleset_rng)
            meta_env_params = env_params.replace(ruleset=rulesets)

            timestep = jax.vmap(env.reset, in_axes=(0, 0))(meta_env_params, reset_rng)
            prev_action = jnp.zeros(config.num_envs_per_device, dtype=jnp.int32)
            prev_reward = jnp.zeros(config.num_envs_per_device)

            # INNER TRAIN LOOP
            def _update_step(runner_state, _):
                # COLLECT TRAJECTORIES
                def _env_step(runner_state, _):
                    rng, train_state, prev_timestep, prev_action, prev_reward, prev_hstate = runner_state

                    # SELECT ACTION
                    rng, _rng = jax.random.split(rng)
                    o, c = prev_timestep.state.goal_encoding[:, 1], prev_timestep.state.goal_encoding[:, 2]
                    idx = jax.random.randint(_rng, (len(prev_timestep.state.goal_encoding),), 0, 19)
                    goal_vec = goals[o, c, idx]
                    rng, _rng = jax.random.split(rng)
                    # goal_vec = jnp.eye(16)[g].reshape(g.shape[0], -1)
                    # goal_vec = prev_timestep.state.goal_vec
                    params = policy.cast_to_param(train_state.params)
                    inputs, hidden = policy.cast_to_compute((
                        ({
                            # [batch_size, seq_len=1, ...]
                            "observation": prev_timestep.observation[:, None],
                            "prev_action": prev_action[:, None],
                            "prev_reward": prev_reward[:, None],
                            "goal": goal_vec[:, None]
                        },
                        prev_hstate)
                    ))
                    dist, value, hstate = train_state.apply_fn(
                        params, inputs, hidden
                    )
                    dist, value, hstate = policy.cast_to_output((dist, value, hstate))
                    action, log_prob = dist.sample_and_log_prob(seed=_rng)
                    # squeeze seq_len where possible
                    action, value, log_prob = action.squeeze(1), value.squeeze(1), log_prob.squeeze(1)

                    # STEP ENV
                    timestep = jax.vmap(env.step, in_axes=0)(meta_env_params, prev_timestep, action)
                    transition = Transition(
                        # ATTENTION: done is always false, as we optimize for entire meta-rollout
                        done=jnp.zeros_like(timestep.last()),
                        action=action,
                        value=value,
                        reward=timestep.reward,
                        goal=goal_vec,
                        log_prob=log_prob,
                        obs=prev_timestep.observation,
                        prev_action=prev_action,
                        prev_reward=prev_reward,
                    )
                    runner_state = (rng, train_state, timestep, action, timestep.reward, hstate)
                    return runner_state, transition

                initial_hstate = runner_state[-1]
                # transitions: [seq_len, batch_size, ...]
                runner_state, transitions = jax.lax.scan(_env_step, runner_state, None, config.num_steps_per_update)

                # CALCULATE ADVANTAGE
                rng, train_state, timestep, prev_action, prev_reward, hstate = runner_state
                # calculate value of the last step for bootstrapping
                # g = timestep.state.goal_encoding
                # goal_vec = jnp.eye(16)[g].reshape(g.shape[0], -1)
                # goal_vec = timestep.state.goal_vec
                rng, _rng = jax.random.split(rng)
                o, c = timestep.state.goal_encoding[:, 1], timestep.state.goal_encoding[:, 2]
                idx = jax.random.randint(_rng, (len(timestep.state.goal_encoding),), 0, 19)
                goal_vec = goals[o, c, idx]
                
                params = policy.cast_to_param(train_state.params)
                inputs, hidden = policy.cast_to_compute(
                    ({
                        "observation": timestep.observation[:, None],
                        "prev_action": prev_action[:, None],
                        "prev_reward": prev_reward[:, None],
                        "goal": goal_vec[:, None],
                    },
                    hstate),
                )
                _, last_val, _ = train_state.apply_fn(
                    params, inputs, hidden    
                )
                last_val = policy.cast_to_output(last_val)
                advantages, targets = calculate_gae(transitions, last_val.squeeze(1), config.gamma, config.gae_lambda)

                # UPDATE NETWORK
                def _update_epoch(update_state, _):
                    def _update_minbatch(train_state, batch_info):
                        init_hstate, transitions, advantages, targets = batch_info
                        new_train_state, update_info = ppo_update_networks(
                            train_state=train_state,
                            transitions=transitions,
                            init_hstate=init_hstate.squeeze(1),
                            advantages=advantages,
                            targets=targets,
                            clip_eps=config.clip_eps,
                            vf_coef=config.vf_coef,
                            ent_coef=config.ent_coef,
                            amp_policy=policy,
                            loss_scale=loss_scale,
                            goals=goals, rng=rng
                        )
                        return new_train_state, update_info

                    rng, train_state, init_hstate, transitions, advantages, targets = update_state

                    # MINIBATCHES PREPARATION
                    rng, _rng = jax.random.split(rng)
                    permutation = jax.random.permutation(_rng, config.num_envs_per_device)
                    # [seq_len, batch_size, ...]
                    batch = (init_hstate, transitions, advantages, targets)
                    # [batch_size, seq_len, ...], as our model assumes
                    batch = jtu.tree_map(lambda x: x.swapaxes(0, 1), batch)

                    shuffled_batch = jtu.tree_map(lambda x: jnp.take(x, permutation, axis=0), batch)
                    # [num_minibatches, minibatch_size, ...]
                    minibatches = jtu.tree_map(
                        lambda x: jnp.reshape(x, (config.num_minibatches, -1) + x.shape[1:]), shuffled_batch
                    )
                    train_state, update_info = jax.lax.scan(_update_minbatch, train_state, minibatches)

                    update_state = (rng, train_state, init_hstate, transitions, advantages, targets)
                    return update_state, update_info

                # hstate shape: [seq_len=None, batch_size, num_layers, hidden_dim]
                update_state = (rng, train_state, initial_hstate[None, :], transitions, advantages, targets)
                update_state, loss_info = jax.lax.scan(_update_epoch, update_state, None, config.update_epochs)
                # WARN: do not forget to get updated params
                rng, train_state = update_state[:2]

                # averaging over minibatches then over epochs
                loss_info = jtu.tree_map(lambda x: x.mean(-1).mean(-1), loss_info)
                runner_state = (rng, train_state, timestep, prev_action, prev_reward, hstate)
                return runner_state, loss_info

            # on each meta-update we reset rnn hidden to init_hstate
            runner_state = (rng, train_state, timestep, prev_action, prev_reward, init_hstate)
            runner_state, loss_info = jax.lax.scan(_update_step, runner_state, None, config.num_inner_updates)
            # WARN: do not forget to get updated params
            rng, train_state = runner_state[:2]

            # EVALUATE AGENT
            eval_ruleset_rng, eval_reset_rng = jax.random.split(jax.random.PRNGKey(config.eval_seed))
            eval_ruleset_rng = jax.random.split(eval_ruleset_rng, num=config.eval_num_envs_per_device)
            eval_reset_rng = jax.random.split(eval_reset_rng, num=config.eval_num_envs_per_device)

            eval_ruleset = jax.vmap(benchmark.sample_ruleset)(eval_ruleset_rng)
            eval_env_params = env_params.replace(ruleset=eval_ruleset)

            eval_stats = jax.vmap(rollout, in_axes=(0, None, 0, None, None, None, None, None))(
                eval_reset_rng,
                env,
                eval_env_params,
                train_state,
                # TODO: make this a static method?
                jnp.zeros((1, config.rnn_num_layers, config.rnn_hidden_dim)),
                goals,
                config.eval_num_episodes,
                policy
            )
            eval_stats = jax.lax.pmean(eval_stats, axis_name="devices")
            normed_return = eval_stats.reward / jnp.maximum(eval_stats.dones, 1)
            # averaging over inner updates, adding evaluation metrics
            loss_info = jtu.tree_map(lambda x: x.mean(-1), loss_info)
            info_dict = {
                    "eval/returns_mean": eval_stats.reward.mean(0),
                    "eval/returns_median": jnp.median(eval_stats.reward),
                    "eval/lengths": eval_stats.length.mean(0),
                    "eval/normalized_return_mean": normed_return.mean(0),
                    "eval/normalized_return_median": jnp.median(normed_return),
                    "eval/normalized_return_20percentile": jnp.percentile(normed_return, q=20),
                    "eval/dones_mean": eval_stats.dones.mean(0),
                    "eval/dones_median": jnp.median(eval_stats.dones),
                    "eval/dones_20percentile": jnp.percentile(eval_stats.dones, q=20),
                    "eval/lengths_20percentile": jnp.percentile(eval_stats.length, q=20),
                    "eval/returns_20percentile": jnp.percentile(eval_stats.reward, q=20),
                    "lr": train_state.opt_state[-1].hyperparams["learning_rate"],
                }
            loss_info.update(
                info_dict 
            )
            # def mycb(x):
            #     wandb.log(x)
            # jax.experimental.io_callback(mycb, None, info_dict)
            meta_state = (rng, train_state)
            return meta_state, loss_info

        meta_state = (rng, train_state)
        meta_state, loss_info = _meta_step(meta_state)
        return {"state": meta_state[-1], "loss_info": loss_info}

    return train


@pyrallis.wrap()
def train(config: TrainConfig):
    # logging to wandb
    run = wandb.init(
        project=config.project,
        group=config.group,
        name=config.name,
        config=asdict(config),
        save_code=True,
    )
    # removing existing checkpoints if any
    # if config.checkpoint_path is not None and os.path.exists(config.checkpoint_path):
    #     shutil.rmtree(config.checkpoint_path)

    rng, env, env_params, benchmark, init_hstate, train_state, policy, loss_scale, goals = make_states(config)
    # replicating args across devices
    rng = jax.random.split(rng, num=jax.local_device_count())
    train_state = replicate(train_state, jax.local_devices())
    goals = replicate(goals, jax.local_devices())
    init_hstate = replicate(init_hstate, jax.local_devices())

    print("Compiling...")
    t = time.time()
    train_fn = make_train(env, env_params, benchmark, config, policy, loss_scale)
    train_fn = train_fn.lower(rng, train_state, init_hstate, goals).compile()
    elapsed_time = time.time() - t
    print(f"Done in {elapsed_time:.2f}s.")

    print("Training...")
    d = time.time()
    total_transitions = 0
    for i in range(config.num_meta_updates):
        t = time.time()
        train_info = jax.block_until_ready(train_fn(rng, train_state, init_hstate, goals))
        # with jax.profiler.trace("/Tmp/slurm.4253589.0/jax-trace1", create_perfetto_link=True):
        #     train_info = jax.block_until_ready(train_fn(rng, train_state, init_hstate, policy))
        curr = config.num_steps_per_env * config.num_envs_per_device * jax.local_device_count()
        total_transitions += curr
        train_state = train_info['state']
        loss_info = train_info['loss_info']
        info = jtu.tree_map(lambda x: x.item(), unreplicate(loss_info))
        info["transitions"] = total_transitions
        info['FPS'] = curr / (time.time() - t)
        wandb.log(info)
        checkpoints.save_checkpoint(ckpt_dir=os.path.abspath(config.checkpoint_path),
                            target=unreplicate(train_info)["state"],
                            step=0,
                            overwrite=True,
                            keep=1)
    elapsed_time = time.time() - d
    print(f"Done in {elapsed_time:.2f}s.")
    run.finish()


if __name__ == "__main__":
    train()
