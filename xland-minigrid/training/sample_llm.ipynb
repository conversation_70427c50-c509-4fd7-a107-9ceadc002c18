{"cells": [{"cell_type": "code", "execution_count": 1, "id": "be1641a2-a169-46b7-b64e-cf47ff214729", "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ['XLA_PYTHON_CLIENT_PREALLOCATE'] = 'false'"]}, {"cell_type": "code", "execution_count": 2, "id": "cae51990-9238-4c13-9973-f324b43a1287", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/network/scratch/a/artem.zholus/conda_envs/rl_jax_torch/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "2024-03-13 19:06:05.596606: W external/xla/xla/service/gpu/nvptx_compiler.cc:742] The NVIDIA driver's CUDA version is 12.2 which is older than the ptxas CUDA version (12.4.99). Because the driver is older than the ptxas version, XLA is disabling parallel compilation, which may slow down compilation. You should update your NVIDIA driver or use the NVIDIA-provided CUDA forward compatibility packages.\n", "WARNING:absl:Tensorflow library not found, tensorflow.io.gfile operations will use native shim calls. GCS paths (i.e. 'gs://...') cannot be accessed.\n"]}], "source": ["import numpy as np\n", "import math\n", "from tqdm import tqdm\n", "import jax\n", "from jax import numpy as jnp\n", "import torch\n", "from transformers import AutoTokenizer, BertForMaskedLM, BertForPreTraining\n", "\n", "from xminigrid.benchmarks import load_bz2_pickle\n", "import xminigrid\n", "from train_meta_task import my_load_benchmark\n", "from rich import print"]}, {"cell_type": "code", "execution_count": 3, "id": "79c0ed99-3854-424d-b6ff-48dc1dd12bba", "metadata": {}, "outputs": [], "source": ["env, env_params = xminigrid.make(\"XLand-MiniGrid-R4-9x9\")\n", "benchmark = my_load_benchmark(\"my_trivial_1m_v2\")"]}, {"cell_type": "code", "execution_count": 53, "id": "9f4dd7a8-de4c-40f0-974d-395431ab3d5a", "metadata": {}, "outputs": [], "source": ["rng = jax.random.<PERSON><PERSON><PERSON><PERSON>(42)\n", "ruleset = benchmark.get_ruleset(1)\n", "env_params = env_params.replace(ruleset=ruleset)\n", "timestep = env.reset(env_params, rng)"]}, {"cell_type": "code", "execution_count": 54, "id": "bf3c64db-5ae7-48de-a615-d3c2db5a97df", "metadata": {}, "outputs": [], "source": ["goal_map = {\n", "    5: 'ball',\n", "    6: 'square',\n", "    7: 'pyramid',\n", "    8: 'goal',\n", "    9: 'key',\n", "    10: 'locked door',\n", "    11: 'closed door',\n", "    12: 'open door',\n", "    13: 'hex',\n", "    14: 'star'\n", "}\n", "\n", "color_map = {\n", "    3: 'red',\n", "    4: 'green',\n", "    5: 'blue',\n", "    6: 'purple',\n", "    7: 'yellow',\n", "    8: 'grey',\n", "    9: 'black',\n", "    10: 'orange',\n", "    11: 'white',\n", "    12: 'brown',\n", "    13: 'pink'\n", "}"]}, {"cell_type": "code", "execution_count": 55, "id": "ec005965-0d02-4530-a66f-c936ed37d629", "metadata": {}, "outputs": [], "source": ["# print(env.render(env_params.replace(render_mode='rich_text'), timestep))"]}, {"cell_type": "code", "execution_count": 56, "id": "8b72f979-5779-402c-b959-008fd283e5d2", "metadata": {}, "outputs": [], "source": ["c = ((timestep.state.grid[..., 0] != 4) & (timestep.state.grid[..., 0] != 3) &\n", "     (timestep.state.grid[..., 0] != 11) & (timestep.state.grid[..., 0] != 10) &\n", "     (timestep.state.grid[..., 1] != 8) & (timestep.state.grid[..., 1] != 9)).nonzero()"]}, {"cell_type": "code", "execution_count": null, "id": "fe9483dc-b6d0-4d97-aa41-707db14dcc5b", "metadata": {"scrolled": true}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 57, "id": "4f8c4579-ce2c-4aad-a8dc-24020a2cadbb", "metadata": {}, "outputs": [], "source": ["info = []\n", "agent_pos = np.array(timestep.state.agent.position)\n", "for o, x, y in zip(np.array(timestep.state.grid[c]), *c):\n", "    info.append((goal_map[o[0]], color_map[o[1]], x.item(), y.item()))"]}, {"cell_type": "code", "execution_count": 58, "id": "0cc4bee9-c292-4c74-97b0-8c23529ad761", "metadata": {}, "outputs": [{"data": {"text/plain": ["[('square', 'yellow', 2, 5), ('hex', 'blue', 2, 7), ('star', 'purple', 6, 2)]"]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["info"]}, {"cell_type": "code", "execution_count": 59, "id": "1a33ca8d-e10b-48ee-925f-373a052ec52f", "metadata": {}, "outputs": [{"data": {"text/plain": ["Array([6, 7], dtype=uint8)"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["ruleset.goal[1:3]"]}, {"cell_type": "code", "execution_count": 100, "id": "947983b4-1d89-4ddb-94eb-11dd77eaf88c", "metadata": {"scrolled": true}, "outputs": [], "source": ["prompt = (f\"You are in the 4 rooms flat\" +\n", "          f\" gridworld with 2 column and 2 rows of rooms. \" +\n", "          # f\"Your position within the grid is ({agent_pos[0]}, {agent_pos[1]}).\" +\n", "          \" Adjacent rooms are connected with doors. \" +\n", "          \"Each room is 3 cells wide and 3 cells high. \") + \\\n", "          ''.join([f'The {col} {obj} is in the grid. ' for obj, col, x, y in info])"]}, {"cell_type": "code", "execution_count": 101, "id": "3370e16b-857b-4700-a04f-9818d8a43190", "metadata": {}, "outputs": [], "source": ["gobj, gcol = ruleset.goal[1:3]\n", "prompt = f'The gridworld environment desciprion: \\n{prompt}\\n' \\\n", "         f'Goal of the agent: Go to the {color_map[gcol.item()]} {goal_map[gobj.item()]}.\\n' \\\n", "         f'Break down the overall goal into short high-level step-by-step ' \\\n", "         f'instruction for the agent in the gridworld environment.\\n' \\\n", "         f'Instruction step 1: '"]}, {"cell_type": "code", "execution_count": 102, "id": "0acb715e-1659-43c3-a073-b5cd8d5b1fd9", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">The gridworld environment desciprion: \n", "You are in the <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4</span> rooms flat gridworld with <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span> column and <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span> rows of rooms.  Adjacent rooms are connected with doors. \n", "Each room is <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span> cells wide and <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span> cells high. The yellow square is in the grid. The blue hex is in the grid. The \n", "purple star is in the grid. \n", "Goal of the agent: Go to the yellow square.\n", "Break down the overall goal into short high-level step-by-step instruction for the agent in the gridworld \n", "environment.\n", "Instruction step <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>: \n", "</pre>\n"], "text/plain": ["The gridworld environment desciprion: \n", "You are in the \u001b[1;36m4\u001b[0m rooms flat gridworld with \u001b[1;36m2\u001b[0m column and \u001b[1;36m2\u001b[0m rows of rooms.  Adjacent rooms are connected with doors. \n", "Each room is \u001b[1;36m3\u001b[0m cells wide and \u001b[1;36m3\u001b[0m cells high. The yellow square is in the grid. The blue hex is in the grid. The \n", "purple star is in the grid. \n", "Goal of the agent: Go to the yellow square.\n", "Break down the overall goal into short high-level step-by-step instruction for the agent in the gridworld \n", "environment.\n", "Instruction step \u001b[1;36m1\u001b[0m: \n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(prompt)"]}, {"cell_type": "code", "execution_count": 63, "id": "2fa7e9da-4573-44d6-ba09-2006be2d9d86", "metadata": {}, "outputs": [], "source": ["from transformers import AutoTokenizer, FlaxT5ForConditionalGeneration, T5ForConditionalGeneration, \\\n", "    FlaxGemmaForCausalLM, GemmaForCausalLM \n", "import os"]}, {"cell_type": "code", "execution_count": 64, "id": "34b4868c-29c1-47e6-bfe6-d1a06e08318f", "metadata": {}, "outputs": [{"data": {"text/plain": ["'/network/scratch/a/artem.zholus'"]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["os.environ['SCRATCH']"]}, {"cell_type": "code", "execution_count": 92, "id": "f24fa2f2-590b-4685-ad9d-723da3b8c08e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Loading checkpoint shards: 100%|████████████████████████████████████████████████████████████████████| 2/2 [00:03<00:00,  1.94s/it]\n"]}], "source": ["tokenizer = AutoTokenizer.from_pretrained(\"google/gemma-2b-it\", cache_dir=os.environ['SCRATCH'] + '/hf')\n", "model = GemmaForCausalLM.from_pretrained(\"google/gemma-2b-it\", cache_dir=os.environ['SCRATCH'] + '/hf').cuda()"]}, {"cell_type": "code", "execution_count": 72, "id": "82f72d99-7d13-4e48-b31f-5665212d0cfd", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Downloading shards: 100%|███████████████████████████████████████████████████████████████████████████| 2/2 [00:55<00:00, 27.86s/it]\n", "Loading checkpoint shards: 100%|████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.66s/it]\n"]}], "source": ["# tokenizer = AutoTokenizer.from_pretrained(\"google/flan-t5-xl\", cache_dir=os.environ['SCRATCH'] + '/hf')\n", "# model = T5ForConditionalGeneration.from_pretrained(\"google/flan-t5-xl\", \n", "#                                                    cache_dir=os.environ['SCRATCH'] + '/hf').cuda()"]}, {"cell_type": "code", "execution_count": 73, "id": "a9d4bed4-3ee5-4203-8c87-2d86ed14eb7c", "metadata": {}, "outputs": [], "source": ["import torch"]}, {"cell_type": "code", "execution_count": 74, "id": "126501ab-c3dd-46dd-b435-53d554a48d03", "metadata": {}, "outputs": [], "source": ["torch.cuda.empty_cache()"]}, {"cell_type": "code", "execution_count": 103, "id": "a656d7c8-97c6-4b4d-ab75-7ed5754379ae", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">The gridworld environment desciprion: \n", "You are in the <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4</span> rooms flat gridworld with <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span> column and <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span> rows of rooms.  Adjacent rooms are connected with doors. \n", "Each room is <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span> cells wide and <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span> cells high. The yellow square is in the grid. The blue hex is in the grid. The \n", "purple star is in the grid. \n", "Goal of the agent: Go to the yellow square.\n", "Break down the overall goal into short high-level step-by-step instruction for the agent in the gridworld \n", "environment.\n", "Instruction step <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>: \n", "</pre>\n"], "text/plain": ["The gridworld environment desciprion: \n", "You are in the \u001b[1;36m4\u001b[0m rooms flat gridworld with \u001b[1;36m2\u001b[0m column and \u001b[1;36m2\u001b[0m rows of rooms.  Adjacent rooms are connected with doors. \n", "Each room is \u001b[1;36m3\u001b[0m cells wide and \u001b[1;36m3\u001b[0m cells high. The yellow square is in the grid. The blue hex is in the grid. The \n", "purple star is in the grid. \n", "Goal of the agent: Go to the yellow square.\n", "Break down the overall goal into short high-level step-by-step instruction for the agent in the gridworld \n", "environment.\n", "Instruction step \u001b[1;36m1\u001b[0m: \n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(prompt)"]}, {"cell_type": "code", "execution_count": 124, "id": "93cae55d-2b06-4f65-bc97-98b3745641fb", "metadata": {}, "outputs": [], "source": ["# with torch.amp.autocast('cuda', dtype=torch.float16):\n", "inputs = tokenizer([prompt], return_tensors=\"pt\")\n", "gen_ids = model.generate(inputs[\"input_ids\"].cuda(), do_sample=True, \n", "                      max_new_tokens=20, top_k=0, temperature=1.4, num_return_sequences=20)\n", "texts = tokenizer.batch_decode(gen_ids[:, inputs[\"input_ids\"].shape[1]:], \n", "                               skip_special_tokens=True, clean_up_tokenization_spaces=False)"]}, {"cell_type": "code", "execution_count": 125, "id": "d41ce34f-2d96-47da-8dff-43b3ef43d5ee", "metadata": {}, "outputs": [{"data": {"text/plain": ["['\\nGo to cell B3.\\nHow do you go from one cell to another and what will',\n", " '\\nMove to cell (2, 1) \\nCreate a set of possible next movements the',\n", " '\\n- Locate the yellow square from the initial position. \\n\\nHow can the agent approach the right',\n", " '\\nHead east. \\n**Understood steps: Head right, where will you look for the door',\n", " '\\n- Start in any arbitrary room in the grid.\\n- Examine the current room and find the',\n", " '\\nKnow where your starting location is\\n\\n\\nAs in the environment, you are currently in room 1',\n", " '\\n* Move one cell downward.\\n* Get obstacle information in the current room by checking the bottom',\n", " '\\n**Cell Selection and Entry:**\\n- Determine the best cell in row 1 and column ',\n", " ' imagine what isрисо .... (outer level - once the grid becomes more familiar).\\n\\n\\n**Step ',\n", " '\\n- reach 2-3 rooms down from the grid initial position.\\n\\n\\nInstruction step 2',\n", " '\\nWhat is your current position?\\nAnswer: Your current position is in the bottom left cell (',\n", " '\\n* Start at the position [4, 1].\\n* Move right 2 steps.',\n", " '\\n- Go from room 1 to room 2.\\n\\nInvestigate nearby rooms, and reach',\n", " '\\n<PERSON><PERSON><PERSON> (ActionListener) Mysterді\\n\\nRecall: \\n1. Move to the right when',\n", " '\\n\\n\\nFollow Green Door.\\n\\n\\nThe agent moves along the vertical white space between Horizontal and Vertical corridors till',\n", " \"\\nIdentify the north, west, and south's room numbers using the A, B, and\",\n", " '\\nThe agent should move from the grid.\\n\\n\\nInstruction step 2:\\nOnce in the grid',\n", " '\\nMove down from the current room.\\n\\n\\nInstruction step 2:\\nGo left through a neighbor',\n", " '\\nFrom starting position move to the south-west neighbor of #R1C1.\\n\\nInstruction',\n", " \"\\nNavigate from the current cell to the yellow square.\\n\\n\\nRemember, it's not necessary to\"]"]}, "execution_count": 125, "metadata": {}, "output_type": "execute_result"}], "source": ["texts"]}, {"cell_type": "code", "execution_count": null, "id": "50b61284-e516-4ef1-b24d-1a4d4f68db02", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}