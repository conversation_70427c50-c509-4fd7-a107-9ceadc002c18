import numpy as np
import math
from tqdm import tqdm
import torch
from transformers import <PERSON>Tokenizer, BertForMaskedLM, BertForPreTraining

from src.xminigrid.benchmarks import load_bz2_pickle

goal_map = {
    5: 'ball',
    6: 'square',
    7: 'pyramid',
    8: 'goal',
    9: 'key',
    10: 'locked door',
    11: 'closed door',
    12: 'open door',
    13: 'hex',
    14: 'star'
}

color_map = {
    3: 'red',
    4: 'green',
    5: 'blue',
    6: 'purple',
    7: 'yellow',
    8: 'grey',
    9: 'black',
    10: 'orange',
    11: 'white',
    12: 'brown',
    13: 'pink'
}


tokenizer = AutoTokenizer.from_pretrained("google-bert/bert-base-uncased")
instruction_backbone = BertForPreTraining.from_pretrained("google-bert/bert-base-uncased").cuda()

bc = load_bz2_pickle('my_trivial_1m_v2')
batch = 100
goals_total = bc["goals"]
steps = int(math.ceil(len(goals_total) / batch))
result = []

patterns = [
    'Go to {color} {object}',
    'Fing the {color} {object}',
    'Seek out the {color} {object}',
    'Get to the {color} {object}',
    'Proceed towards the {color} {object}',
    'Navigate to the {color} {object}',
    'Reach for the {color} {object}',
    'Head towards the {color} {object}',
    'Seek out the {object} that is of the desired color - {color}.',
    'Seek out the {object} that is of the specified color - {color}.',
    'Secure the {object} of {color} origin',
    'Procure the {object} corresponding to the {color}',
    'Obtain the {object} identified by {color}',
    'Discover and grasp the {object} with {color} attributes',
    'Locate and obtain the {color} {object} via manipulation',
    'Attain the {color} {object} using reaching actions',
    'Find and retrieve the {color} {object} through interaction',
    'Acquire the {color} {object} by reaching and grasping',
    'Obtain the {object} associated with {color}'
]
def iterator():
    for i, k in enumerate(goal_map):
        for j, c in enumerate(color_map):
            for t, pattern in enumerate(patterns):
                yield (i,j,t), pattern.format(color=c, object=k)
texts = [c for c in iterator()]
addrs = [c[0] for c in texts]
texts = [c[1] for c in texts]

for j in tqdm(range(int(math.ceil(len(texts) / batch)))):
    txt_batch = texts[j * batch : (j + 1) * batch]
    addr_batch = addrs[j * batch : (j + 1) * batch]


    ids = tokenizer(txt_batch, padding='longest')
    ids = {k: torch.from_numpy(np.array(v)).to(instruction_backbone.device) 
       for k, v in ids.items()}
    # text_features [batch, dim]
    with torch.no_grad(), torch.amp.autocast(device_type='cuda', dtype=torch.float16):
        outs = instruction_backbone(**ids, output_hidden_states=True)
        text_features = outs.hidden_states[-1][:, 0] # [batch, length, dim]        
    result.append(text_features.detach().float().cpu().numpy())

# for j in tqdm(range(steps)):
#     curr = goals_total[j * batch : (j + 1) * batch]
#     txt_batch = []
#     for k in range(batch):
#        color = color_map[curr[k][2].item()]
#        object = goal_map[curr[k][1].item()]
       
#        txt_batch.append(prompt)


#     ids = tokenizer(txt_batch, padding='longest')
#     ids = {k: torch.from_numpy(np.array(v)).to(instruction_backbone.device) 
#        for k, v in ids.items()}
#     # text_features [batch, dim]
#     with torch.no_grad(), torch.amp.autocast(device_type='cuda', dtype=torch.float16):
#         outs = instruction_backbone(**ids, output_hidden_states=True)
#         text_features = outs.hidden_states[-1][:, 0] # [batch, length, dim]        
#     result.append(text_features.detach().float().cpu().numpy())
print('concat...')
result = np.concatenate(result, axis=0).reshape((len(goal_map), len(color_map), len(patterns), 768))
np.save('/network/scratch/a/artem.zholus/bert_features_rephrase.npy', result)

