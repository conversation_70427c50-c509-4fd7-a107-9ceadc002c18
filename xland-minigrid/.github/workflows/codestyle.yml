name: codestyle check
on:
  push:
    branches:
      - dev
      - main
  pull_request:
    branches:
      - dev
      - main

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python 3.10
        uses: actions/setup-python@v4
        with:
          python-version: "3.10"
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -e ".[dev]"
      - name: check codestyle
        run: |
          ruff --config pyproject.toml --diff .