#!/bin/bash
set -e
# define CLUSTER variable: mila | cedar | narval
export CLUSTER=$(cat /etc/slurm/slurm.conf | grep ClusterName | sed 's/.*=//g')
export ARR=0
if [[ -z $EXPID ]]; then
    export EXPID=$(uuidgen)
    export ARR=2
fi
if [[ ! -z $NSEEDS ]]; then
    export ARR=$NSEEDS
fi
if [[ -z $CONSTRAINT ]]; then
    export CONSTRAINT="32gb|40gb|48gb|80gb"
fi
if [[ -z $SLURM_TIME ]]; then
    export SLURM_TIME=--time=0-12:00
fi
if [[ -z $SLURM_CPU ]]; then
    #export SLURM_CPU=--cpus-per-task=8
    export SLURM_CPU=--cpus-per-task=40
fi
if [[ -z $SLURM_GPU ]]; then
    # export SLURM_GPU=--gres=gpu:$N_GPUS
    export SLURM_GPU=--gpus-per-task=$N_GPUS
fi
if [[ -z $SLURM_MEM ]]; then
    export SLURM_MEM=--mem=256G
fi
if [[ -z $SLURM_TASKS ]]; then
    export SLURM_TASKS=--ntasks-per-node=1
    # export SLURM_TASKS=--ntasks-per-node=$N_GPUS
fi
if [[ -z $SLURM_NODES ]]; then
    export SLURM_NODES=--nodes=$N_NODES
fi
if [[ -z $SLURM_PARTITION ]]; then
    export SLURM_PARTITION=--partition=$PARTITION
fi
# if [[ -z $CONSTRAINT ]]; then
#     export CONSTRAINT="32gb|40gb|48gb|80gb"
# fi
export PROJECT=lg
export RUN_DIR=$SCRATCH/"$PROJECT"_files/$EXPID
echo "copying stuff into: $RUN_DIR"
mkdir -p $RUN_DIR
export USER_ARGS="$@"
cp -r OpenRLHF xland-minigrid .netrc scripts $RUN_DIR/
echo Job configuration:
echo $SLURM_TIME \
    $SLURM_CPU \
    $SLURM_GPU \
    $SLURM_TASKS \
    $SLURM_NODES \
    $SLURM_MEM \
    $SLURM_PARTITION
echo Running script:
echo $USER_ARGS
pushd $RUN_DIR \
  && sbatch \
        --array=0-$ARR \
        --output "$RUN_DIR/output-%j.out" \
        --error "$RUN_DIR/output-%j.out" \
	--constraint="40gb|80gb" \
        $SLURM_TIME $SLURM_CPU $SLURM_GPU \
        $SLURM_TASKS $SLURM_NODES $SLURM_MEM \
        $SLURM_PARTITION \
        scripts/run_$CLUSTER.sh "$@" \
  && popd
