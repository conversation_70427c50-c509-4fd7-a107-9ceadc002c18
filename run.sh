#!/bin/bash
export N_GPUS=4
export N_NODES=2
export NSEEDS=0
export SLURM_CPU=--cpus-per-task=40
export SLURM_TIME=--time=2-00:00
export SLURM_MEM=--mem=256G
export PARTITION="lab-chandar"

# For 8 x 40gb gpus and 2b model
# --micro_train_batch_size 8
# --micro_rollout_batch_size 8
./scripts/spawn.sh train_ppo.py \
    --pretrain google/gemma-2b-it \
    --reward_pretrain google/gemma-2b-it \
    --save_steps -1 \
    --logging_steps 1 \
    --eval_steps -1 \
    --micro_train_batch_size 4 \
    --train_batch_size 1024 \
    --micro_rollout_batch_size 8 \
    --rollout_batch_size 1024 \
    --max_epochs 2 \
    --prompt_max_len 256 \
    --generate_max_len 256 \
    --zero_stage 2 \
    --bf16 \
    --actor_learning_rate 5e-7 \
    --critic_learning_rate 9e-6 \
    --init_kl_coef 0.01 \
    --max_samples 80000 \
    --normalize_reward \
    --actor_init_on_gpu \
    --adam_offload \
    --flash_attn \
    --gradient_checkpointing \
    --use_wandb True
