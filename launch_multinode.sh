#!/bin/bash
#SBATCH --nodes=2
#SBATCH --ntasks-per-node=1
#SBATCH --gpus-per-task=4
#SBATCH --cpus-per-task=48
#SBATCH --mem=256G
#SBATCH --time=0-01:00
#SBATCH --partition=long

export MASTER_PORT=$(expr 10000 + $(echo -n $SLURM_JOBID | tail -c 4))
export MASTER_ADDR=$(scontrol show hostnames $SLURM_JOB_NODELIST | head -n 1)
module load singularity
# module load openmpi/4.0.4
# module load cuda/12.1.1
#srun --ntasks=$SLURM_JOB_NUM_NODES --ntasks-per-node=1 singularity build --sandbox $SLURM_TMPDIR/image $SCRATCH/img_latest.sif
export XLA_PYTHON_CLIENT_PREALLOCATE=false
singularity shell --nv -B $SCRATCH/hf:/home/<USER>/hf:rw --cleanenv -H $(pwd) --env SCRATCH=/home/<USER>/hf $SCRATCH/lg2_latest.sif
cd OpenRLHF/examples && python \
    train_ppo.py \
    --pretrain google/gemma-2b-it \
    --reward_pretrain google/gemma-2b-it \
    --save_steps -1 \
    --logging_steps 1 \
    --eval_steps -1 \
    --micro_train_batch_size 2 \
    --train_batch_size 128 \
    --micro_rollout_batch_size 4 \
    --rollout_batch_size 256 \
    --max_epochs 1 \
    --prompt_max_len 256 \
    --generate_max_len 1024 \
    --zero_stage 2 \
    --bf16 \
    --actor_learning_rate 5e-7 \
    --lora_rank 1 \
    --critic_learning_rate 9e-6 \
    --init_kl_coef 0.01 \
    --max_samples 80000 \
    --normalize_reward \
    --actor_init_on_gpu \
    --adam_offload \
    --flash_attn \
    --gradient_checkpointing
#    --env TORCH_DISTRIBUTED_DEBUG=DETAIL \
#    --env NCCL_DEBUG=INFO \
#    --env TORCH_CPP_LOG_LEVEL=INFO \
#srun --ntasks=$SLURM_JOB_NUM_NODES --ntasks-per-node=1 rsync -r --progress $SCRATCH/data $SLURM_TMPDIR
# srun --ntasks=$SLURM_JOB_NUM_NODES --ntasks-per-node=1 \
#     singularity exec --nv \
#     -H $SCRATCH/tfm0:/home/<USER>
#     -B $SCRATCH/data:/home/<USER>/data \
#     -B $SCRATCH/mol_models:/home/<USER>/models \
#     --env WANDB_DIR=/tmp/ \
#     --env HYDRA_FULL_ERROR=1 \
#     --env TOKENIZERS_PARALLELISM=true \
#     --env HOME=/home \
#     $SCRATCH/img_latest.sif ./myrun.sh
